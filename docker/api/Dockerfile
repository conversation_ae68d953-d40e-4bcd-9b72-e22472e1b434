FROM python:3.13-slim

ENV USER=appuser \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    UV_PROJECT_ENVIRONMENT=/usr/local

RUN apt-get update && apt-get install --no-install-recommends -y curl gcc python3-dev musl-dev  \
    build-essential libpq-dev libmagic1 libffi-dev git netcat-traditional wkhtmltopdf \
    default-libmysqlclient-dev pkg-config \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && useradd -m -s /bin/bash $USER

COPY --from=ghcr.io/astral-sh/uv:0.5.5 /uv /uvx /bin/

ENV APP_DIR=/app
WORKDIR $APP_DIR

COPY pyproject.toml pyproject.toml
COPY uv.lock uv.lock

RUN --mount=type=cache,target=/home/<USER>/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project

COPY app $APP_DIR

RUN --mount=type=cache,target=/home/<USER>/.cache/uv \
    uv sync --frozen

ENV PYTHONPATH=$APP_DIR

RUN chown -R "$USER":"$USER" $APP_DIR
USER $USER

# Copy the entrypoint script and make it executable
COPY ./docker/api/entrypoint.sh /entrypoint.sh
# RUN chmod +x  /entrypoint.sh

COPY ./setup.sh /setup.sh
# RUN chmod +x /setup.sh

# Command for running the app in production (via entrypoint.sh)
ENTRYPOINT ["/entrypoint.sh"]

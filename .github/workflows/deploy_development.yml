name: Deploy Development

on:
  push:
    branches: [dev]

permissions:
  contents: read
  security-events: write

jobs:

  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Extract Repository Name
        run: echo "REPO_NAME=${GITHUB_REPOSITORY#*/}" >> $GITHUB_ENV

      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@v5
        with:
          args: >-
            -Dsonar.projectKey=${{ env.REPO_NAME }}-dev
            -Dsonar.verbose=false
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: SonarQube Quality Gate check (non-blocking)
        continue-on-error: true
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Build Docker image app
        run: |
          IMAGE=registry.digitalocean.com/capitalsagecr/${{ env.REPO_NAME }}-dev
          docker build -f docker/api/Dockerfile -t ${IMAGE}:${{ github.sha }} .

      - name: Install Trivy
        run: |
          sudo apt-get update -y
          sudo apt-get install -y wget apt-transport-https gnupg lsb-release
          wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
          echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -cs) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
          sudo apt-get update -y
          sudo apt-get install -y trivy

      - name: Trivy Image Scan
        run: |
          IMAGE_BUILD=registry.digitalocean.com/capitalsagecr/${{ env.REPO_NAME }}-dev:${{ github.sha }}
          trivy image --format sarif --output trivy-results.sarif --exit-code 0 --severity CRITICAL,HIGH ${IMAGE_BUILD}

      - name: Upload Trivy Scan Report as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: trivy-image-report-${{ env.REPO_NAME }}-dev-${{ github.sha }}
          path: trivy-results.sarif

  deploy:
    needs: build
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Get env from AWS Secrets Manager
        run: |
          SECRET_NAME="dev/sagecloud-core-api"

          echo "Fetching secrets from AWS Secrets Manager..."

          aws secretsmanager get-secret-value \
            --secret-id $SECRET_NAME \
            --region eu-west-1 \
            --query SecretString \
            --output text | jq -r 'to_entries|map("\(.key)=\(.value|tostring)")|.[]' > .env

          echo ".env file created with secrets:"
          # cat .env
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.DEVOPS_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.DEVOPS_AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ secrets.DEVOPS_AWS_REGION }}

      - name: Update Environment variables
        run: |
          echo "${{ secrets.DEV_SSHKEY }}" > id_rsa
          chmod 600 id_rsa
          scp -P ${{ secrets.PORT }} -i ./id_rsa -o StrictHostKeyChecking=no .env ${{ secrets.DEV_USERNAME }}@${{ secrets.DEV_HOST }}:${{ secrets.DEV_PATH }}/.env

      - name: Push to server and deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEV_HOST }}
          username: ${{ secrets.DEV_USERNAME }}
          port: ${{ secrets.PORT }}
          key: ${{ secrets.DEV_SSHKEY }}
          script: |
            cd ${{ secrets.DEV_PATH }} && ls && git pull && docker compose up --build -d

  notify:
    needs: [build, deploy]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify Teams of Workflow Result
        env:
          TEAMS_WEBHOOK_URL: ${{ secrets.APM_TEAMS_WEBHOOK_URL }}
        run: |
          if [[ "${{ needs.build.result }}" == "success" && "${{ needs.deploy.result }}" == "success" ]]; then
            STATUS="✅ Deployment succeeded for ${{ github.repository }}<br>Branch: ${{ github.ref_name }}<br>Engineer: ${{ github.actor }}<br>Commit: ${{ github.sha }}"
            COLOR="00FF00"
          else
            STATUS="❌ Deployment failed for ${{ github.repository }}<br>Branch: ${{ github.ref_name }}<br>Engineer: ${{ github.actor }}<br>Commit: ${{ github.sha }}"
            COLOR="FF0000"
          fi

          PAYLOAD=$(cat <<EOF
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "summary": "GitHub Actions Notification",
            "themeColor": "$COLOR",
            "title": "GitHub Actions Notification",
            "text": "$STATUS"
          }
          EOF
          )

          curl -H "Content-Type: application/json" -d "$PAYLOAD" "$TEAMS_WEBHOOK_URL"

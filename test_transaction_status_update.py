#!/usr/bin/env python3
"""
Test script to verify the transaction status update endpoint is working correctly.
"""


import requests

# Configuration
BASE_URL = "http://localhost:47001"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "testpassword123"
TRANSACTION_ID = "a1lt9eqikrbwnql7m4x8sd2c"
TRANSACTION_REFERENCE = "TXN_20250613_0005"


def get_auth_token():
    """Get JWT token for admin user"""
    login_url = f"{BASE_URL}/api/v1/auth/login/"
    login_data = {"email": ADMIN_EMAIL, "password": ADMIN_PASSWORD}

    print(f" Attempting to login as {ADMIN_EMAIL}...")
    response = requests.post(login_url, json=login_data)

    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data.get("access")
        print(" Login successful!")
        return access_token
    else:
        print(f" Login failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None


def get_transaction_status(token, identifier):
    """Get current transaction status"""
    url = f"{BASE_URL}/api/v1/transaction/{identifier}/"
    headers = {"Authorization": f"Bearer {token}"}

    print(f" Getting current status for transaction {identifier}...")
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        status = data.get("status")
        print(f" Current status: {status}")
        return status
    else:
        print(f"Failed to get transaction: {response.status_code}")
        print(f"Response: {response.text}")
        return None


def update_transaction_status(token, identifier, new_status):
    """Update transaction status"""
    url = f"{BASE_URL}/api/v1/transaction/{identifier}/update-status/"
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    data = {"status": new_status}

    print(f" Updating transaction {identifier} status to {new_status}...")
    response = requests.post(url, headers=headers, json=data)

    print(f"Response Status: {response.status_code}")
    print(f"Response Body: {response.text}")

    if response.status_code == 200:
        print("Status update request successful!")
        return True
    else:
        print(f" Status update failed: {response.status_code}")
        return False


def main():
    print(" Testing Transaction Status Update Endpoint")
    print("=" * 50)

    # Step 1: Get authentication token
    token = get_auth_token()
    if not token:
        print(" Cannot proceed without authentication token")
        return

    # Step 2: Get current transaction status
    current_status = get_transaction_status(token, TRANSACTION_REFERENCE)
    if not current_status:
        print(" Cannot proceed without current status")
        return

    # Step 3: Determine new status (toggle between PENDING and FAILED)
    if current_status == "PENDING":
        new_status = "FAILED"
    elif current_status == "FAILED":
        new_status = "SUCCESSFUL"
    else:
        new_status = "PENDING"

    print(f" Will change status from {current_status} to {new_status}")

    # Step 4: Update the status
    success = update_transaction_status(token, TRANSACTION_REFERENCE, new_status)

    if success:
        # Step 5: Verify the change immediately
        print("\n Verifying the status change immediately...")
        updated_status = get_transaction_status(token, TRANSACTION_REFERENCE)

        if updated_status == new_status:
            print(f" SUCCESS! Status successfully changed to {updated_status}")

            # Step 6: Wait a moment and check again to ensure persistence
            print("\n Waiting 2 seconds and checking again...")
            import time

            time.sleep(2)

            final_status = get_transaction_status(token, TRANSACTION_REFERENCE)
            if final_status == new_status:
                print(f" PERSISTENCE CHECK PASSED! Status is still {final_status}")
            else:
                print(
                    f" PERSISTENCE CHECK FAILED! Status changed from {new_status} to {final_status}"
                )
                print("This suggests the status update is being reverted somehow!")

            # Step 7: Try fetching with different identifier (ID instead of reference)
            print(f"\n Double-checking with transaction ID {TRANSACTION_ID}...")
            id_status = get_transaction_status(token, TRANSACTION_ID)
            if id_status == new_status:
                print(f" ID FETCH CONFIRMED! Status is {id_status}")
            else:
                print(f" ID FETCH MISMATCH! Expected {new_status}, got {id_status}")
        else:
            print(f"FAILURE! Expected {new_status}, but got {updated_status}")
            print(
                "This confirms the bug - the API returns success but doesn't actually update the status!"
            )
    else:
        print("Update request failed")


if __name__ == "__main__":
    main()

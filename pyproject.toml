[project]
name = "sagecloud-api"
version = "0.1.0"
description = "Add your description here"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false
requires-python = ">=3.13"
dependencies = [
    "autopep8>=2.3.2",
    "boto3>=1.37.23",
    "botocore>=1.37.23",
    "celery>=5.4.0",
    "channels>=4.2.2",
    "channels-redis>=4.2.1",
    "colorlog>=6.9.0",
    "cryptography>=44.0.2",
    "cuid2>=2.0.1",
    "django>=5.1.7",
    "django-celery-beat>=2.7.0",
    "django-celery-results>=2.5.1",
    "django-cors-headers>=4.7.0",
    "django-debug-toolbar>=5.1.0",
    "django-extensions>=3.2.3",
    "django-filter>=25.1",
    "django-import-export>=4.3.7",
    "django-ipware>=7.0.1",
    "django-jazzmin>=3.0.1",
    "django-redis>=5.4.0",
    "django-sendgrid-v5>=1.2.4",
    "django-storages>=1.14.5",
    "django-widget-tweaks>=1.5.0",
    "djangorestframework>=3.16.0",
    "djangorestframework-api-key>=3.0.0",
    "djangorestframework-simplejwt>=5.5.0",
    "docutils>=0.21.2",
    "drf-extra-fields>=3.7.0",
    "drf-spectacular>=0.28.0",
    "drf-standardized-errors>=0.14.1",
    "elastic-apm>=6.23.0",
    "email-validator>=2.2.0",
    "fcm-django>=2.2.1",
    "flower>=2.0.1",
    "gunicorn>=23.0.0",
    "hvac>=2.3.0",
    "locust>=2.33.2",
    "logtail-python>=0.3.3",
    "markdown>=3.7",
    "markupsafe>=3.0.2",
    "opensearch-py>=2.8.0",
    "pandas>=2.2.3",
    "pdfkit>=1.0.0",
    "pdfrw2>=0.5.0",
    "pillow>=11.1.0",
    "psutil>=7.0.0",
    "psycopg2-binary>=2.9.10",
    "pykolofinance>=4.0.3",
    "pylint>=3.3.6",
    "pyotp>=2.9.0",
    "pytest-django>=4.10.0",
    "python-dateutil>=2.9.0.post0",
    "python-decouple>=3.8",
    "redis>=5.2.1",
    "sendgrid>=6.12.2",
    "sentry-sdk>=2.24.1",
    "tqdm>=4.67.1",
    "uuid6>=2024.7.10",
    "whitenoise>=6.9.0",
    "xhtml2pdf>=0.2.17",
    "google-auth>=2.40.0",
    "mysqlclient>=2.2.7",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flake8>=7.2.0",
    "isort>=6.0.1",
    "pre-commit>=4.2.0",
    "pylint>=3.3.6",
    "pylint-django>=2.6.1",
    "ruff>=0.11.2",
]
test = [
    "pytest>=8.3.5",
    "pytest-cov>=6.1.0",
    "pytest-django>=4.10.0",
]

[tool.isort]
profile = "black"
combine_as_imports = true
include_trailing_comma = true
line_length = 120

[tool.ruff]
line-length = 120

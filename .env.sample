DEBUG=1
SECRET_KEY=x))o3_(azp2h*o%j-3&qk=7f2zyd&+^&=at1$75202z$u2a_90
SQL_ENGINE=django.db.backends.postgresql
REDIS_HOST=redis
DATABASE_URL=
POSTGRES_USER=
POSTGRES_DB=
POSTGRES_PASSWORD=
AUDIT_POSTGRES_USER=
AUDIT_POSTGRES_DB=
AUDIT_POSTGRES_PASSWORD=
AUDIT_POSTGRES_HOST=
AUDIT_POSTGRES_PORT=
ALLOW_EMPTY_PASSWORD=yes
REDIS_PORT=6379
REDIS_URL=redis://redis:6379/1
BROKER_URL=redis://redis:6379/1
FLOWER_BASIC_AUTH=admin:useradmin
APP_NAME=
APP_DESCRIPTION="Template API"
STORAGE=S3
ACCESS_KEY_ID=
ACCESS_SECRET=
BUCKET_NAME=
REGION_NAME=
CUSTOM_DOMAIN=
SMTP_HOST=smtp.zeptomail.com
SMTP_USER=emailapikey
SMTP_PASSWORD=
SENDER_EMAIL=<EMAIL>
CLIENT_URL=http://localhost:3000
SENTRY_DSN=
LOGTAIL_SOURCE_TOKEN=
ELK_LOGGER_URL=
ENVIRONMENT_INSTANCE=dev
ENCRYPTION_KEY=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
SAGECLOUD_VAS_GATE_BASE_URL=
SAGECLOUD_VAS_GATE_API_KEY=
EASYPAY_CLIENT_CODE=

# Generated by Django 5.1.7 on 2025-06-30 13:57

import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("transaction", "0005_rename_identity_full_name_kycvastransaction_full_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WemaPayoutHistory",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "wema_transaction_id",
                    models.Char<PERSON>ield(
                        blank=True, db_index=True, max_length=100, null=True
                    ),
                ),
                ("recipient_account_number", models.CharField(max_length=10)),
                ("recipient_account_name", models.CharField(max_length=255)),
                ("recipient_bank_name", models.CharField(max_length=255)),
                ("recipient_bank_code", models.CharField(max_length=6)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                ("narration", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("wema_response", models.JSONField(blank=True, null=True)),
                (
                    "wema_response_code",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                ("wema_response_message", models.TextField(blank=True, null=True)),
                ("requery_count", models.IntegerField(default=0)),
                ("last_requery_at", models.DateTimeField(blank=True, null=True)),
                (
                    "session_id",
                    models.CharField(
                        blank=True, db_index=True, max_length=100, null=True
                    ),
                ),
                (
                    "initiated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="wema_payouts_initiated",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Wema Payout History",
                "verbose_name_plural": "Wema Payout Histories",
                "ordering": ("-created_at",),
                "indexes": [
                    models.Index(
                        fields=["status", "created_at"],
                        name="transaction_status_639893_idx",
                    ),
                    models.Index(
                        fields=["initiated_by", "created_at"],
                        name="transaction_initiat_6451e1_idx",
                    ),
                ],
            },
        ),
    ]

# Generated by Django 5.1.7 on 2025-07-14 11:54

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("transaction", "0009_alter_commissiontransaction_txn_class"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TransactionLien",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "status",
                    models.CharField(
                        choices=[("ACTIVE", "Active"), ("REMOVED", "Removed")],
                        db_index=True,
                        default="ACTIVE",
                        help_text="Current status of the lien",
                        max_length=10,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount under lien (matches transaction amount)",
                        max_digits=20,
                    ),
                ),
                (
                    "placed_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        help_text="When the lien was placed",
                    ),
                ),
                (
                    "removed_at",
                    models.DateTimeField(
                        blank=True,
                        db_index=True,
                        help_text="When the lien was removed",
                        null=True,
                    ),
                ),
                (
                    "placed_by",
                    models.ForeignKey(
                        help_text="Admin user who placed the lien",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="liens_placed",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "removed_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="Admin user who removed the lien",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="liens_removed",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "transaction",
                    models.OneToOneField(
                        help_text="The transaction that has a lien placed on it",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="lien",
                        to="transaction.transaction",
                    ),
                ),
            ],
            options={
                "verbose_name": "Transaction Lien",
                "verbose_name_plural": "Transaction Liens",
                "ordering": ["-placed_at"],
                "indexes": [
                    models.Index(
                        fields=["status", "placed_at"],
                        name="transaction_status_8dff8e_idx",
                    ),
                    models.Index(
                        fields=["transaction", "status"],
                        name="transaction_transac_fe45d3_idx",
                    ),
                    models.Index(
                        fields=["placed_by", "placed_at"],
                        name="transaction_placed__889ca9_idx",
                    ),
                ],
            },
        ),
    ]

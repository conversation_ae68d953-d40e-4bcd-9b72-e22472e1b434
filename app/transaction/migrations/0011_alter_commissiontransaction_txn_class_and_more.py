# Generated by Django 5.1.7 on 2025-07-28 09:56

import common.kgs
import django.db.models.deletion
import django.utils.timezone
import transaction.enums
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business", "0006_alter_businessvasproduct_product_type"),
        ("transaction", "0010_create_transacion_lien"),
        ("wallet", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="commissiontransaction",
            name="txn_class",
            field=models.CharField(
                choices=[
                    ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                    ("TRANSFER", "TRANSFER"),
                    ("WITHDRAWAL", "WITHDRAWAL"),
                    ("AIRTIME", "AIRTIME"),
                    ("DATA", "DATA"),
                    ("BETTING", "BETTING"),
                    ("ELECTRICITY", "ELECTRICITY"),
                    ("CABLE_TV", "CABLE_TV"),
                    ("SME_DATA", "SME_DATA"),
                    ("KYC", "KYC"),
                    ("EDUCATION", "EDUCATION"),
                    ("EPIN", "EPIN"),
                    ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                    ("MANUAL_CREDIT", "MANUAL_CREDIT"),
                    ("MANUAL_DEBIT", "MANUAL_DEBIT"),
                ],
                max_length=30,
            ),
        ),
        migrations.CreateModel(
            name="WithdrawalVasTransaction",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "merchant_reference",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=transaction.enums.TransactionStatusEnum.choices,
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "mode",
                    models.CharField(
                        choices=transaction.enums.TransactionModeEnum.choices,
                        db_index=True,
                        max_length=10,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=20)),
                (
                    "charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                (
                    "net_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=20),
                ),
                ("narration", models.TextField()),
                ("settlement_account_number", models.CharField(max_length=10)),
                ("settlement_account_name", models.CharField(max_length=255)),
                ("settlement_bank_name", models.CharField(max_length=255)),
                ("settlement_bank_code", models.CharField(max_length=6)),
                (
                    "source_wallet_type",
                    models.CharField(
                        help_text="Type of wallet funds are withdrawn from",
                        max_length=30,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(
                        blank=True, db_index=True, max_length=40, null=True
                    ),
                ),
                (
                    "transaction_id",
                    models.CharField(
                        blank=True, db_index=True, max_length=40, null=True
                    ),
                ),
                (
                    "requery_response_code",
                    models.CharField(
                        blank=True,
                        help_text="Response code for requery",
                        max_length=5,
                        null=True,
                    ),
                ),
                (
                    "requery_response_message",
                    models.CharField(
                        blank=True,
                        help_text="Response message for requery",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "business",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="business.business",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(class)ss",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Withdrawal VAS Transaction",
                "verbose_name_plural": "Withdrawal VAS Transactions",
                "db_table": "withdrawal_vas_transaction",
                "indexes": [
                    models.Index(
                        fields=["settlement_account_number"],
                        name="withdrawal__settlem_15aa23_idx",
                    ),
                    models.Index(
                        fields=["source_wallet_type"],
                        name="withdrawal__source__6f6db7_idx",
                    ),
                    models.Index(
                        fields=["session_id"], name="withdrawal__session_ac4d7c_idx"
                    ),
                    models.Index(
                        fields=["transaction_id"], name="withdrawal__transac_a7e38b_idx"
                    ),
                ],
            },
        ),
    ]

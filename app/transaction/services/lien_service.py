from decimal import Decimal
from typing import Optional

from django.db import transaction
from django.utils import timezone
from transaction.models import Transaction, TransactionLien
from transaction.models.lien import TransactionLienStatus


class LienServiceException(Exception):
    """Custom exception for lien service operations"""

    pass


class TransactionLienService:
    """
    Service class for managing transaction liens.

    Handles placing and removing liens on transactions with proper validation
    and business logic enforcement.
    """

    def __init__(self, transaction_obj: Transaction):
        """
        Initialize the service with a transaction.

        Args:
            transaction_obj: Transaction instance to manage liens for
        """
        self.transaction = transaction_obj

    def place_lien(self, admin_user) -> TransactionLien:
        """
        Place a lien on the transaction.

        Args:
            admin_user: User instance of the admin placing the lien

        Returns:
            TransactionLien: The created lien instance

        Raises:
            LienServiceException: If lien cannot be placed
        """
        # Validate admin user permissions
        if not self._is_admin_user(admin_user):
            raise LienServiceException(
                "Only admin users can place liens on transactions"
            )

        # Check if transaction already has an active lien
        if self.has_active_lien():
            raise LienServiceException(
                f"Transaction {self.transaction.reference} already has an active lien"
            )

        # Validate transaction is eligible for lien
        if not self._is_transaction_eligible_for_lien():
            raise LienServiceException(
                f"Transaction {self.transaction.reference} is not eligible for lien placement"
            )

        with transaction.atomic():
            # Create the lien
            lien = TransactionLien.objects.create(
                transaction=self.transaction,
                placed_by=admin_user,
                status=TransactionLienStatus.ACTIVE,
                amount=self.transaction.amount,
                placed_at=timezone.now(),
            )

            return lien

    def remove_lien(self, admin_user) -> TransactionLien:
        """
        Remove an active lien from the transaction.

        Args:
            admin_user: User instance of the admin removing the lien

        Returns:
            TransactionLien: The updated lien instance

        Raises:
            LienServiceException: If lien cannot be removed
        """
        # Validate admin user permissions
        if not self._is_admin_user(admin_user):
            raise LienServiceException(
                "Only admin users can remove liens from transactions"
            )

        # Get active lien
        lien = self.get_active_lien()
        if not lien:
            raise LienServiceException(
                f"No active lien found for transaction {self.transaction.reference}"
            )

        with transaction.atomic():
            # Remove the lien
            lien.remove_lien(admin_user)

            return lien

    def has_active_lien(self) -> bool:
        """
        Check if the transaction has an active lien.

        Returns:
            bool: True if transaction has an active lien, False otherwise
        """
        return TransactionLien.objects.filter(
            transaction=self.transaction, status=TransactionLienStatus.ACTIVE
        ).exists()

    def get_active_lien(self) -> Optional[TransactionLien]:
        """
        Get the active lien for the transaction.

        Returns:
            TransactionLien or None: The active lien if exists, None otherwise
        """
        try:
            return TransactionLien.objects.get(
                transaction=self.transaction, status=TransactionLienStatus.ACTIVE
            )
        except TransactionLien.DoesNotExist:
            return None

    def get_lien_amount(self) -> Decimal:
        """
        Get the amount under lien for this transaction.

        Returns:
            Decimal: Amount under lien, 0 if no active lien
        """
        lien = self.get_active_lien()
        return lien.amount if lien else Decimal("0.00")

    def _is_admin_user(self, user) -> bool:
        """
        Check if user has admin permissions to manage liens.

        Args:
            user: User instance to check

        Returns:
            bool: True if user is admin, False otherwise
        """
        if not user or not user.is_authenticated:
            return False

        # Check if user has admin role (following existing codebase pattern)
        return user.role == "Admin"

    def _is_transaction_eligible_for_lien(self) -> bool:
        """
        Check if transaction is eligible for lien placement.

        Returns:
            bool: True if eligible, False otherwise
        """
        # For now, all transactions are eligible for liens
        # TODO:
        # This can be extended to add specific business rules
        # e.g., only successful transactions, certain transaction types, etc.
        return True

    @classmethod
    def get_business_lien_amount(cls, business) -> Decimal:
        """
        Get total amount under lien for a business across all wallets.

        Args:
            business: Business instance

        Returns:
            Decimal: Total amount under lien for the business
        """
        from django.db.models import Sum

        total_lien = TransactionLien.objects.filter(
            transaction__business=business, status=TransactionLienStatus.ACTIVE
        ).aggregate(total=Sum("amount"))["total"]

        return total_lien or Decimal("0.00")

    @classmethod
    def get_wallet_lien_amount(cls, wallet) -> Decimal:
        """
        Get total amount under lien for a specific wallet.

        Args:
            wallet: Wallet instance

        Returns:
            Decimal: Total amount under lien for the wallet
        """
        from django.db.models import Sum

        total_lien = TransactionLien.objects.filter(
            transaction__wallet=wallet, status=TransactionLienStatus.ACTIVE
        ).aggregate(total=Sum("amount"))["total"]

        return total_lien or Decimal("0.00")

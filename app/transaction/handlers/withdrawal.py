from ledger.enums import LedgerTypeEnum
from ledger.models import LedgerTransaction
from transaction.handlers.interface import BaseVASTransactionHandler
from transaction.models.base import Transaction
from transaction.models.withdrawal import WithdrawalVasTransaction
from transaction.utils import get_ledger


class WithdrawalTransactionHandler(BaseVASTransactionHandler):
    """
    Handler for withdrawal transactions where merchants withdraw funds
    from their wallets to their settlement accounts.
    """

    def create_vas_transaction(
        self, txn: Transaction, extra_fields: dict
    ) -> WithdrawalVasTransaction:
        """
        Create a withdrawal VAS transaction record.

        Args:
            txn: The base transaction record
            extra_fields: Additional fields specific to withdrawal transactions

        Returns:
            WithdrawalVasTransaction: The created withdrawal transaction record
        """
        payload = self.get_base_payload(txn)
        payload.update(extra_fields)
        return WithdrawalVasTransaction.objects.create(**payload)

    def update_vas_transaction(
        self, txn: Transaction, vas_txn: WithdrawalVasTransaction, update_fields: dict
    ) -> WithdrawalVasTransaction:
        """
        Update a withdrawal VAS transaction with new data.

        Args:
            txn: The base transaction record
            vas_txn: The withdrawal VAS transaction to update
            update_fields: Fields to update

        Returns:
            WithdrawalVasTransaction: The updated withdrawal transaction record
        """
        return super().update_vas_transaction(vas_txn, update_fields)

    def create_ledger_entry(self, txn: Transaction) -> LedgerTransaction:
        """
        Create a ledger entry for the withdrawal transaction.

        Args:
            txn: The transaction to create ledger entry for

        Returns:
            LedgerTransaction: The created ledger transaction
        """
        ledger = get_ledger(LedgerTypeEnum.WITHDRAWAL.value)
        ledger_txn = ledger.credit(txn)
        return ledger_txn

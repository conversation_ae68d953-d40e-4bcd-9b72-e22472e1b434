from decimal import Decimal

from business.models import Business
from django.contrib.auth import get_user_model
from django.test import TestCase
from transaction.enums import (
    TransactionClassEnum,
    TransactionModeEnum,
    TransactionStatusEnum,
)
from transaction.models import Transaction, TransactionLien
from transaction.models.lien import TransactionLienStatus
from transaction.services import LienServiceException, TransactionLienService
from wallet.enums import WalletEnums
from wallet.models import Wallet

User = get_user_model()


class TransactionLienSystemTestCase(TestCase):
    """Test cases for the transaction lien system"""

    def setUp(self):
        """Set up test data"""
        # Create admin user
        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="Admin",
            firstname="Admin",
            lastname="User",
        )

        # Create business owner user
        self.business_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="Business_Owner",
            firstname="Business",
            lastname="Owner",
        )

        # Create business
        self.business = Business.objects.create(
            name="Test Business", email="<EMAIL>", owner=self.business_user
        )

        # Create wallet
        self.wallet = Wallet.objects.create(
            business=self.business,
            type=WalletEnums.GENERAL,
            balance=Decimal("10000.00"),
        )

        # Create test transaction
        self.transaction = Transaction.objects.create(
            wallet=self.wallet,
            business=self.business,
            reference="TEST_TXN_001",
            merchant_reference="MERCHANT_REF_001",
            status=TransactionStatusEnum.SUCCESSFUL.value,
            mode=TransactionModeEnum.DEBIT.value,
            txn_class=TransactionClassEnum.AIRTIME.value,
            type="MTN",
            amount=Decimal("1000.00"),
            charge=Decimal("50.00"),
            net_amount=Decimal("1050.00"),
            old_balance=Decimal("10000.00"),
            new_balance=Decimal("8950.00"),
            narration="Test transaction for lien testing",
            is_wallet_impacted=True,
            provider="TEST_PROVIDER",
        )

    def test_lien_placement(self):
        """Test placing a lien on a transaction"""
        lien_service = TransactionLienService(self.transaction)

        # Initially no lien should exist
        self.assertFalse(lien_service.has_active_lien())
        self.assertEqual(lien_service.get_lien_amount(), Decimal("0.00"))

        # Place lien
        lien = lien_service.place_lien(self.admin_user)

        # Verify lien was created
        self.assertIsInstance(lien, TransactionLien)
        self.assertEqual(lien.transaction, self.transaction)
        self.assertEqual(lien.placed_by, self.admin_user)
        self.assertEqual(lien.status, TransactionLienStatus.ACTIVE)
        self.assertEqual(lien.amount, self.transaction.amount)

        # Verify service methods
        self.assertTrue(lien_service.has_active_lien())
        self.assertEqual(lien_service.get_lien_amount(), self.transaction.amount)

    def test_duplicate_lien_prevention(self):
        """Test that duplicate liens cannot be placed on the same transaction"""
        lien_service = TransactionLienService(self.transaction)

        # Place first lien
        lien_service.place_lien(self.admin_user)

        # Attempt to place second lien should fail
        with self.assertRaises(LienServiceException) as context:
            lien_service.place_lien(self.admin_user)

        self.assertIn("already has an active lien", str(context.exception))

    def test_lien_removal(self):
        """Test removing a lien from a transaction"""
        lien_service = TransactionLienService(self.transaction)

        # Place lien first
        original_lien = lien_service.place_lien(self.admin_user)

        # Remove lien
        removed_lien = lien_service.remove_lien(self.admin_user)

        # Verify it's the same lien object but status changed
        self.assertEqual(removed_lien.id, original_lien.id)
        self.assertEqual(removed_lien.status, TransactionLienStatus.REMOVED)
        self.assertEqual(removed_lien.removed_by, self.admin_user)
        self.assertIsNotNone(removed_lien.removed_at)

        # Verify service methods
        self.assertFalse(lien_service.has_active_lien())
        self.assertEqual(lien_service.get_lien_amount(), Decimal("0.00"))

    def test_remove_nonexistent_lien(self):
        """Test removing a lien when none exists"""
        lien_service = TransactionLienService(self.transaction)

        # Attempt to remove lien when none exists should fail
        with self.assertRaises(LienServiceException) as context:
            lien_service.remove_lien(self.admin_user)

        self.assertIn("No active lien found", str(context.exception))

    def test_wallet_balance_calculations(self):
        """Test wallet balance calculations with liens"""
        # Initial balance calculations
        self.assertEqual(self.wallet.ledger_balance, Decimal("10000.00"))
        self.assertEqual(self.wallet.lien_amount, Decimal("0.00"))
        self.assertEqual(self.wallet.available_balance, Decimal("10000.00"))
        self.assertEqual(self.wallet.withdrawable_balance, Decimal("10000.00"))

        # Place lien
        lien_service = TransactionLienService(self.transaction)
        lien = lien_service.place_lien(self.admin_user)

        # Refresh wallet to get updated calculations
        self.wallet.refresh_from_db()

        # Balance calculations with lien
        self.assertEqual(self.wallet.ledger_balance, Decimal("10000.00"))  # Unchanged
        self.assertEqual(self.wallet.lien_amount, lien.amount)
        self.assertEqual(
            self.wallet.available_balance, Decimal("10000.00") - lien.amount
        )
        self.assertEqual(
            self.wallet.withdrawable_balance, self.wallet.available_balance
        )

        # Remove lien
        lien_service.remove_lien(self.admin_user)
        self.wallet.refresh_from_db()

        # Balance calculations after lien removal
        self.assertEqual(self.wallet.ledger_balance, Decimal("10000.00"))
        self.assertEqual(self.wallet.lien_amount, Decimal("0.00"))
        self.assertEqual(self.wallet.available_balance, Decimal("10000.00"))
        self.assertEqual(self.wallet.withdrawable_balance, Decimal("10000.00"))

    def test_wallet_can_debit_with_lien(self):
        """Test wallet debit capability with liens"""
        # Initially can debit full amount
        self.assertTrue(self.wallet.can_debit(Decimal("5000.00")))
        self.assertTrue(self.wallet.can_debit(Decimal("10000.00")))
        self.assertFalse(self.wallet.can_debit(Decimal("10001.00")))

        # Place lien
        lien_service = TransactionLienService(self.transaction)
        lien = lien_service.place_lien(self.admin_user)

        # Refresh wallet
        self.wallet.refresh_from_db()

        # Can debit less than available balance
        available_amount = Decimal("10000.00") - lien.amount
        self.assertTrue(self.wallet.can_debit(available_amount))
        self.assertTrue(self.wallet.can_debit(available_amount - Decimal("1.00")))

        # Cannot debit more than available balance
        self.assertFalse(self.wallet.can_debit(available_amount + Decimal("1.00")))
        self.assertFalse(
            self.wallet.can_debit(Decimal("10000.00"))
        )  # Full ledger balance

    def test_non_admin_user_cannot_place_lien(self):
        """Test that non-admin users cannot place liens"""
        lien_service = TransactionLienService(self.transaction)

        # Business owner should not be able to place lien
        with self.assertRaises(LienServiceException) as context:
            lien_service.place_lien(self.business_user)

        self.assertIn("Only admin users can place liens", str(context.exception))

    def test_business_lien_amount_calculation(self):
        """Test calculating total lien amount for a business"""
        # Create another transaction
        transaction2 = Transaction.objects.create(
            wallet=self.wallet,
            business=self.business,
            reference="TEST_TXN_002",
            merchant_reference="MERCHANT_REF_002",
            status=TransactionStatusEnum.SUCCESSFUL.value,
            mode=TransactionModeEnum.DEBIT.value,
            txn_class=TransactionClassEnum.DATA.value,
            type="MTN",
            amount=Decimal("500.00"),
            charge=Decimal("25.00"),
            net_amount=Decimal("525.00"),
            old_balance=Decimal("8950.00"),
            new_balance=Decimal("8425.00"),
            narration="Second test transaction",
            is_wallet_impacted=True,
            provider="TEST_PROVIDER",
        )

        # Initially no liens
        total_lien = TransactionLienService.get_business_lien_amount(self.business)
        self.assertEqual(total_lien, Decimal("0.00"))

        # Place lien on first transaction
        lien_service1 = TransactionLienService(self.transaction)
        lien1 = lien_service1.place_lien(self.admin_user)

        total_lien = TransactionLienService.get_business_lien_amount(self.business)
        self.assertEqual(total_lien, lien1.amount)

        # Place lien on second transaction
        lien_service2 = TransactionLienService(transaction2)
        lien2 = lien_service2.place_lien(self.admin_user)

        total_lien = TransactionLienService.get_business_lien_amount(self.business)
        self.assertEqual(total_lien, lien1.amount + lien2.amount)

        # Remove one lien
        lien_service1.remove_lien(self.admin_user)

        total_lien = TransactionLienService.get_business_lien_amount(self.business)
        self.assertEqual(total_lien, lien2.amount)

    def test_wallet_lien_amount_calculation(self):
        """Test calculating total lien amount for a wallet"""
        # Initially no liens
        wallet_lien = TransactionLienService.get_wallet_lien_amount(self.wallet)
        self.assertEqual(wallet_lien, Decimal("0.00"))

        # Place lien
        lien_service = TransactionLienService(self.transaction)
        lien = lien_service.place_lien(self.admin_user)

        wallet_lien = TransactionLienService.get_wallet_lien_amount(self.wallet)
        self.assertEqual(wallet_lien, lien.amount)

        # Remove lien
        lien_service.remove_lien(self.admin_user)

        wallet_lien = TransactionLienService.get_wallet_lien_amount(self.wallet)
        self.assertEqual(wallet_lien, Decimal("0.00"))

import random
from decimal import Decimal

from common.kgs import generate_uuid7
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from transaction.enums import TransactionStatusEnum
from transaction.models.wema_payout import WemaPayoutHistory

User = get_user_model()


class Command(BaseCommand):
    help = "Seed test data for Wema payout history"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=20,
            help="Number of payout records to create (default: 20)",
        )
        parser.add_argument(
            "--admin-email",
            type=str,
            default="<EMAIL>",
            help="Email of admin user to use as initiator (default: <EMAIL>)",
        )

    def handle(self, *args, **options):
        count = options["count"]
        admin_email = options["admin_email"]

        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            email=admin_email,
            defaults={
                "firstname": "Admin",
                "lastname": "User",
                "role": "Admin",
                "is_staff": True,
                "verified": True,
            },
        )

        if created:
            self.stdout.write(self.style.SUCCESS(f"Created admin user: {admin_email}"))

        # Sample bank data
        banks = [
            {"code": "044", "name": "ACCESS BANK"},
            {"code": "011", "name": "FIRST BANK"},
            {"code": "058", "name": "GTBANK"},
            {"code": "030", "name": "HERITAGE BANK"},
            {"code": "070", "name": "FIDELITY BANK"},
            {"code": "221", "name": "STANBIC IBTC"},
            {"code": "214", "name": "FCMB"},
            {"code": "035", "name": "WEMA BANK"},
        ]

        # Sample account names
        account_names = [
            "JOHN DOE",
            "JANE SMITH",
            "MICHAEL JOHNSON",
            "SARAH WILLIAMS",
            "DAVID BROWN",
            "EMILY DAVIS",
            "ROBERT MILLER",
            "LISA WILSON",
            "JAMES MOORE",
            "MARY TAYLOR",
        ]

        # Sample narrations
        narrations = [
            "Salary payment",
            "Vendor payment",
            "Refund payment",
            "Commission payment",
            "Bonus payment",
            "Contract payment",
            "Service payment",
            "Consultation fee",
            "Project payment",
            "Monthly allowance",
        ]

        statuses = [
            TransactionStatusEnum.SUCCESSFUL.value,
            TransactionStatusEnum.FAILED.value,
            TransactionStatusEnum.PENDING.value,
        ]

        created_count = 0

        for i in range(count):
            bank = random.choice(banks)
            account_name = random.choice(account_names)
            narration = random.choice(narrations)
            status = random.choice(statuses)

            # Generate random account number (10 digits)
            account_number = "".join([str(random.randint(0, 9)) for _ in range(10)])

            # Generate random amount between 1000 and 100000
            amount = Decimal(random.randint(1000, 100000))

            # Create reference
            reference = generate_uuid7()

            # Create payout history
            WemaPayoutHistory.objects.create(
                reference=reference,
                wema_transaction_id=(
                    f"WEMA{random.randint(100000, 999999)}"
                    if status != TransactionStatusEnum.PENDING.value
                    else None
                ),
                recipient_account_number=account_number,
                recipient_account_name=account_name,
                recipient_bank_name=bank["name"],
                recipient_bank_code=bank["code"],
                amount=amount,
                narration=narration,
                status=status,
                initiated_by=admin_user,
                wema_response=(
                    {
                        "status": status,
                        "message": f"Transaction {status}",
                        "transaction_id": (
                            f"WEMA{random.randint(100000, 999999)}"
                            if status != TransactionStatusEnum.PENDING.value
                            else None
                        ),
                    }
                    if status != TransactionStatusEnum.PENDING.value
                    else None
                ),
                wema_response_code=(
                    "00"
                    if status == TransactionStatusEnum.SUCCESSFUL.value
                    else (
                        "99" if status == TransactionStatusEnum.FAILED.value else None
                    )
                ),
                wema_response_message=(
                    "Successful"
                    if status == TransactionStatusEnum.SUCCESSFUL.value
                    else (
                        "Failed"
                        if status == TransactionStatusEnum.FAILED.value
                        else None
                    )
                ),
                session_id=f"SES{random.randint(100000, 999999)}",
            )

            created_count += 1

            if created_count % 5 == 0:
                self.stdout.write(f"Created {created_count} payout records...")

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} Wema payout history records"
            )
        )

        # Display summary
        self.stdout.write("\n--- Summary ---")
        self.stdout.write(f"Total records created: {created_count}")
        self.stdout.write(f"Admin user: {admin_user.email}")

        # Count by status
        successful_count = WemaPayoutHistory.objects.filter(
            status=TransactionStatusEnum.SUCCESSFUL.value
        ).count()
        failed_count = WemaPayoutHistory.objects.filter(
            status=TransactionStatusEnum.FAILED.value
        ).count()
        pending_count = WemaPayoutHistory.objects.filter(
            status=TransactionStatusEnum.PENDING.value
        ).count()

        self.stdout.write(f"Successful: {successful_count}")
        self.stdout.write(f"Failed: {failed_count}")
        self.stdout.write(f"Pending: {pending_count}")

        self.stdout.write("\n--- API Testing ---")
        self.stdout.write("You can now test the admin Wema endpoints:")
        self.stdout.write("1. Name Enquiry: POST /api/v1/admin/wema/name-enquiry/")
        self.stdout.write("2. Funds Transfer: POST /api/v1/admin/wema/funds-transfer/")
        self.stdout.write(
            "3. Account Statements: POST /api/v1/admin/wema/account-statements/"
        )
        self.stdout.write("4. Payout History: GET /api/v1/admin/wema/payout-history/")
        self.stdout.write(
            "5. Payout Detail: GET /api/v1/admin/wema/payout-history/{id}/"
        )

        self.stdout.write("\nSample request for funds transfer:")
        self.stdout.write(
            """{
    "bank_code": "044",
    "account_number": "**********",
    "account_name": "JOHN DOE",
    "amount": "5000.00",
    "narration": "Test payment"
}"""
        )

        self.stdout.write("\nSample request for name enquiry:")
        self.stdout.write(
            """{
    "account_number": "**********",
    "bank_code": "044"
}"""
        )

        self.stdout.write("\nSample request for account statements:")
        self.stdout.write(
            """{
    "account_number": "**********",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
}"""
        )

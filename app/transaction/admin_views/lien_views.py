import logging
from decimal import Decimal

from audit.utils import log_lien_placed, log_lien_removed
from common.permissions import IsAdmin
from django.db import models
from django.db.models import Count, Sum
from django.shortcuts import get_object_or_404
from django_filters import rest_framework as django_filters
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import filters, generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from transaction.models import Transaction, TransactionLien
from transaction.models.lien import TransactionLienStatus
from transaction.services import TransactionLienService

from .lien_serializers import (
    LienSummarySerializer,
    PlaceLienSerializer,
    RemoveLienSerializer,
    TransactionLienSerializer,
)

logger = logging.getLogger(__name__)


class LienFilter(django_filters.FilterSet):
    """Custom filter for transaction liens"""

    placed_date = django_filters.DateFilter(
        field_name="placed_at__date", help_text="Filter by placement date (YYYY-MM-DD)"
    )
    removed_date = django_filters.DateFilter(
        field_name="removed_at__date", help_text="Filter by removal date (YYYY-MM-DD)"
    )

    status = django_filters.ChoiceFilter(
        choices=TransactionLienStatus.CHOICES, help_text="Filter by lien status"
    )

    class Meta:
        model = TransactionLien
        fields = {
            "status": ["exact"],
        }


@extend_schema_view(
    list=extend_schema(
        summary="List Transaction Liens",
        description="Retrieve a list of all transaction liens with filtering and search capabilities",
        tags=["Admin - Transaction Liens"],
    ),
    retrieve=extend_schema(
        summary="Get Transaction Lien Details",
        description="Retrieve detailed information about a specific transaction lien",
        tags=["Admin - Transaction Liens"],
    ),
)
class AdminTransactionLienViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for admin users to view transaction liens.

    Provides endpoints to list and retrieve transaction liens with filtering capabilities.
    """

    queryset = TransactionLien.objects.select_related(
        "transaction",
        "transaction__business",
        "transaction__wallet",
        "placed_by",
        "removed_by",
    ).all()
    serializer_class = TransactionLienSerializer
    permission_classes = [IsAuthenticated, IsAdmin]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = LienFilter
    search_fields = [
        "transaction__reference",
        "transaction__business__name",
        "transaction__business__email",
        "placed_by__email",
        "removed_by__email",
    ]
    ordering_fields = ["placed_at", "removed_at", "amount", "status"]
    ordering = ["-placed_at"]

    @extend_schema(
        summary="Get Lien Summary Statistics",
        description="Get summary statistics about transaction liens",
        responses={200: LienSummarySerializer},
        tags=["Admin - Transaction Liens"],
    )
    @action(detail=False, methods=["get"])
    def summary(self, request):
        """Get summary statistics about liens"""

        # Get summary data
        summary_data = TransactionLien.objects.aggregate(
            total_liens=Count("id"),
            total_amount_under_lien=Sum(
                "amount", filter=models.Q(status=TransactionLienStatus.ACTIVE)
            ),
            active_liens=Count(
                "id", filter=models.Q(status=TransactionLienStatus.ACTIVE)
            ),
            removed_liens=Count(
                "id", filter=models.Q(status=TransactionLienStatus.REMOVED)
            ),
        )

        # Handle None values
        summary_data["total_amount_under_lien"] = summary_data[
            "total_amount_under_lien"
        ] or Decimal("0.00")

        serializer = LienSummarySerializer(summary_data)
        return Response(serializer.data, status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        summary="Place Lien on Transaction",
        description="Place a lien on a specific transaction to prevent withdrawal of that amount",
        request=PlaceLienSerializer,
        responses={201: TransactionLienSerializer},
        tags=["Admin - Transaction Liens"],
    ),
)
class AdminPlaceLienView(generics.CreateAPIView):
    """
    API endpoint for admin users to place liens on transactions.
    """

    serializer_class = PlaceLienSerializer
    permission_classes = [IsAuthenticated, IsAdmin]

    def perform_create(self, serializer):
        """Create the lien and log the action"""
        lien = serializer.save()

        logger.info(
            f"Admin user {self.request.user.email} placed lien on transaction "
            f"{lien.transaction.reference} for amount {lien.amount}"
        )

        try:
            log_lien_placed(
                request=self.request,
                user=self.request.user,
                transaction=lien.transaction,
                lien=lien,
            )
        except Exception as e:
            logger.warning(f"Failed to log lien placement audit: {e}")

        return lien

    def create(self, request, *args, **kwargs):
        """Override create to return lien details"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        lien = self.perform_create(serializer)

        # Return detailed lien information
        response_serializer = TransactionLienSerializer(lien)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)


@extend_schema_view(
    post=extend_schema(
        summary="Remove Lien from Transaction",
        description="Remove an active lien from a specific transaction",
        request=RemoveLienSerializer,
        responses={200: TransactionLienSerializer},
        tags=["Admin - Transaction Liens"],
    ),
)
class AdminRemoveLienView(generics.CreateAPIView):
    """
    API endpoint for admin users to remove liens from transactions.
    """

    serializer_class = RemoveLienSerializer
    permission_classes = [IsAuthenticated, IsAdmin]

    def get_transaction(self):
        """Get transaction from URL parameter"""
        transaction_reference = self.kwargs.get("transaction_reference")
        return get_object_or_404(Transaction, reference=transaction_reference)

    def get_serializer_context(self):
        """Add transaction to serializer context"""
        context = super().get_serializer_context()
        context["transaction"] = self.get_transaction()
        return context

    def perform_create(self, serializer):
        """Remove the lien and log the action"""
        lien = serializer.save()

        logger.info(
            f"Admin user {self.request.user.email} removed lien from transaction "
            f"{lien.transaction.reference} for amount {lien.amount}"
        )

        # Log to audit system
        try:
            log_lien_removed(
                request=self.request,
                user=self.request.user,
                transaction=lien.transaction,
                lien=lien,
            )
        except Exception as e:
            logger.warning(f"Failed to log lien removal audit: {e}")

        return lien

    def create(self, request, *args, **kwargs):
        """Override create to return lien details"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        lien = self.perform_create(serializer)

        # Return detailed lien information
        response_serializer = TransactionLienSerializer(lien)
        return Response(response_serializer.data, status=status.HTTP_200_OK)


@extend_schema(
    summary="Get Transaction Lien Status",
    description="Get lien status for a specific transaction",
    responses={200: TransactionLienSerializer},
    tags=["Admin - Transaction Liens"],
)
class AdminTransactionLienStatusView(generics.RetrieveAPIView):
    """
    API endpoint to get lien status for a specific transaction.
    """

    serializer_class = TransactionLienSerializer
    permission_classes = [IsAuthenticated, IsAdmin]
    lookup_field = "transaction__reference"
    lookup_url_kwarg = "transaction_reference"

    def get_queryset(self):
        return TransactionLien.objects.select_related(
            "transaction",
            "transaction__business",
            "transaction__wallet",
            "placed_by",
            "removed_by",
        ).all()

    def retrieve(self, request, *args, **kwargs):
        """Get lien information for transaction"""
        transaction_reference = kwargs.get("transaction_reference")
        transaction = get_object_or_404(Transaction, reference=transaction_reference)

        lien_service = TransactionLienService(transaction)
        lien = lien_service.get_active_lien()

        if not lien:
            # Check if there was a removed lien
            try:
                lien = TransactionLien.objects.filter(
                    transaction=transaction, status=TransactionLienStatus.REMOVED
                ).latest("removed_at")
            except TransactionLien.DoesNotExist:
                return Response(
                    {"detail": "No lien found for this transaction"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        serializer = self.get_serializer(lien)
        return Response(serializer.data, status=status.HTTP_200_OK)

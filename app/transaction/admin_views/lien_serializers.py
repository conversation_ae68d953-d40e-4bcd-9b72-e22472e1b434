from rest_framework import serializers
from transaction.models import Transaction, TransactionLien
from transaction.services import LienServiceException, TransactionLienService


class TransactionLienSerializer(serializers.ModelSerializer):
    """Serializer for TransactionLien model"""

    transaction_reference = serializers.Char<PERSON>ield(
        source="transaction.reference", read_only=True
    )
    business_name = serializers.CharField(
        source="transaction.business.name", read_only=True
    )
    business_email = serializers.CharField(
        source="transaction.business.email", read_only=True
    )
    wallet_type = serializers.CharField(
        source="transaction.wallet.type", read_only=True
    )
    placed_by_email = serializers.CharField(source="placed_by.email", read_only=True)
    removed_by_email = serializers.CharField(source="removed_by.email", read_only=True)

    class Meta:
        model = TransactionLien
        fields = [
            "id",
            "transaction_reference",
            "business_name",
            "business_email",
            "wallet_type",
            "status",
            "amount",
            "placed_at",
            "removed_at",
            "placed_by_email",
            "removed_by_email",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "transaction_reference",
            "business_name",
            "business_email",
            "wallet_type",
            "status",
            "amount",
            "placed_at",
            "removed_at",
            "placed_by_email",
            "removed_by_email",
            "created_at",
            "updated_at",
        ]


class PlaceLienSerializer(serializers.Serializer):
    """Serializer for placing a lien on a transaction"""

    transaction_reference = serializers.Char<PERSON>ield(
        help_text="Reference of the transaction to place lien on"
    )

    def validate_transaction_reference(self, value):
        """Validate that transaction exists"""
        try:
            transaction = Transaction.objects.get(reference=value)
            return transaction
        except Transaction.DoesNotExist:
            raise serializers.ValidationError(
                f"Transaction with reference '{value}' not found"
            )

    def create(self, validated_data):
        """Place lien on the transaction"""
        transaction_reference = validated_data["transaction_reference"]
        admin_user = self.context["request"].user

        try:
            transaction = Transaction.objects.get(reference=transaction_reference)
            lien_service = TransactionLienService(transaction)
            lien = lien_service.place_lien(admin_user)
            return lien
        except LienServiceException as e:
            raise serializers.ValidationError(str(e))


class RemoveLienSerializer(serializers.Serializer):
    """Serializer for removing a lien from a transaction"""

    def create(self, validated_data):
        """Remove lien from the transaction"""
        transaction = self.context["transaction"]
        admin_user = self.context["request"].user

        try:
            lien_service = TransactionLienService(transaction)
            lien = lien_service.remove_lien(admin_user)
            return lien
        except LienServiceException as e:
            raise serializers.ValidationError(str(e))


class LienSummarySerializer(serializers.Serializer):
    """Serializer for lien summary statistics"""

    total_liens = serializers.IntegerField(read_only=True)
    total_amount_under_lien = serializers.DecimalField(
        max_digits=20, decimal_places=2, read_only=True
    )
    active_liens = serializers.IntegerField(read_only=True)
    removed_liens = serializers.IntegerField(read_only=True)


class TransactionWithLienSerializer(serializers.ModelSerializer):
    """Serializer for Transaction with lien information"""

    has_lien = serializers.SerializerMethodField()
    lien_amount = serializers.SerializerMethodField()
    lien_status = serializers.SerializerMethodField()
    lien_placed_at = serializers.SerializerMethodField()
    lien_placed_by = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = [
            "id",
            "reference",
            "merchant_reference",
            "status",
            "mode",
            "txn_class",
            "type",
            "amount",
            "charge",
            "net_amount",
            "narration",
            "created_at",
            "has_lien",
            "lien_amount",
            "lien_status",
            "lien_placed_at",
            "lien_placed_by",
        ]

    def get_has_lien(self, obj):
        """Check if transaction has an active lien"""
        lien_service = TransactionLienService(obj)
        return lien_service.has_active_lien()

    def get_lien_amount(self, obj):
        """Get lien amount for the transaction"""
        lien_service = TransactionLienService(obj)
        return lien_service.get_lien_amount()

    def get_lien_status(self, obj):
        """Get lien status"""
        lien_service = TransactionLienService(obj)
        lien = lien_service.get_active_lien()
        return lien.status if lien else None

    def get_lien_placed_at(self, obj):
        """Get when lien was placed"""
        lien_service = TransactionLienService(obj)
        lien = lien_service.get_active_lien()
        return lien.placed_at if lien else None

    def get_lien_placed_by(self, obj):
        """Get who placed the lien"""
        lien_service = TransactionLienService(obj)
        lien = lien_service.get_active_lien()
        return lien.placed_by.email if lien else None

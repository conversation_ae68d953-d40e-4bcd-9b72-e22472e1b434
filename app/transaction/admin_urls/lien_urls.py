from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from transaction.admin_views import (
    AdminPlaceLienView,
    AdminRemoveLienView,
    AdminTransactionLienStatusView,
    AdminTransactionLienViewSet,
)

app_name = "admin_transaction_liens"

# Router for viewsets
router = DefaultRouter()
router.register(
    "liens",
    AdminTransactionLienViewSet,
    basename="admin-transaction-liens",
)

urlpatterns = [
    # Lien management endpoints
    path(
        "liens/place/",
        AdminPlaceLienView.as_view(),
        name="admin-place-lien",
    ),
    path(
        "transactions/<str:transaction_reference>/lien/remove/",
        AdminRemoveLienView.as_view(),
        name="admin-remove-lien",
    ),
    path(
        "transactions/<str:transaction_reference>/lien/status/",
        AdminTransactionLienStatusView.as_view(),
        name="admin-lien-status",
    ),
    # Include router URLs
    path("", include(router.urls)),
]

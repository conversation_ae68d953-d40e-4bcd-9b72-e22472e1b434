from common.models import AuditableModel
from django.db import models


class TransactionLienStatus:
    """Status choices for transaction liens"""

    ACTIVE = "ACTIVE"
    REMOVED = "REMOVED"

    CHOICES = [
        (ACTIVE, "Active"),
        (REMOVED, "Removed"),
    ]


class TransactionLien(AuditableModel):
    """
    Model to track liens placed on transactions by admin users.

    A lien prevents merchants from withdrawing the amount of a specific transaction
    until the lien is removed by an admin user. This is used for investigation purposes.
    """

    # Transaction being placed under lien
    transaction = models.OneToOneField(
        "transaction.Transaction",
        on_delete=models.PROTECT,
        related_name="lien",
        db_index=True,
        help_text="The transaction that has a lien placed on it",
    )

    # Admin user who placed the lien
    placed_by = models.ForeignKey(
        "user.User",
        on_delete=models.PROTECT,
        related_name="liens_placed",
        db_index=True,
        help_text="Admin user who placed the lien",
    )

    # Admin user who removed the lien (if removed)
    removed_by = models.ForeignKey(
        "user.User",
        on_delete=models.PROTECT,
        related_name="liens_removed",
        null=True,
        blank=True,
        db_index=True,
        help_text="Admin user who removed the lien",
    )

    # Lien status
    status = models.CharField(
        max_length=10,
        choices=TransactionLienStatus.CHOICES,
        default=TransactionLienStatus.ACTIVE,
        db_index=True,
        help_text="Current status of the lien",
    )

    amount = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        help_text="Amount under lien (matches transaction amount)",
    )

    placed_at = models.DateTimeField(
        auto_now_add=True, db_index=True, help_text="When the lien was placed"
    )

    removed_at = models.DateTimeField(
        null=True, blank=True, db_index=True, help_text="When the lien was removed"
    )

    class Meta:
        ordering = ["-placed_at"]
        verbose_name = "Transaction Lien"
        verbose_name_plural = "Transaction Liens"
        indexes = [
            models.Index(fields=["status", "placed_at"]),
            models.Index(fields=["transaction", "status"]),
            models.Index(fields=["placed_by", "placed_at"]),
        ]

    def __str__(self):
        return f"Lien on {self.transaction.reference} - {self.status} - {self.amount}"

    def is_active(self):
        """Check if the lien is currently active"""
        return self.status == TransactionLienStatus.ACTIVE

    def remove_lien(self, removed_by_user):
        """
        Remove the lien and update relevant fields

        Args:
            removed_by_user: User instance of the admin removing the lien
        """
        from django.utils import timezone

        self.status = TransactionLienStatus.REMOVED
        self.removed_by = removed_by_user
        self.removed_at = timezone.now()
        self.save(update_fields=["status", "removed_by", "removed_at", "updated_at"])

    @property
    def business(self):
        """Get the business associated with the transaction"""
        return self.transaction.business

    @property
    def wallet(self):
        """Get the wallet associated with the transaction"""
        return self.transaction.wallet

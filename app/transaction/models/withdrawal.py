from django.db import models
from transaction.models.base import VASTransaction


class WithdrawalVasTransaction(VASTransaction):
    """
    Model for withdrawal transactions where merchants withdraw funds
    from their wallets to their settlement accounts.
    """

    # Settlement account details (destination)
    settlement_account_number = models.Char<PERSON>ield(max_length=10)
    settlement_account_name = models.CharField(max_length=255)
    settlement_bank_name = models.CharField(max_length=255)
    settlement_bank_code = models.CharField(max_length=6)

    # Source wallet information
    source_wallet_type = models.CharField(
        max_length=30, help_text="Type of wallet funds are withdrawn from"
    )

    # External API response fields
    session_id = models.CharField(max_length=40, db_index=True, null=True, blank=True)
    transaction_id = models.Char<PERSON>ield(
        max_length=40, db_index=True, null=True, blank=True
    )

    # Requery fields for transaction status verification
    requery_response_code = models.Char<PERSON><PERSON>(
        max_length=5, help_text="Response code for requery", null=True, blank=True
    )
    requery_response_message = models.<PERSON><PERSON><PERSON><PERSON>(
        max_length=255, help_text="Response message for requery", null=True, blank=True
    )

    class Meta:
        db_table = "withdrawal_vas_transaction"
        verbose_name = "Withdrawal VAS Transaction"
        verbose_name_plural = "Withdrawal VAS Transactions"
        indexes = [
            models.Index(fields=["settlement_account_number"]),
            models.Index(fields=["source_wallet_type"]),
            models.Index(fields=["session_id"]),
            models.Index(fields=["transaction_id"]),
        ]

    def __str__(self):
        return f"Withdrawal {self.reference} - {self.settlement_account_number} - {self.status}"

from common.models import AuditableModel
from django.db import models
from transaction.enums import TransactionStatusEnum


class WemaPayoutHistory(AuditableModel):
    """
    Model to track Wema payout history for admin operations.
    This tracks the status of Wema fund transfers initiated by admin users.
    """

    # Transaction identification
    reference = models.Char<PERSON>ield(max_length=100, unique=True, db_index=True)
    wema_transaction_id = models.CharField(
        max_length=100, null=True, blank=True, db_index=True
    )

    # Recipient details
    recipient_account_number = models.Char<PERSON>ield(max_length=10)
    recipient_account_name = models.Char<PERSON><PERSON>(max_length=255)
    recipient_bank_name = models.Char<PERSON><PERSON>(max_length=255)
    recipient_bank_code = models.Char<PERSON>ield(max_length=6)

    # Transaction details
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    narration = models.TextField()

    # Status tracking
    status = models.Char<PERSON>ield(
        max_length=20,
        choices=TransactionStatusEnum.choices,
        default=TransactionStatusEnum.PENDING.value,
        db_index=True,
    )

    # Admin user who initiated the transaction
    initiated_by = models.ForeignKey(
        "user.User",
        on_delete=models.PROTECT,
        related_name="wema_payouts_initiated",
        db_index=True,
    )

    # Response data from Wema
    wema_response = models.JSONField(null=True, blank=True)
    wema_response_code = models.CharField(max_length=10, null=True, blank=True)
    wema_response_message = models.TextField(null=True, blank=True)

    # Additional metadata
    session_id = models.CharField(max_length=100, null=True, blank=True, db_index=True)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "Wema Payout History"
        verbose_name_plural = "Wema Payout Histories"
        indexes = [
            models.Index(fields=["status", "created_at"]),
            models.Index(fields=["initiated_by", "created_at"]),
        ]

    def __str__(self):
        return f"Wema Payout {self.reference} - {self.recipient_account_number} - {self.status}"

    def mark_as_successful(self, wema_response=None):
        """Mark the payout as successful"""
        self.status = TransactionStatusEnum.SUCCESSFUL.value
        if wema_response:
            self.wema_response = wema_response
        self.save(update_fields=["status", "wema_response", "updated_at"])

    def mark_as_failed(self, wema_response=None, error_message=None):
        """Mark the payout as failed"""
        self.status = TransactionStatusEnum.FAILED.value
        if wema_response:
            self.wema_response = wema_response
        if error_message:
            self.wema_response_message = error_message
        self.save(
            update_fields=[
                "status",
                "wema_response",
                "wema_response_message",
                "updated_at",
            ]
        )

    def mark_as_pending(self, wema_response=None):
        """Mark the payout as pending"""
        self.status = TransactionStatusEnum.PENDING.value
        if wema_response:
            self.wema_response = wema_response
        self.save(update_fields=["status", "wema_response", "updated_at"])

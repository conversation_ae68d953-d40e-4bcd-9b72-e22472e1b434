import logging
from decimal import Decimal

from audit.models import AuditLog
from audit.utils import log_user_action
from common.pagination import LargeDatasetKeySetPagination
from common.permissions import IsAdmin
from django.db.models import Count, Q, Sum
from django.db.models.functions import Coalesce
from django.http import Http404
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import OpenApiParameter, OpenApiTypes, extend_schema
from rest_framework import filters, generics, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from teams.enums import DefaultAdminRole, SystemBaseUserRole
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.models import CommissionTransaction, Transaction
from transaction.v1.filters import (
    CommissionTransactionOverviewFilter,
    TransactionFilter,
    TransactionOverviewFilter,
)
from transaction.v1.serializers import (
    CommissionTransactionOverviewSerializer,
    TailTransactionSerializer,
    TransactionOverviewSerializer,
    TransactionPolymorphicSerializer,
    TransactionSerializer,
    TransactionStatusUpdateSerializer,
)

logger = logging.getLogger(__name__)


class TransactionViewSet(viewsets.ModelViewSet):
    queryset = Transaction.objects.all().select_related("wallet", "business")
    permission_classes = [IsAuthenticated]
    serializer_class = TransactionSerializer
    http_method_names = ["get", "post"]
    lookup_field = "identifier"
    pagination_class = LargeDatasetKeySetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = TransactionFilter
    search_fields = ["reference", "merchant_reference"]
    ordering_fields = ["-created_at"]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == SystemBaseUserRole.BusinessOwner.value:
            return queryset.filter(business__owner=self.request.user)
        if self.request.user.role == DefaultAdminRole.RelationshipManager.value:
            return queryset.filter(relationship_manager=self.request.user.id)
        return queryset

    def get_object(self):
        lookup_value = self.kwargs.get(self.lookup_field)
        for field in ["id", "reference", "merchant_reference"]:
            try:
                return self.get_queryset().get(**{field: lookup_value})
            except Transaction.DoesNotExist:
                continue

        raise Http404("Transaction not found.")

    def list(self, request, *args, **kwargs):
        """List transactions with audit logging"""
        # Log transaction access
        log_user_action(
            request=request,
            user=request.user,
            action=AuditLog.API_CALL,
            description="Transaction list accessed",
            resource_type="Transaction",
            metadata={
                "endpoint": request.path,
                "method": request.method,
                "filters": dict(request.GET),
            },
        )
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        """Retrieve transaction with audit logging"""
        transaction = self.get_object()

        # Log transaction access
        log_user_action(
            request=request,
            user=request.user,
            action=AuditLog.API_CALL,
            description=f"Transaction {transaction.reference} accessed",
            resource_type="Transaction",
            resource_id=str(transaction.id),
            metadata={
                "transaction_reference": transaction.reference,
                "transaction_amount": str(transaction.amount),
                "endpoint": request.path,
            },
        )
        return super().retrieve(request, *args, **kwargs)

    def get_serializer_class(self):
        if self.action == "retrieve":
            return TransactionPolymorphicSerializer
        if self.action == "update_status":
            return TransactionStatusUpdateSerializer
        return super().get_serializer_class()

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @action(
        detail=True,
        methods=["post"],
        url_path="update-status",
        permission_classes=[IsAdmin],
    )
    def update_status(self, request, *args, **kwargs):
        transaction = self.get_object()
        old_status = transaction.status

        # Debug logging
        logger.info(
            f"View: Starting status update for transaction {transaction.reference} from {old_status}"
        )

        serializer = self.get_serializer(
            data=request.data,
            context={
                "transaction": transaction,
                "user": request.user,
            },
        )
        serializer.is_valid(raise_exception=True)
        transaction = serializer.save()

        # Debug logging after save
        logger.info(
            f"View: After serializer.save(), transaction status is {transaction.status}"
        )

        # Refresh from database to double-check
        transaction.refresh_from_db()
        logger.info(
            f"View: After refresh_from_db(), transaction status is {transaction.status}"
        )

        return Response(
            {
                "success": True,
                "message": f"Transaction status marked as {transaction.status.lower()}.",
            },
            status=status.HTTP_200_OK,
        )


class TransactionOverviewAPIView(generics.GenericAPIView):
    queryset = Transaction.objects.all().select_related("wallet", "business")
    serializer_class = TransactionOverviewSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = TransactionOverviewFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == SystemBaseUserRole.BusinessOwner.value:
            return queryset.filter(business__owner=self.request.user)
        return queryset

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="start",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="Start date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="end",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="End date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="txn_class",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionClassEnum.values(),
                description="Transaction Class",
            ),
        ],
        responses={200: TransactionOverviewSerializer},
    )
    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        filtered_queryset = self.filter_queryset(queryset)
        totals = filtered_queryset.aggregate(
            total_value=Sum("amount"),
            total_count=Count("id"),
            pending_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.PENDING.value)
            ),
            failed_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.FAILED.value)
            ),
            successful_count=Count(
                "id", filter=Q(status=TransactionStatusEnum.SUCCESSFUL.value)
            ),
        )

        response_data = {
            "total_value": totals.get("total_value", 0) or 0,
            "total_count": totals.get("total_count", 0),
            "pending_count": totals.get("pending_count", 0),
            "failed_count": totals.get("failed_count", 0),
            "successful_count": totals.get("successful_count", 0),
        }
        return Response(response_data, status=status.HTTP_200_OK)


class CommissionTransactionOverviewAPIView(generics.GenericAPIView):
    queryset = CommissionTransaction.objects.all().select_related("wallet", "business")
    serializer_class = TransactionOverviewSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = CommissionTransactionOverviewFilter
    filter_backends = [DjangoFilterBackend]

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.user.role == SystemBaseUserRole.BusinessOwner.value:
            return queryset.filter(business__owner=self.request.user)
        return queryset

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="start",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="Start date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="end",
                required=False,
                type=OpenApiTypes.DATE,
                location=OpenApiParameter.QUERY,
                description="End date in format YYYY-MM-DD",
            ),
            OpenApiParameter(
                name="txn_class",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionClassEnum.values(),
                description="Transaction Class",
            ),
        ],
        responses={200: CommissionTransactionOverviewSerializer},
    )
    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        filtered_queryset = self.filter_queryset(queryset)
        commission_totals = filtered_queryset.aggregate(
            total_commission=Coalesce(Sum("amount"), Decimal("0.00")),
            total_commission_count=Coalesce(Count("id"), 0),
        )

        return Response(
            {
                "total_commission": commission_totals["total_commission"],
                "total_commission_count": commission_totals["total_commission_count"],
            }
        )


class TailRecentTransactionsView(generics.GenericAPIView):
    serializer_class = TailTransactionSerializer
    permission_classes = [IsAdmin]
    filter_backends = [DjangoFilterBackend]
    filterset_class = TransactionFilter

    def get_queryset(self):
        return Transaction.objects.select_related("business").order_by("-created_at")

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="status",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionStatusEnum.values(),
                description="Transaction Status",
            ),
            OpenApiParameter(
                name="items_size",
                required=False,
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description="Item Size. Default: 10, Maximum: 30",
            ),
            OpenApiParameter(
                name="txn_class",
                required=False,
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                enum=TransactionClassEnum.values(),
                description="Transaction Class",
            ),
        ],
        responses={200: TailTransactionSerializer},
    )
    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        try:
            items_size = int(request.query_params.get("items_size", 10))
        except ValueError:
            items_size = 10

        items_size = max(1, min(items_size, 30))
        queryset = queryset[:items_size]

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

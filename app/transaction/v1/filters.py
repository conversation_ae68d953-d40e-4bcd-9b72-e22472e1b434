import django_filters
from common.filter import DateFilter
from transaction.models import CommissionTransaction, Transaction


class TransactionFilter(DateFilter):
    business_id = django_filters.CharFilter(
        field_name="business__id", lookup_expr="exact"
    )

    class Meta:
        model = Transaction
        fields = [
            "business_id",
            "status",
            "type",
            "provider",
            "txn_class",
        ]


class TransactionOverviewFilter(DateFilter):
    class Meta:
        model = Transaction
        fields = [
            "txn_class",
        ]


class CommissionFilter(DateFilter):
    business_id = django_filters.CharFilter(
        field_name="business__id", lookup_expr="exact"
    )

    class Meta:
        model = CommissionTransaction
        fields = [
            "business_id",
            "txn_class",
        ]


class CommissionTransactionOverviewFilter(DateFilter):
    class Meta:
        model = CommissionTransaction
        fields = [
            "txn_class",
        ]

import logging

from django.utils import timezone
from django.utils.timesince import timesince
from rest_framework import serializers
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.models import (
    AirtimeVASTransaction,
    BettingVASTransaction,
    CableTVVASTransaction,
    DataVASTransaction,
    EducationVASTransaction,
    ElectricityVASTransaction,
    EpinVASTransaction,
    KYCVASTransaction,
    Transaction,
    VirtualAccountVasTransaction,
)
from transaction.services import TransactionLienService

logger = logging.getLogger(__name__)


class TransactionSerializer(serializers.ModelSerializer):
    merchant_name = serializers.CharField(source="business.name", read_only=True)
    has_lien = serializers.SerializerMethodField()
    lien_amount = serializers.SerializerMethodField()
    lien_status = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = "__all__"

    def get_has_lien(self, obj):
        """Check if transaction has an active lien"""

        lien_service = TransactionLienService(obj)
        return lien_service.has_active_lien()

    def get_lien_amount(self, obj):
        """Get lien amount for the transaction"""
        lien_service = TransactionLienService(obj)
        return lien_service.get_lien_amount()

    def get_lien_status(self, obj):
        """Get lien status"""
        lien_service = TransactionLienService(obj)
        lien = lien_service.get_active_lien()
        return lien.status if lien else None


class TransactionOverviewSerializer(serializers.Serializer):
    total_value = serializers.FloatField()
    total_count = serializers.IntegerField()
    pending_count = serializers.IntegerField()
    failed_count = serializers.IntegerField()
    successful_count = serializers.IntegerField()


class CommissionTransactionOverviewSerializer(serializers.Serializer):
    total_commission = serializers.FloatField()
    total_commission_count = serializers.IntegerField()


class BaseVASTransactionSerializer(serializers.ModelSerializer):
    def to_representation(self, instance):
        data = super().to_representation(instance)
        business = instance.business
        data["merchant_name"] = business.name
        return data


class AirtimeVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = AirtimeVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.AIRTIME.value
        return data


class BettingVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = BettingVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.BETTING.value
        return data


class CableTVVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = CableTVVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.CABLE_TV.value
        return data


class DataVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = DataVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.DATA.value
        return data


class EpinVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = EpinVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.EPIN.value
        return data


class ElectricityVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = ElectricityVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.ELECTRICITY.value
        return data


class EducationVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = EducationVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.EDUCATION.value
        return data


class KYCVASTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = KYCVASTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.KYC.value
        return data


class VirtualAccountVasTransactionSerializer(BaseVASTransactionSerializer):
    class Meta:
        model = VirtualAccountVasTransaction
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["txn_class"] = TransactionClassEnum.VIRTUAL_ACCOUNT.value
        return data


TXN_CLASS_SERIALIZER_MAP = {
    TransactionClassEnum.AIRTIME.value: {
        "model": AirtimeVASTransaction,
        "serializer": AirtimeVASTransactionSerializer,
    },
    TransactionClassEnum.BETTING.value: {
        "model": BettingVASTransaction,
        "serializer": BettingVASTransactionSerializer,
    },
    TransactionClassEnum.CABLE_TV.value: {
        "model": CableTVVASTransaction,
        "serializer": CableTVVASTransactionSerializer,
    },
    TransactionClassEnum.DATA.value: {
        "model": DataVASTransaction,
        "serializer": DataVASTransactionSerializer,
    },
    TransactionClassEnum.ELECTRICITY.value: {
        "model": ElectricityVASTransaction,
        "serializer": ElectricityVASTransactionSerializer,
    },
    TransactionClassEnum.EPIN.value: {
        "model": EpinVASTransaction,
        "serializer": EpinVASTransactionSerializer,
    },
    TransactionClassEnum.EDUCATION.value: {
        "model": EducationVASTransaction,
        "serializer": EducationVASTransactionSerializer,
    },
    TransactionClassEnum.KYC.value: {
        "model": KYCVASTransaction,
        "serializer": KYCVASTransactionSerializer,
    },
    TransactionClassEnum.VIRTUAL_ACCOUNT.value: {
        "model": VirtualAccountVasTransaction,
        "serializer": VirtualAccountVasTransactionSerializer,
    },
}


class TransactionPolymorphicSerializer(serializers.ModelSerializer):
    has_lien = serializers.SerializerMethodField()
    lien_amount = serializers.SerializerMethodField()
    lien_status = serializers.SerializerMethodField()
    lien_placed_at = serializers.SerializerMethodField()
    lien_placed_by = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = "__all__"

    def get_has_lien(self, obj):
        """Check if transaction has an active lien"""
        from transaction.services import TransactionLienService

        lien_service = TransactionLienService(obj)
        return lien_service.has_active_lien()

    def get_lien_amount(self, obj):
        """Get lien amount for the transaction"""
        from transaction.services import TransactionLienService

        lien_service = TransactionLienService(obj)
        return lien_service.get_lien_amount()

    def get_lien_status(self, obj):
        """Get lien status"""
        from transaction.services import TransactionLienService

        lien_service = TransactionLienService(obj)
        lien = lien_service.get_active_lien()
        return lien.status if lien else None

    def get_lien_placed_at(self, obj):
        """Get when lien was placed"""
        from transaction.services import TransactionLienService

        lien_service = TransactionLienService(obj)
        lien = lien_service.get_active_lien()
        return lien.placed_at if lien else None

    def get_lien_placed_by(self, obj):
        """Get who placed the lien (only for admin users)"""
        request = self.context.get("request")
        if (
            request
            and hasattr(request.user, "role")
            and request.user.role in ["Admin", "Admin View Only"]
        ):
            from transaction.services import TransactionLienService

            lien_service = TransactionLienService(obj)
            lien = lien_service.get_active_lien()
            return lien.placed_by.email if lien else None
        return None

    def to_representation(self, instance):
        txn_class = getattr(instance, "txn_class", None)
        mapping = TXN_CLASS_SERIALIZER_MAP.get(txn_class)
        extra_data = {
            "old_balance": str(instance.old_balance),
            "new_balance": str(instance.new_balance),
            "provider": getattr(instance, "provider", None),
        }

        if mapping:
            model_cls = mapping["model"]
            serializer_cls = mapping["serializer"]

            detailed_instance = self._get_detailed_instance(model_cls, instance)

            if detailed_instance:
                data = serializer_cls(detailed_instance, context=self.context).data
                data["base_txn_id"] = str(instance.id)
                data.update(extra_data)
                return data

        data = TransactionSerializer(instance, context=self.context).data
        data["base_txn_id"] = str(instance.id)
        data.update(extra_data)
        return data

    def _get_detailed_instance(self, model_cls, instance):
        lookup_fields = ("id", "reference", "merchant_reference")
        for field in lookup_fields:
            value = getattr(instance, field, None)
            if value:
                try:
                    return model_cls.objects.get(**{field: value})
                except model_cls.DoesNotExist:
                    continue
        return None


class TailTransactionSerializer(serializers.ModelSerializer):
    merchant_name = serializers.CharField(source="business.name", default=None)
    timestamp = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = [
            "id",
            "merchant_name",
            "txn_class",
            "amount",
            "status",
            "reference",
            "merchant_reference",
            "narration",
            "timestamp",
        ]

    def get_timestamp(self, obj):
        now = timezone.now()
        delta = now - obj.created_at
        seconds = int(delta.total_seconds())

        if seconds < 60:
            return f"{seconds} seconds"
        return timesince(obj.created_at, now).split(",")[0]


class TransactionStatusUpdateSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=TransactionStatusEnum.choices())

    def validate(self, attrs):
        transaction = self.context["transaction"]
        txn_status = attrs["status"]
        if transaction.status == txn_status:
            raise serializers.ValidationError(
                f"Transaction already has a {txn_status.lower()} status."
            )
        return attrs

    def save(self, **kwargs):
        transaction: Transaction = self.context["transaction"]
        new_status = self.validated_data["status"]
        old_status = transaction.status

        # Debug logging
        logger.info(
            f"Updating transaction {transaction.reference} status from {old_status} to {new_status}"
        )

        transaction.status = new_status
        transaction.save()

        # Verify the save worked
        transaction.refresh_from_db()
        logger.info(
            f"After save and refresh: transaction {transaction.reference} status is {transaction.status}"
        )

        if transaction.status != new_status:
            logger.error(
                f"Status update failed! Expected {new_status}, got {transaction.status}"
            )

        # TODO: Create audit log

        return transaction

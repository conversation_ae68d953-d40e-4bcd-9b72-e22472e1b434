from common.pagination import LargeDatasetKeySetPagination
from django.db.models import Count, Q
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, viewsets
from rest_framework.decorators import action
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response

from .filters import AuditLogFilter
from .models import AuditLog
from .serializers import AuditLogListSerializer, AuditLogSerializer
from .utils import log_audit_log_access


class AdminAuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Admin-only ViewSet for viewing all audit logs across the platform

    Provides endpoints for:
    - Listing all audit logs with filtering and search
    - Retrieving individual audit log details
    - Getting platform-wide audit log statistics

    Access Control: Only Admin and Super_Admin roles
    """

    queryset = AuditLog.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = LargeDatasetKeySetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AuditLogFilter
    search_fields = [
        "user_email",
        "description",
        "ip_address",
        "user_name",
    ]
    ordering_fields = ["created_at", "action", "status", "user_email"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == "list":
            return AuditLogListSerializer
        return AuditLogSerializer

    def get_queryset(self):
        """
        Admin-only access - return all logs from all businesses
        """
        user = self.request.user

        # Strict admin-only access
        admin_roles = ["Admin", "Super_Admin"]
        if not (user.is_staff or user.role in admin_roles):
            # Return empty queryset for non-admin users
            return AuditLog.objects.none()

        return super().get_queryset()

    def list(self, request, *args, **kwargs):
        """
        List audit logs with access logging
        """
        # Log audit log access
        log_audit_log_access(
            request=request,
            user=request.user,
            filter_params=dict(request.GET),
        )

        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def stats(self, request):
        """
        Get audit log statistics for the current user's accessible logs
        """
        queryset = self.get_queryset()

        # Get basic stats
        total_logs = queryset.count()
        successful_logs = queryset.filter(status=AuditLog.SUCCESS).count()
        failed_logs = queryset.filter(status=AuditLog.FAILED).count()

        # Get action breakdown
        action_stats = (
            queryset.values("action")
            .annotate(count=Count("id"))
            .order_by("-count")[:10]
        )

        stats_data = {
            "total_logs": total_logs,
            "successful_logs": successful_logs,
            "failed_logs": failed_logs,
            "success_rate": (
                round((successful_logs / total_logs) * 100, 2) if total_logs > 0 else 0
            ),
            "top_actions": [
                {
                    "action": stat["action"],
                    "action_display": dict(AuditLog.ACTION_CHOICES).get(
                        stat["action"], stat["action"]
                    ),
                    "count": stat["count"],
                }
                for stat in action_stats
            ],
        }

        # Log stats access
        log_audit_log_access(
            request=request,
            user=request.user,
            filter_params={"action": "stats"},
        )

        return Response(stats_data)


class BusinessAuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Business-only ViewSet for viewing business-related audit logs

    Provides endpoints for:
    - Listing business-related audit logs with filtering and search
    - Retrieving individual audit log details (business-related only)
    - Getting business-specific audit log statistics

    Access Control: Business owners and team members can see logs from their business only
    """

    queryset = AuditLog.objects.select_related("user").all()
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = LargeDatasetKeySetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AuditLogFilter
    search_fields = [
        "user_email",
        "description",
        "ip_address",
        "user_name",
    ]
    ordering_fields = ["created_at", "action", "status"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == "list":
            return AuditLogListSerializer
        return AuditLogSerializer

    def get_queryset(self):
        """
        Filter to business-related logs only

        Business owners and team members can see:
        1. Their own logs
        2. Logs from other team members in the same business
        3. Business-related actions (even if performed by system/admin on their business)
        """
        user = self.request.user
        queryset = super().get_queryset()

        # Only allow business users (not admins)
        admin_roles = ["Admin", "Super_Admin"]
        if user.is_staff or user.role in admin_roles:

            return AuditLog.objects.none()

        if not hasattr(user, "business") or not user.business:
            return queryset.filter(user_id=str(user.id))

        # Get all users from the same business
        business_user_ids = [user.business.owner.id]

        # Add team members from core team members system
        # Note: The old BusinessMember model was migrated to TeamMember in teams app
        core_team_members = user.business.core_team_members.filter(
            status="Active"
        ).values_list("user_id", flat=True)
        business_user_ids.extend(list(core_team_members))

        business_user_ids = list(set(business_user_ids))

        # Filter logs to business-related users and business-related actions
        # This includes:
        # 1. Logs from users in the same business
        # 2. Logs that have business context matching their
        #    business(for example,admin user did something on a business account like KYC approval )
        business_user_id_strs = [str(uid) for uid in business_user_ids]
        return queryset.filter(
            Q(user_id__in=business_user_id_strs)
            | Q(metadata__business_id=str(user.business.id))
        )

    def list(self, request, *args, **kwargs):
        """
        List business audit logs with access logging
        """
        log_audit_log_access(
            request=request,
            user=request.user,
            filter_params=dict(request.GET),
        )

        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=["get"])
    def stats(self, request):
        """
        Get audit log statistics for the current business
        """
        queryset = self.get_queryset()

        total_logs = queryset.count()
        successful_logs = queryset.filter(status=AuditLog.SUCCESS).count()
        failed_logs = queryset.filter(status=AuditLog.FAILED).count()

        # Get action breakdown
        action_stats = (
            queryset.values("action")
            .annotate(count=Count("id"))
            .order_by("-count")[:10]
        )

        stats_data = {
            "total_logs": total_logs,
            "successful_logs": successful_logs,
            "failed_logs": failed_logs,
            "success_rate": (
                round((successful_logs / total_logs) * 100, 2) if total_logs > 0 else 0
            ),
            "top_actions": [
                {
                    "action": stat["action"],
                    "action_display": dict(AuditLog.ACTION_CHOICES).get(
                        stat["action"], stat["action"]
                    ),
                    "count": stat["count"],
                }
                for stat in action_stats
            ],
        }

        # Log stats access
        log_audit_log_access(
            request=request,
            user=request.user,
            filter_params={"action": "stats"},
        )

        return Response(stats_data)

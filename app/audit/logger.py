"""
Audit Logger - Asynchronous audit logging utilities
Matches the pattern from your previous application
"""

from typing import Dict, Optional

from audit.models import AuditLog
from audit.tasks import (
    log_api_call,
    log_audit_event_task,
    log_audit_event_with_retry,
    log_system_action,
)
from django.contrib.auth import get_user_model
from django.http import HttpRequest

User = get_user_model()


class AuditLogger:
    """
    Centralized audit logging utility with async Celery support
    """

    @staticmethod
    def log_async(
        user: Optional[User] = None,
        action: str = None,
        description: str = None,
        ip_address: str = None,
        user_agent: str = None,
        status: str = AuditLog.SUCCESS,
        resource_type: str = None,
        resource_id: str = None,
        old_values: Dict = None,
        new_values: Dict = None,
        metadata: Dict = None,
        session_id: str = None,
        request_id: str = None,
        audit_type: str = None,
        critical: bool = False,
    ) -> None:
        """
        Log audit event asynchronously using Celery

        Args:
            user: User object (optional)
            action: Action performed
            description: Description of the action
            ip_address: IP address of the request
            user_agent: User agent string
            status: Status of the action (SUCCESS, FAILED, PENDING)
            resource_type: Type of resource affected
            resource_id: ID of the resource affected
            old_values: Previous values (for updates)
            new_values: New values (for updates)
            metadata: Additional metadata
            session_id: Session ID
            request_id: Request ID for tracing
            audit_type: Type of audit event
            critical: Whether to use retry logic for critical events
        """
        # Prepare payload
        payload = {
            "action": action,
            "description": description,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "status": status,
            "resource_type": resource_type,
            "resource_id": resource_id,
            "old_values": old_values,
            "new_values": new_values,
            "metadata": metadata,
            "session_id": session_id,
            "request_id": request_id,
            "audit_type": audit_type or action,
        }

        # Add user information
        if user:
            payload.update(
                {
                    "user_id": str(user.id),
                    "user_email": user.email,
                    "user_name": getattr(user, "fullname", None)
                    or f"{getattr(user, 'firstname', '')} {getattr(user, 'lastname', '')}".strip()
                    or user.email.split("@")[0],
                }
            )
        else:
            payload.update(
                {
                    "user_id": None,
                    "user_email": "system@local",
                    "user_name": "System",
                }
            )

        # Remove None values
        payload = {k: v for k, v in payload.items() if v is not None}

        # Choose task based on criticality
        if critical:
            log_audit_event_with_retry.delay(payload)
        else:
            log_audit_event_task.delay(payload)

    @staticmethod
    def log_sync(
        user: Optional[User] = None,
        action: str = None,
        description: str = None,
        ip_address: str = None,
        user_agent: str = None,
        status: str = AuditLog.SUCCESS,
        resource_type: str = None,
        resource_id: str = None,
        old_values: Dict = None,
        new_values: Dict = None,
        metadata: Dict = None,
        session_id: str = None,
        request_id: str = None,
        audit_type: str = None,
    ) -> AuditLog:
        """
        Log audit event synchronously (for critical operations that need immediate logging)
        """
        return AuditLog.log_action(
            user=user,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            status=status,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            metadata=metadata,
            session_id=session_id,
            request_id=request_id,
            audit_type=audit_type,
        )

    @staticmethod
    def log_from_request(
        request: HttpRequest,
        action: str,
        description: str = None,
        status: str = AuditLog.SUCCESS,
        resource_type: str = None,
        resource_id: str = None,
        old_values: Dict = None,
        new_values: Dict = None,
        metadata: Dict = None,
        critical: bool = False,
    ) -> None:
        """
        Log audit event from Django request object
        """
        user = (
            getattr(request, "user", None)
            if hasattr(request, "user") and request.user.is_authenticated
            else None
        )

        AuditLogger.log_async(
            user=user,
            action=action,
            description=description,
            ip_address=get_client_ip(request),
            user_agent=request.META.get("HTTP_USER_AGENT"),
            status=status,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            metadata=metadata,
            session_id=(
                request.session.session_key if hasattr(request, "session") else None
            ),
            request_id=getattr(request, "id", None),
            critical=critical,
        )

    @staticmethod
    def log_login(user: User, request: HttpRequest, success: bool = True) -> None:
        """Log user login attempt"""
        AuditLogger.log_from_request(
            request=request,
            action=AuditLog.LOGIN,
            description=f"User login {'successful' if success else 'failed'}",
            status=AuditLog.SUCCESS if success else AuditLog.FAILED,
            critical=True,  # Login events are critical
        )

    @staticmethod
    def log_logout(user: User, request: HttpRequest) -> None:
        """Log user logout"""
        AuditLogger.log_from_request(
            request=request,
            action=AuditLog.LOGOUT,
            description="User logged out",
            status=AuditLog.SUCCESS,
        )

    @staticmethod
    def log_api_call_async(
        user: User,
        endpoint: str,
        method: str,
        ip_address: str = None,
        user_agent: str = None,
        status_code: int = 200,
        metadata: Dict = None,
    ) -> None:
        """Log API call asynchronously"""
        log_api_call.delay(
            user_id=str(user.id),
            user_email=user.email,
            user_name=getattr(user, "fullname", None)
            or f"{getattr(user, 'firstname', '')} {getattr(user, 'lastname', '')}".strip()
            or user.email.split("@")[0],
            endpoint=endpoint,
            method=method,
            ip_address=ip_address,
            user_agent=user_agent,
            status=AuditLog.SUCCESS if status_code < 400 else AuditLog.FAILED,
            metadata=metadata,
        )

    @staticmethod
    def log_system_event(
        action: str,
        description: str,
        status: str = AuditLog.SUCCESS,
        metadata: Dict = None,
    ) -> None:
        """Log system-level events"""
        log_system_action.delay(
            action=action, description=description, status=status, metadata=metadata
        )


def get_client_ip(request: HttpRequest) -> str:
    """Extract client IP address from request"""
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


# Convenience functions for common audit events
def log_user_login(user: User, request: HttpRequest, success: bool = True) -> None:
    """Convenience function for logging user login"""
    AuditLogger.log_login(user, request, success)


def log_user_logout(user: User, request: HttpRequest) -> None:
    """Convenience function for logging user logout"""
    AuditLogger.log_logout(user, request)


def log_transaction_event(
    user: User,
    action: str,
    transaction_id: str,
    amount: float = None,
    status: str = AuditLog.SUCCESS,
    metadata: Dict = None,
) -> None:
    """Convenience function for logging transaction events"""
    AuditLogger.log_async(
        user=user,
        action=action,
        description=f"Transaction {action.lower()}",
        resource_type="TRANSACTION",
        resource_id=transaction_id,
        status=status,
        metadata={**(metadata or {}), "amount": amount} if amount else metadata,
        critical=True,  # Transaction events are critical
    )

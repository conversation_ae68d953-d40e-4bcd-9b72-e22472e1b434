from datetime import datetime, time, timedelta

import django_filters
from django.utils import timezone

from .models import AuditLog


class AuditLogFilter(django_filters.FilterSet):
    """
    Filter class for AuditLog model
    """

    # Date range filters
    start = django_filters.DateFilter(
        field_name="created_at",
        lookup_expr="gte",
        help_text="Start date for filtering (YYYY-MM-DD) - includes entire day",
    )
    end = django_filters.DateFilter(
        field_name="created_at",
        method="filter_end_date",
        help_text="End date for filtering (YYYY-MM-DD) - includes entire day",
    )

    # Date shortcuts
    last_24h = django_filters.BooleanFilter(
        method="filter_last_24h", help_text="Filter logs from last 24 hours"
    )
    last_7d = django_filters.BooleanFilter(
        method="filter_last_7d", help_text="Filter logs from last 7 days"
    )
    last_30d = django_filters.BooleanFilter(
        method="filter_last_30d", help_text="Filter logs from last 30 days"
    )

    # Action filters
    action = django_filters.ChoiceFilter(
        choices=AuditLog.ACTION_CHOICES, help_text="Filter by action type"
    )
    actions = django_filters.MultipleChoiceFilter(
        field_name="action",
        choices=AuditLog.ACTION_CHOICES,
        help_text="Filter by multiple action types",
    )

    # Status filters
    status = django_filters.ChoiceFilter(
        choices=AuditLog.STATUS_CHOICES, help_text="Filter by status"
    )

    # User filters
    email = django_filters.CharFilter(
        field_name="user_email",
        lookup_expr="icontains",
        help_text="Filter by user email (partial match)",
    )
    user_id = django_filters.CharFilter(
        field_name="user_id", help_text="Filter by user ID"
    )

    # IP address filter
    ip_address = django_filters.CharFilter(
        lookup_expr="icontains", help_text="Filter by IP address (partial match)"
    )

    # Resource filters
    resource_type = django_filters.CharFilter(
        lookup_expr="icontains", help_text="Filter by resource type"
    )
    resource_id = django_filters.CharFilter(help_text="Filter by resource ID")

    # Success/failure filters
    successful_only = django_filters.BooleanFilter(
        method="filter_successful_only", help_text="Show only successful actions"
    )
    failed_only = django_filters.BooleanFilter(
        method="filter_failed_only", help_text="Show only failed actions"
    )

    class Meta:
        model = AuditLog
        fields = [
            "action",
            "status",
            "email",
            "user_id",
            "ip_address",
            "resource_type",
            "resource_id",
            "start",
            "end",
            "last_24h",
            "last_7d",
            "last_30d",
            "actions",
            "successful_only",
            "failed_only",
        ]

    def filter_last_24h(self, queryset, name, value):
        if value:
            start_time = timezone.now() - timedelta(hours=24)
            return queryset.filter(created_at__gte=start_time)
        return queryset

    def filter_last_7d(self, queryset, name, value):
        if value:
            start_time = timezone.now() - timedelta(days=7)
            return queryset.filter(created_at__gte=start_time)
        return queryset

    def filter_last_30d(self, queryset, name, value):
        if value:
            start_time = timezone.now() - timedelta(days=30)
            return queryset.filter(created_at__gte=start_time)
        return queryset

    def filter_successful_only(self, queryset, name, value):
        if value:
            return queryset.filter(status=AuditLog.SUCCESS)
        return queryset

    def filter_failed_only(self, queryset, name, value):
        if value:
            return queryset.filter(status=AuditLog.FAILED)
        return queryset

    def filter_end_date(self, queryset, name, value):
        """Filter end date with proper end-of-day handling for inclusive filtering"""
        if value:
            # Convert date to end of day for inclusive filtering
            end_of_day = datetime.combine(value, time.max)
            end_of_day = (
                timezone.make_aware(end_of_day)
                if timezone.is_naive(end_of_day)
                else end_of_day
            )
            return queryset.filter(created_at__lte=end_of_day)
        return queryset

import uuid

from django.utils import timezone

from .models import AuditLog


def get_client_ip(request):
    """
    Get the client IP address from the request
    """
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def get_user_agent(request):
    """
    Get the user agent from the request
    """
    return request.META.get("HTTP_USER_AGENT", "")


def generate_request_id():
    """
    Generate a unique request ID for tracing
    """
    return str(uuid.uuid4())


def log_user_action(
    request,
    user=None,
    action=None,
    description=None,
    status=AuditLog.SUCCESS,
    resource_type=None,
    resource_id=None,
    old_values=None,
    new_values=None,
    metadata=None,
):
    """
    Utility function to log user actions

    Args:
        request: Django request object
        user: User instance (optional, will try to get from request)
        action: Action type (from AuditLog.ACTION_CHOICES)
        description: Description of the action
        status: Status of the action (SUCCESS, FAILED, PENDING)
        resource_type: Type of resource affected
        resource_id: ID of the resource affected
        old_values: Previous values (for updates)
        new_values: New values (for updates)
        metadata: Additional metadata

    Returns:
        AuditLog instance
    """
    # Get user from request if not provided
    if not user and hasattr(request, "user") and request.user.is_authenticated:
        user = request.user

    # Get email
    email = user.email if user else "<EMAIL>"

    # Get IP address and user agent
    ip_address = get_client_ip(request)
    user_agent = get_user_agent(request)

    # Generate request ID
    request_id = generate_request_id()

    session_id = request.session.session_key if hasattr(request, "session") else None

    return AuditLog.log_action(
        user=user,
        email=email,
        action=action,
        description=description,
        ip_address=ip_address,
        user_agent=user_agent,
        status=status,
        resource_type=resource_type,
        resource_id=resource_id,
        old_values=old_values,
        new_values=new_values,
        metadata=metadata,
        session_id=session_id,
        request_id=request_id,
    )


def log_login_attempt(request, user=None, success=True, reason=None):
    """
    Log login attempts
    """
    status = AuditLog.SUCCESS if success else AuditLog.FAILED
    description = "User logged in successfully"
    if not success:
        description = f"Login failed: {reason}" if reason else "Login failed"

    metadata = {
        "login_method": "email_password",
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.LOGIN,
        description=description,
        status=status,
        resource_type="User",
        resource_id=user.id if user else None,
        metadata=metadata,
    )


def log_logout(request, user=None):
    """
    Log logout actions
    """
    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.LOGOUT,
        description="User logged out",
        resource_type="User",
        resource_id=user.id if user else None,
    )


def log_registration(request, user=None, registration_type="email"):
    """
    Log user registration
    """
    metadata = {
        "registration_type": registration_type,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.REGISTER,
        # status=AuditLog.SUCCESS if user else AuditLog.FAILED,
        description=f"New user registered via {registration_type}",
        resource_type="User",
        resource_id=user.id if user else None,
        metadata=metadata,
    )


def log_password_change(request, user=None, success=True):
    """
    Log password change attempts
    """
    status = AuditLog.SUCCESS if success else AuditLog.FAILED
    description = (
        "Password changed successfully" if success else "Password change failed"
    )

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.PASSWORD_CHANGE,
        description=description,
        status=status,
        resource_type="User",
        resource_id=user.id if user else None,
    )


def log_profile_update(request, user=None, old_data=None, new_data=None):
    """
    Log profile updates
    """
    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.PROFILE_UPDATE,
        description="User profile updated",
        resource_type="User",
        resource_id=user.id if user else None,
        old_values=old_data,
        new_values=new_data,
    )


def log_transaction_action(
    request, user=None, action_type="CREATE", transaction=None, description=None
):
    """
    Log transaction-related actions
    """
    action = (
        AuditLog.TRANSACTION_CREATE
        if action_type == "CREATE"
        else AuditLog.TRANSACTION_UPDATE
    )

    if not description:
        description = f"Transaction {action_type.lower()}"

    return log_user_action(
        request=request,
        user=user,
        action=action,
        description=description,
        resource_type="Transaction",
        resource_id=transaction.id if transaction else None,
        metadata={
            "transaction_reference": transaction.reference if transaction else None,
            "amount": str(transaction.amount) if transaction else None,
        },
    )


def log_otp_action(request, user=None, action_type="REQUEST", success=True):
    """
    Log OTP-related actions
    """
    action = AuditLog.OTP_REQUEST if action_type == "REQUEST" else AuditLog.OTP_VERIFY
    status = AuditLog.SUCCESS if success else AuditLog.FAILED

    description = f"OTP {action_type.lower()} "
    description += "successful" if success else "failed"

    return log_user_action(
        request=request,
        user=user,
        action=action,
        description=description,
        status=status,
        resource_type="User",
        resource_id=user.id if user else None,
    )


def log_admin_login(request, user=None, success=True, reason=None):
    """
    Log admin login attempts (differentiated from regular login)
    """
    status = AuditLog.SUCCESS if success else AuditLog.FAILED
    description = "Admin logged in successfully"
    if not success:
        description = (
            f"Admin login failed: {reason}" if reason else "Admin login failed"
        )

    metadata = {
        "login_method": "admin_portal",
        "user_role": user.role if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.ADMIN_LOGIN,
        description=description,
        status=status,
        resource_type="User",
        resource_id=user.id if user else None,
        metadata=metadata,
    )


def log_admin_logout(request, user=None):
    """
    Log admin logout actions
    """
    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.ADMIN_LOGOUT,
        description="Admin logged out",
        resource_type="User",
        resource_id=user.id if user else None,
        metadata={"user_role": user.role if user else None},
    )


def log_api_key_generation(request, user=None, business=None, key_type="private"):
    """
    Log API key generation
    """
    description = f"API {key_type} key generated"
    metadata = {
        "key_type": key_type,
        "business_id": business.id if business else None,
        "business_name": business.name if business else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.API_KEY_GENERATE,
        description=description,
        resource_type="APIConfig",
        resource_id=business.id if business else None,
        metadata=metadata,
    )


def log_api_key_revocation(request, user=None, business=None, key_type="private"):
    """
    Log API key revocation
    """
    description = f"API {key_type} key revoked"
    metadata = {
        "key_type": key_type,
        "business_id": business.id if business else None,
        "business_name": business.name if business else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.API_KEY_REVOKE,
        description=description,
        resource_type="APIConfig",
        resource_id=business.id if business else None,
        metadata=metadata,
    )


def log_dispute_creation(request, user=None, dispute=None):
    """
    Log transaction dispute creation
    """
    description = (
        f"Transaction dispute raised for {dispute.transaction_reference}"
        if dispute
        else "Transaction dispute raised"
    )
    metadata = {
        "dispute_id": dispute.id if dispute else None,
        "transaction_reference": dispute.transaction_reference if dispute else None,
        "amount": str(dispute.amount) if dispute else None,
        "vas_service": dispute.vas_service if dispute else None,
        "business_id": dispute.business.id if dispute and dispute.business else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.DISPUTE_CREATE,
        description=description,
        resource_type="Dispute",
        resource_id=dispute.id if dispute else None,
        metadata=metadata,
    )


def log_dispute_resolution(request, user=None, dispute=None, resolution_notes=None):
    """
    Log dispute resolution
    """
    description = (
        f"Dispute resolved for {dispute.transaction_reference}"
        if dispute
        else "Dispute resolved"
    )
    metadata = {
        "dispute_id": dispute.id if dispute else None,
        "transaction_reference": dispute.transaction_reference if dispute else None,
        "amount": str(dispute.amount) if dispute else None,
        "business_id": dispute.business.id if dispute and dispute.business else None,
        "resolution_notes": resolution_notes,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.DISPUTE_RESOLVE,
        description=description,
        resource_type="Dispute",
        resource_id=dispute.id if dispute else None,
        metadata=metadata,
    )


def log_merchant_onboarding(request, user=None, business=None, onboarded_by=None):
    """
    Log merchant onboarding completion
    """
    description = (
        f"Merchant onboarded: {business.name}" if business else "Merchant onboarded"
    )
    metadata = {
        "business_id": business.id if business else None,
        "business_name": business.name if business else None,
        "business_email": business.email if business else None,
        "onboarded_by": onboarded_by.email if onboarded_by else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=onboarded_by or user,
        action=AuditLog.BUSINESS_ONBOARD,
        description=description,
        resource_type="Business",
        resource_id=business.id if business else None,
        metadata=metadata,
    )


def log_merchant_update(
    request, user=None, business=None, old_data=None, new_data=None
):
    """
    Log merchant/business updates
    """
    description = (
        f"Merchant updated: {business.name}" if business else "Merchant updated"
    )
    metadata = {
        "business_id": business.id if business else None,
        "business_name": business.name if business else None,
        "updated_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.BUSINESS_UPDATE,
        description=description,
        resource_type="Business",
        resource_id=business.id if business else None,
        old_values=old_data,
        new_values=new_data,
        metadata=metadata,
    )


def log_role_update(request, user=None, target_user=None, old_role=None, new_role=None):
    """
    Log user role updates
    """
    description = (
        f"User role updated: {target_user.email} from {old_role} to {new_role}"
        if target_user
        else "User role updated"
    )
    metadata = {
        "target_user_id": target_user.id if target_user else None,
        "target_user_email": target_user.email if target_user else None,
        "old_role": old_role,
        "new_role": new_role,
        "updated_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.ROLE_UPDATE,
        description=description,
        resource_type="User",
        resource_id=target_user.id if target_user else None,
        old_values={"role": old_role},
        new_values={"role": new_role},
        metadata=metadata,
    )


def log_provider_addition(request, user=None, provider_name=None, provider_type=None):
    """
    Log new provider addition
    """
    description = (
        f"New provider added: {provider_name}"
        if provider_name
        else "New provider added"
    )
    metadata = {
        "provider_name": provider_name,
        "provider_type": provider_type,
        "added_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.PROVIDER_ADD,
        description=description,
        resource_type="Provider",
        resource_id=provider_name,
        metadata=metadata,
    )


def log_provider_switch(
    request, user=None, old_provider=None, new_provider=None, service_type=None
):
    """
    Log provider switching
    """
    description = (
        f"Provider switched from {old_provider} to {new_provider}"
        if old_provider and new_provider
        else "Provider switched"
    )
    metadata = {
        "old_provider": old_provider,
        "new_provider": new_provider,
        "service_type": service_type,
        "switched_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.PROVIDER_SWITCH,
        description=description,
        resource_type="Provider",
        resource_id=new_provider,
        old_values={"provider": old_provider},
        new_values={"provider": new_provider},
        metadata=metadata,
    )


def log_fee_structure_edit(
    request, user=None, fee_type=None, old_values=None, new_values=None
):
    """
    Log fee structure edits
    """
    description = (
        f"Fee structure edited: {fee_type}" if fee_type else "Fee structure edited"
    )
    metadata = {
        "fee_type": fee_type,
        "edited_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.FEE_STRUCTURE_EDIT,
        description=description,
        resource_type="FeeStructure",
        resource_id=fee_type,
        old_values=old_values,
        new_values=new_values,
        metadata=metadata,
    )


def log_transaction_reversal(request, user=None, transaction=None, reason=None):
    """
    Log transaction reversals
    """
    description = (
        f"Transaction reversed: {transaction.reference}"
        if transaction
        else "Transaction reversed"
    )
    metadata = {
        "transaction_id": transaction.id if transaction else None,
        "transaction_reference": transaction.reference if transaction else None,
        "amount": str(transaction.amount) if transaction else None,
        "reason": reason,
        "reversed_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.TRANSACTION_REVERSE,
        description=description,
        resource_type="Transaction",
        resource_id=transaction.id if transaction else None,
        metadata=metadata,
    )


def log_wallet_funding(request, user=None, wallet=None, amount=None, funding_type=None):
    """
    Log wallet funding activities
    """
    description = f"Wallet funded: {amount}" if amount else "Wallet funded"
    metadata = {
        "wallet_id": wallet.id if wallet else None,
        "amount": str(amount) if amount else None,
        "funding_type": funding_type,
        "funded_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.WALLET_FUNDING,
        description=description,
        resource_type="Wallet",
        resource_id=wallet.id if wallet else None,
        metadata=metadata,
    )


def log_system_settings_change(
    request, user=None, setting_name=None, old_value=None, new_value=None
):
    """
    Log system settings changes
    """
    description = (
        f"System setting changed: {setting_name}"
        if setting_name
        else "System setting changed"
    )
    metadata = {
        "setting_name": setting_name,
        "changed_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.SYSTEM_CONFIG,
        description=description,
        resource_type="SystemSetting",
        resource_id=setting_name,
        old_values={"value": old_value},
        new_values={"value": new_value},
        metadata=metadata,
    )


def log_audit_log_access(
    request, user=None, accessed_user_email=None, filter_params=None
):
    """
    Log audit log access
    """
    description = (
        f"Audit logs accessed for {accessed_user_email}"
        if accessed_user_email
        else "Audit logs accessed"
    )
    metadata = {
        "accessed_by": user.email if user else None,
        "accessed_user_email": accessed_user_email,
        "filter_params": filter_params,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.AUDIT_LOG_ACCESS,
        description=description,
        resource_type="AuditLog",
        resource_id=accessed_user_email,
        metadata=metadata,
    )


def log_lien_placed(request, user=None, transaction=None, lien=None):
    """
    Log when a lien is placed on a transaction
    """
    description = (
        f"Lien placed on transaction {transaction.reference} for amount {lien.amount}"
    )

    metadata = {
        "transaction_reference": transaction.reference,
        "transaction_id": str(transaction.id),
        "business_name": transaction.business.name,
        "business_email": transaction.business.email,
        "wallet_type": transaction.wallet.type,
        "lien_amount": str(lien.amount),
        "lien_id": str(lien.id),
        "placed_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.LIEN_PLACE,
        description=description,
        resource_type="TransactionLien",
        resource_id=str(lien.id),
        metadata=metadata,
    )


def log_lien_removed(request, user=None, transaction=None, lien=None):
    """
    Log when a lien is removed from a transaction
    """
    description = f"Lien removed from transaction {transaction.reference} for amount {lien.amount}"

    metadata = {
        "transaction_reference": transaction.reference,
        "transaction_id": str(transaction.id),
        "business_name": transaction.business.name,
        "business_email": transaction.business.email,
        "wallet_type": transaction.wallet.type,
        "lien_amount": str(lien.amount),
        "lien_id": str(lien.id),
        "placed_by": lien.placed_by.email if lien.placed_by else None,
        "removed_by": user.email if user else None,
        "placed_at": lien.placed_at.isoformat() if lien.placed_at else None,
        "removed_at": lien.removed_at.isoformat() if lien.removed_at else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.LIEN_REMOVE,
        description=description,
        resource_type="TransactionLien",
        resource_id=str(lien.id),
        metadata=metadata,
    )


def log_vas_transaction(request, user=None, transaction=None, action_type="CREATE"):
    """
    Log VAS transaction actions
    """
    action = AuditLog.VAS_TRANSACTION
    description = f"VAS transaction {action_type.lower()}"

    if transaction:
        description = f"VAS transaction {transaction.reference} {action_type.lower()}"

    metadata = {
        "action_type": action_type,
        "timestamp": timezone.now().isoformat(),
    }

    if transaction:
        metadata.update(
            {
                "transaction_reference": getattr(transaction, "reference", None),
                "transaction_class": getattr(transaction, "transaction_class", None),
                "amount": str(getattr(transaction, "amount", 0)),
                "status": getattr(transaction, "status", None),
            }
        )

    return log_user_action(
        request=request,
        user=user,
        action=action,
        description=description,
        resource_type="VASTransaction",
        resource_id=str(transaction.id) if transaction else None,
        metadata=metadata,
    )


def log_virtual_account_create(request, user=None, virtual_account=None):
    """
    Log virtual account creation
    """
    description = "Virtual account created"
    if virtual_account:
        description = f"Virtual account {virtual_account.account_number} created"

    metadata = {
        "timestamp": timezone.now().isoformat(),
    }

    if virtual_account:
        metadata.update(
            {
                "account_number": virtual_account.account_number,
                "bank_name": virtual_account.bank_name,
                "business_id": (
                    str(virtual_account.business.id)
                    if virtual_account.business
                    else None
                ),
            }
        )

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.VIRTUAL_ACCOUNT_CREATE,
        description=description,
        resource_type="VirtualAccount",
        resource_id=str(virtual_account.id) if virtual_account else None,
        metadata=metadata,
    )


def log_api_key_action(request, user=None, action_type="GENERATE", api_key_id=None):
    """
    Log API key generation/revocation
    """
    action = (
        AuditLog.API_KEY_GENERATE
        if action_type == "GENERATE"
        else AuditLog.API_KEY_REVOKE
    )
    description = f"API key {action_type.lower()}"

    metadata = {
        "action_type": action_type,
        "timestamp": timezone.now().isoformat(),
        "api_key_id": api_key_id,
    }

    return log_user_action(
        request=request,
        user=user,
        action=action,
        description=description,
        resource_type="APIKey",
        resource_id=api_key_id,
        metadata=metadata,
    )


def log_team_member_action(request, user=None, team_member=None, action_type="INVITE"):
    """
    Log team member actions (invite, activate, deactivate)
    """
    action_map = {
        "INVITE": AuditLog.TEAM_MEMBER_INVITE,
        "ACTIVATE": AuditLog.TEAM_MEMBER_ACTIVATE,
        "DEACTIVATE": AuditLog.TEAM_MEMBER_DEACTIVATE,
    }

    action = action_map.get(action_type, AuditLog.TEAM_MEMBER_INVITE)
    description = f"Team member {action_type.lower()}"

    if team_member:
        member_email = getattr(team_member, "email", None) or getattr(
            team_member, "user", {}
        ).get("email", "Unknown")
        description = f"Team member {member_email} {action_type.lower()}"

    metadata = {
        "action_type": action_type,
        "timestamp": timezone.now().isoformat(),
    }

    if team_member:
        metadata.update(
            {
                "member_email": getattr(team_member, "email", None),
                "member_role": getattr(team_member, "role", None),
            }
        )

    return log_user_action(
        request=request,
        user=user,
        action=action,
        description=description,
        resource_type="TeamMember",
        resource_id=str(team_member.id) if team_member else None,
        metadata=metadata,
    )


def log_team_member_invitation(
    request, user=None, invited_email=None, role=None, business=None
):
    """
    Log team member invitations
    """
    description = (
        f"Team member invited: {invited_email} as {role}"
        if invited_email
        else "Team member invited"
    )
    metadata = {
        "invited_email": invited_email,
        "role": role,
        "business_id": business.id if business else None,
        "business_name": business.name if business else None,
        "invited_by": user.email if user else None,
        "timestamp": timezone.now().isoformat(),
    }

    return log_user_action(
        request=request,
        user=user,
        action=AuditLog.TEAM_MEMBER_INVITE,
        description=description,
        resource_type="TeamMember",
        resource_id=invited_email,
        metadata=metadata,
    )

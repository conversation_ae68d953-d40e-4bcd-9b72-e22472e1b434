from common.models import AuditableModel
from django.db import models


class AuditLog(AuditableModel):
    """
    Model to store audit logs for user actions
    """

    # Action types
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    REGISTER = "REGISTER"
    PASSWORD_CHANGE = "PASSWORD_CHANGE"
    PASSWORD_RESET = "PASSWORD_RESET"
    PROFILE_UPDATE = "PROFILE_UPDATE"
    TRANSACTION_CREATE = "TRANSACTION_CREATE"
    TRANSACTION_UPDATE = "TRANSACTION_UPDATE"
    TRANSACTION_REVERSE = "TRANSACTION_REVERSE"
    WALLET_CREATE = "WALLET_CREATE"
    WALLET_UPDATE = "WALLET_UPDATE"
    WALLET_FUNDING = "WALLET_FUNDING"
    BUSINESS_CREATE = "BUSINESS_CREATE"
    BUSINESS_UPDATE = "BUSINESS_UPDATE"
    BUSINESS_ONBOARD = "BUSINESS_ONBOARD"
    PIN_CHANGE = "PIN_CHANGE"
    PIN_VERIFY = "PIN_VERIFY"
    OTP_REQUEST = "OTP_REQUEST"
    OTP_VERIFY = "OTP_VERIFY"
    TWO_FA_SETUP = "TWO_FA_SETUP"
    TWO_FA_DISABLE = "TWO_FA_DISABLE"
    GOOGLE_AUTH = "GOOGLE_AUTH"
    API_ACCESS = "API_ACCESS"
    API_KEY_GENERATE = "API_KEY_GENERATE"
    API_KEY_REVOKE = "API_KEY_REVOKE"
    PERMISSION_CHANGE = "PERMISSION_CHANGE"
    ROLE_UPDATE = "ROLE_UPDATE"
    ACCOUNT_DISABLE = "ACCOUNT_DISABLE"
    ACCOUNT_ENABLE = "ACCOUNT_ENABLE"
    DATA_EXPORT = "DATA_EXPORT"
    DATA_IMPORT = "DATA_IMPORT"
    SYSTEM_CONFIG = "SYSTEM_CONFIG"
    DISPUTE_CREATE = "DISPUTE_CREATE"
    DISPUTE_RESOLVE = "DISPUTE_RESOLVE"
    PROVIDER_ADD = "PROVIDER_ADD"
    PROVIDER_SWITCH = "PROVIDER_SWITCH"
    FEE_STRUCTURE_EDIT = "FEE_STRUCTURE_EDIT"
    AUDIT_LOG_ACCESS = "AUDIT_LOG_ACCESS"
    TEAM_MEMBER_INVITE = "TEAM_MEMBER_INVITE"
    TEAM_MEMBER_ACTIVATE = "TEAM_MEMBER_ACTIVATE"
    TEAM_MEMBER_DEACTIVATE = "TEAM_MEMBER_DEACTIVATE"
    DISPUTE_UPDATE = "DISPUTE_UPDATE"
    API_CALL = "API_CALL"
    VAS_TRANSACTION = "VAS_TRANSACTION"
    VIRTUAL_ACCOUNT_CREATE = "VIRTUAL_ACCOUNT_CREATE"
    WEBHOOK_RECEIVED = "WEBHOOK_RECEIVED"
    ADMIN_LOGIN = "ADMIN_LOGIN"
    ADMIN_LOGOUT = "ADMIN_LOGOUT"
    LIEN_PLACE = "LIEN_PLACE"
    LIEN_REMOVE = "LIEN_REMOVE"
    OTHER = "OTHER"

    ACTION_CHOICES = [
        (LOGIN, "Login"),
        (LOGOUT, "Logout"),
        (REGISTER, "Registration"),
        (PASSWORD_CHANGE, "Password Change"),
        (PASSWORD_RESET, "Password Reset"),
        (PROFILE_UPDATE, "Profile Update"),
        (TRANSACTION_CREATE, "Transaction Created"),
        (TRANSACTION_UPDATE, "Transaction Updated"),
        (TRANSACTION_REVERSE, "Transaction Reversed"),
        (WALLET_CREATE, "Wallet Created"),
        (WALLET_UPDATE, "Wallet Updated"),
        (WALLET_FUNDING, "Wallet Funding"),
        (BUSINESS_CREATE, "Business Created"),
        (BUSINESS_UPDATE, "Business Updated"),
        (BUSINESS_ONBOARD, "Merchant Onboarded"),
        (PIN_CHANGE, "PIN Change"),
        (PIN_VERIFY, "PIN Verification"),
        (OTP_REQUEST, "OTP Request"),
        (OTP_VERIFY, "OTP Verification"),
        (TWO_FA_SETUP, "2FA Setup"),
        (TWO_FA_DISABLE, "2FA Disable"),
        (GOOGLE_AUTH, "Google Authentication"),
        (API_ACCESS, "API Access"),
        (API_KEY_GENERATE, "API Key Generated"),
        (API_KEY_REVOKE, "API Key Revoked"),
        (PERMISSION_CHANGE, "Permission Change"),
        (ROLE_UPDATE, "User Role Updated"),
        (ACCOUNT_DISABLE, "Account Disable"),
        (ACCOUNT_ENABLE, "Account Enable"),
        (DATA_EXPORT, "Data Export"),
        (DATA_IMPORT, "Data Import"),
        (SYSTEM_CONFIG, "System Configuration"),
        (DISPUTE_CREATE, "Transaction Dispute Raised"),
        (DISPUTE_RESOLVE, "Dispute Resolved"),
        (PROVIDER_ADD, "New Provider Added"),
        (PROVIDER_SWITCH, "Provider Switched"),
        (FEE_STRUCTURE_EDIT, "Fee Structure Edited"),
        (AUDIT_LOG_ACCESS, "Audit Log Accessed"),
        (TEAM_MEMBER_INVITE, "Team Member Invited"),
        (TEAM_MEMBER_ACTIVATE, "Team Member Activated"),
        (TEAM_MEMBER_DEACTIVATE, "Team Member Deactivated"),
        (DISPUTE_UPDATE, "Dispute Updated"),
        (API_CALL, "API Call"),
        (VAS_TRANSACTION, "VAS Transaction"),
        (VIRTUAL_ACCOUNT_CREATE, "Virtual Account Created"),
        (WEBHOOK_RECEIVED, "Webhook Received"),
        (ADMIN_LOGIN, "Admin Login"),
        (ADMIN_LOGOUT, "Admin Logout"),
        (LIEN_PLACE, "Lien Placed"),
        (LIEN_REMOVE, "Lien Removed"),
        (OTHER, "Other"),
    ]

    # Status choices
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    PENDING = "PENDING"

    STATUS_CHOICES = [
        (SUCCESS, "Success"),
        (FAILED, "Failed"),
        (PENDING, "Pending"),
    ]

    # Pure denormalized fields (matching previous application pattern)
    audit_type = models.CharField(
        max_length=255, db_index=True, help_text="Type of audit event"
    )
    user_id = models.CharField(
        max_length=255, db_index=True, help_text="User UUID as string"
    )
    user_name = models.CharField(
        max_length=255, db_index=True, help_text="User full name at time of action"
    )
    user_email = models.EmailField(
        db_index=True, help_text="User email at time of action"
    )
    action = models.CharField(
        max_length=50,
        choices=ACTION_CHOICES,
        db_index=True,
        help_text="Type of action performed",
    )
    description = models.TextField(help_text="Detailed description of the action")
    ip_address = models.GenericIPAddressField(
        db_index=True, help_text="IP address from which the action was performed"
    )
    user_agent = models.TextField(
        blank=True, null=True, help_text="User agent string from the request"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=SUCCESS,
        db_index=True,
        help_text="Status of the action",
    )
    resource_type = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text="Type of resource affected (e.g., User, Transaction, Wallet)",
    )
    resource_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text="ID of the resource affected",
    )
    old_values = models.JSONField(
        blank=True,
        null=True,
        help_text="Previous values before the change (for updates)",
    )
    new_values = models.JSONField(
        blank=True, null=True, help_text="New values after the change (for updates)"
    )
    metadata = models.JSONField(
        blank=True, null=True, help_text="Additional metadata about the action"
    )
    session_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text="Session ID if available",
    )
    request_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_index=True,
        help_text="Unique request ID for tracing",
    )

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user_id", "-created_at"]),
            models.Index(fields=["user_email", "-created_at"]),
            models.Index(fields=["action", "-created_at"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["status", "-created_at"]),
            models.Index(fields=["resource_type", "resource_id"]),
            models.Index(fields=["audit_type", "-created_at"]),
        ]
        verbose_name = "Audit Log"
        verbose_name_plural = "Audit Logs"

    def __str__(self):
        return f"{self.user_email} - {self.get_action_display()} - {self.created_at}"

    @property
    def formatted_timestamp(self):
        """Return formatted timestamp for display"""
        return self.created_at.strftime("%Y-%m-%d %H:%M:%S UTC")

    @classmethod
    def log_action(
        cls,
        user=None,
        email=None,
        action=None,
        description=None,
        ip_address=None,
        user_agent=None,
        status=SUCCESS,
        resource_type=None,
        resource_id=None,
        old_values=None,
        new_values=None,
        metadata=None,
        session_id=None,
        request_id=None,
        audit_type=None,
    ):
        """
        Convenience method to create audit log entries
        Pure denormalized approach - no foreign keys
        """
        if user:
            user_id = str(user.id)
            user_email = user.email

            if hasattr(user, "fullname") and user.fullname:
                user_name = user.fullname
            elif hasattr(user, "firstname") and hasattr(user, "lastname"):
                user_name = f"{user.firstname or ''} {user.lastname or ''}".strip()
            else:
                user_name = user.email.split("@")[0]  # Fallback to email prefix

            if not user_name:
                user_name = "Unknown User"

            if not email:
                email = user.email
        else:
            # Handle system/anonymous actions
            user_id = None
            user_email = email or "system@local"
            user_name = "System"

        audit_data = {
            "user_id": user_id,
            "user_email": user_email,
            "user_name": user_name,
            "audit_type": audit_type or action,
            "action": action,
            "description": description,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "status": status,
            "resource_type": resource_type,
            "resource_id": resource_id,
            "old_values": old_values,
            "new_values": new_values,
            "metadata": metadata,
            "session_id": session_id,
            "request_id": request_id,
        }

        return cls.objects.create(**audit_data)

    @classmethod
    def log_action_async(
        cls,
        user=None,
        email=None,
        action=None,
        description=None,
        ip_address=None,
        user_agent=None,
        status=SUCCESS,
        resource_type=None,
        resource_id=None,
        old_values=None,
        new_values=None,
        metadata=None,
        session_id=None,
        request_id=None,
        audit_type=None,
        critical=False,
    ):
        """
        Convenience method to create audit log entries asynchronously using Celery
        """
        from audit.logger import AuditLogger

        AuditLogger.log_async(
            user=user,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            status=status,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            metadata=metadata,
            session_id=session_id,
            request_id=request_id,
            audit_type=audit_type,
            critical=critical,
        )

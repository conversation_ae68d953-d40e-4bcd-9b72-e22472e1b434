import logging
from typing import Any, Dict

from audit.models import Audit<PERSON>og
from celery import shared_task
from core.celery import APP

logger = logging.getLogger(__name__)


def log_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous function to create audit log entry
    Matches the pattern from your previous application
    """
    try:
        # Create audit log using the pure denormalized model
        audit_log = AuditLog.objects.create(**event)

        return {"status": "Logged", "payload": event, "audit_log_id": str(audit_log.id)}
    except Exception as e:
        logger.error(f"Failed to create audit log: {e}")
        return {"status": "Failed", "payload": event, "error": str(e)}


@APP.task(queue="logger_queue")
def log_audit_event_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Asynchronous Celery task for audit logging
    Matches the pattern from your previous application

    Args:
        payload: Dictionary containing audit log data

    Returns:
        Dictionary with logging status and payload
    """
    return log_event(payload)


@shared_task(bind=True, max_retries=3, queue="logger_queue")
def log_audit_event_with_retry(self, payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Asynchronous Celery task with retry logic for critical audit events

    Args:
        payload: Dictionary containing audit log data

    Returns:
        Dictionary with logging status and payload
    """
    try:
        return log_event(payload)
    except Exception as exc:
        logger.error(
            f"Audit logging failed (attempt {self.request.retries + 1}): {exc}"
        )

        if self.request.retries < self.max_retries:
            # Exponential backoff: 60s, 120s, 240s
            countdown = 60 * (2**self.request.retries)
            logger.info(f"Retrying audit log in {countdown} seconds...")
            raise self.retry(countdown=countdown, exc=exc)
        else:
            # Final failure - log to system logs
            logger.critical(
                f"Failed to log audit event after {self.max_retries} retries: {payload}"
            )
            return {
                "status": "Failed",
                "payload": payload,
                "error": str(exc),
                "retries_exhausted": True,
            }


@shared_task(queue="logger_queue")
def bulk_log_audit_events(payloads: list) -> Dict[str, Any]:
    """
    Bulk audit logging for high-volume scenarios

    Args:
        payloads: List of audit log data dictionaries

    Returns:
        Dictionary with bulk logging results
    """
    results = {"total": len(payloads), "successful": 0, "failed": 0, "errors": []}

    audit_logs = []

    for payload in payloads:
        try:
            # Prepare audit log object (don't save yet)
            audit_log = AuditLog(**payload)
            audit_logs.append(audit_log)
            results["successful"] += 1
        except Exception as e:
            results["failed"] += 1
            results["errors"].append({"payload": payload, "error": str(e)})
            logger.error(f"Failed to prepare audit log: {e}")

    # Bulk create successful audit logs
    if audit_logs:
        try:
            AuditLog.objects.bulk_create(audit_logs)
            logger.info(f"Successfully bulk created {len(audit_logs)} audit logs")
        except Exception as e:
            logger.error(f"Bulk create failed: {e}")
            results["failed"] += results["successful"]
            results["successful"] = 0
            results["errors"].append({"error": f"Bulk create failed: {str(e)}"})

    return results


# Convenience functions for different audit event types
@APP.task(queue="logger_queue")
def log_user_action(
    user_id: str, user_email: str, user_name: str, action: str, **kwargs
) -> Dict[str, Any]:
    """
    Log user-specific actions
    """
    payload = {
        "user_id": user_id,
        "user_email": user_email,
        "user_name": user_name,
        "audit_type": action,
        "action": action,
        **kwargs,
    }
    return log_event(payload)


@APP.task(queue="logger_queue")
def log_system_action(action: str, description: str, **kwargs) -> Dict[str, Any]:
    """
    Log system-level actions
    """
    payload = {
        "user_id": None,
        "user_email": "system@local",
        "user_name": "System",
        "audit_type": action,
        "action": action,
        "description": description,
        **kwargs,
    }
    return log_event(payload)


@APP.task(queue="logger_queue")
def log_api_call(
    user_id: str, user_email: str, user_name: str, endpoint: str, method: str, **kwargs
) -> Dict[str, Any]:
    """
    Log API calls
    """
    payload = {
        "user_id": user_id,
        "user_email": user_email,
        "user_name": user_name,
        "audit_type": "API_CALL",
        "action": "API_CALL",
        "description": f"{method} {endpoint}",
        "resource_type": "API_ENDPOINT",
        "resource_id": endpoint,
        **kwargs,
    }
    return log_event(payload)

"""
Console services for admin-specific business logic
"""

import logging
from decimal import Decimal
from typing import Any, Dict

from common.kgs import generate_uuid7
from console.models import ManualWalletAdjustment
from django.core.exceptions import ValidationError
from django.db import transaction
from transaction.enums import (
    TransactionClassEnum,
    TransactionModeEnum,
    TransactionStatusEnum,
)
from transaction.models import Transaction

logger = logging.getLogger(__name__)


class ManualWalletAdjustmentService:
    """
    Service class to handle manual wallet adjustments.

    This service provides the business logic for creating manual wallet
    adjustments when transactions fail to properly credit/debit wallets.
    """

    def __init__(self, original_transaction: Transaction, admin_user):
        """
        Initialize the service with the original transaction and admin user.

        Args:
            original_transaction: The transaction that had wallet issues
            admin_user: The admin user performing the adjustment
        """
        self.original_transaction = original_transaction
        self.admin_user = admin_user
        self.wallet = original_transaction.wallet
        self.business = original_transaction.business

    def create_adjustment(
        self, adjustment_type: str, reason: str
    ) -> ManualWalletAdjustment:
        """
        Create a manual wallet adjustment.

        Args:
            adjustment_type: 'CREDIT' or 'DEBIT'
            reason: Reason for the adjustment

        Returns:
            ManualWalletAdjustment: The created adjustment record

        Raises:
            ValidationError: If validation fails
        """
        # Validate inputs
        self._validate_adjustment(adjustment_type, reason)

        # Use the original transaction amount
        amount = self.original_transaction.amount

        with transaction.atomic():
            # Create the adjustment transaction record
            adjustment_transaction = self._create_adjustment_transaction(
                adjustment_type, amount, reason
            )

            # Update wallet balance
            self._update_wallet_balance(adjustment_type, amount)

            # Create the manual adjustment record
            manual_adjustment = ManualWalletAdjustment.objects.create(
                original_transaction=self.original_transaction,
                wallet=self.wallet,
                adjustment_type=adjustment_type,
                amount=amount,
                reason=reason,
                admin_user=self.admin_user,
                created_transaction=adjustment_transaction,
            )

            logger.info(
                f"Manual wallet adjustment created: {manual_adjustment.id} "
                f"for transaction {self.original_transaction.reference}"
            )

            # Send email notification asynchronously
            from console.tasks import send_manual_wallet_adjustment_email

            send_manual_wallet_adjustment_email.delay(manual_adjustment.id)

            return manual_adjustment

    def _validate_adjustment(self, adjustment_type: str, reason: str) -> None:
        """
        Validate the adjustment parameters.

        Args:
            adjustment_type: 'CREDIT' or 'DEBIT'
            reason: Reason for the adjustment

        Raises:
            ValidationError: If validation fails
        """
        # Check adjustment type
        if adjustment_type not in ["CREDIT", "DEBIT"]:
            raise ValidationError("Adjustment type must be 'CREDIT' or 'DEBIT'")

        # Check reason is provided
        if not reason or not reason.strip():
            raise ValidationError("Reason is required for manual adjustments")

        # Check if adjustment already exists for this transaction and type
        existing_adjustment = ManualWalletAdjustment.objects.filter(
            original_transaction=self.original_transaction,
            adjustment_type=adjustment_type,
        ).first()

        if existing_adjustment:
            raise ValidationError(
                f"A {adjustment_type.lower()} adjustment already exists for this transaction"
            )

        # Check if wallet exists and belongs to the business
        if self.wallet.business != self.business:
            raise ValidationError(
                "Wallet does not belong to the transaction's business"
            )

        # For debit adjustments, check if wallet has sufficient balance
        if adjustment_type == "DEBIT":
            if self.wallet.balance < self.original_transaction.amount:
                raise ValidationError(
                    f"Insufficient wallet balance. Current: {self.wallet.balance}, "
                    f"Required: {self.original_transaction.amount}"
                )

    def _create_adjustment_transaction(
        self, adjustment_type: str, amount: Decimal, reason: str
    ) -> Transaction:
        """
        Create a transaction record for the manual adjustment.

        Args:
            adjustment_type: 'CREDIT' or 'DEBIT'
            amount: Amount to adjust
            reason: Reason for adjustment

        Returns:
            Transaction: The created transaction record
        """
        # Get current wallet balance before adjustment
        self.wallet.refresh_from_db()
        old_balance = self.wallet.balance

        # Calculate new balance
        if adjustment_type == "CREDIT":
            new_balance = old_balance + amount
            mode = TransactionModeEnum.CREDIT.value
            txn_class = TransactionClassEnum.MANUAL_CREDIT.value
            narration = f"Manual credit adjustment: {reason}"
        else:  # DEBIT
            new_balance = old_balance - amount
            mode = TransactionModeEnum.DEBIT.value
            txn_class = TransactionClassEnum.MANUAL_DEBIT.value
            narration = f"Manual debit adjustment: {reason}"

        # Create transaction record
        adjustment_transaction = Transaction.objects.create(
            wallet=self.wallet,
            business=self.business,
            reference=generate_uuid7(),
            merchant_reference=f"MANUAL_ADJ_{self.original_transaction.reference}",
            status=TransactionStatusEnum.SUCCESSFUL.value,
            mode=mode,
            txn_class=txn_class,
            type=f"MANUAL_{adjustment_type}",
            amount=amount,
            charge=Decimal("0.00"),
            net_amount=amount,
            old_balance=old_balance,
            new_balance=new_balance,
            narration=narration,
            is_wallet_impacted=True,
            relationship_manager=self.business.relationship_manager,
        )

        return adjustment_transaction

    def _update_wallet_balance(self, adjustment_type: str, amount: Decimal) -> None:
        """
        Update the wallet balance based on adjustment type.

        Args:
            adjustment_type: 'CREDIT' or 'DEBIT'
            amount: Amount to adjust
        """
        if adjustment_type == "CREDIT":
            self.wallet.credit(amount)
        else:  # DEBIT
            self.wallet.debit(amount)

        logger.info(
            f"Wallet {self.wallet.id} balance updated: {adjustment_type} {amount}. "
            f"New balance: {self.wallet.balance}"
        )

    @classmethod
    def get_adjustment_summary(cls, business_id: str = None) -> Dict[str, Any]:
        """
        Get summary of manual adjustments.

        Args:
            business_id: Optional business ID to filter by

        Returns:
            Dict containing adjustment statistics
        """
        queryset = ManualWalletAdjustment.objects.all()

        if business_id:
            queryset = queryset.filter(wallet__business_id=business_id)

        total_adjustments = queryset.count()
        total_credits = queryset.filter(adjustment_type="CREDIT").count()
        total_debits = queryset.filter(adjustment_type="DEBIT").count()

        credit_amount = sum(
            adj.amount for adj in queryset.filter(adjustment_type="CREDIT")
        )
        debit_amount = sum(
            adj.amount for adj in queryset.filter(adjustment_type="DEBIT")
        )

        return {
            "total_adjustments": total_adjustments,
            "total_credits": total_credits,
            "total_debits": total_debits,
            "total_credit_amount": credit_amount,
            "total_debit_amount": debit_amount,
            "net_adjustment": credit_amount - debit_amount,
        }

# Generated by Django 5.1.7 on 2025-07-11 02:09

import common.kgs
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("console", "0002_initial"),
        ("transaction", "0009_alter_commissiontransaction_txn_class"),
        ("wallet", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="systemvasproduct",
            name="type",
            field=models.CharField(
                choices=[
                    ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                    ("TRANSFER", "TRANSFER"),
                    ("AIRTIME", "AIRTIME"),
                    ("DATA", "DATA"),
                    ("BETTING", "BETTING"),
                    ("ELECTRICITY", "ELECTRICITY"),
                    ("CABLE_TV", "CABLE_TV"),
                    ("SME_DATA", "SME_DATA"),
                    ("KY<PERSON>", "KYC"),
                    ("EDUCATION", "EDUCATION"),
                    ("EPIN", "EPIN"),
                    ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                    ("MANUAL_CREDIT", "MANUAL_CREDIT"),
                    ("MANUAL_DEBIT", "MANUAL_DEBIT"),
                ],
                max_length=50,
            ),
        ),
        migrations.CreateModel(
            name="ManualWalletAdjustment",
            fields=[
                (
                    "id",
                    models.CharField(
                        db_index=True,
                        default=common.kgs.generate_unique_id,
                        editable=False,
                        max_length=50,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "adjustment_type",
                    models.CharField(
                        choices=[("CREDIT", "Credit"), ("DEBIT", "Debit")],
                        help_text="Whether this is a credit or debit adjustment",
                        max_length=10,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount to credit or debit",
                        max_digits=20,
                    ),
                ),
                (
                    "reason",
                    models.TextField(
                        help_text="Detailed reason why this manual adjustment was necessary"
                    ),
                ),
                (
                    "admin_user",
                    models.ForeignKey(
                        help_text="Admin user who performed this adjustment",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="manual_adjustments_performed",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_transaction",
                    models.OneToOneField(
                        help_text="The transaction record created for this adjustment",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="manual_adjustment",
                        to="transaction.transaction",
                    ),
                ),
                (
                    "original_transaction",
                    models.ForeignKey(
                        help_text="The original transaction that had wallet issues",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="manual_adjustments",
                        to="transaction.transaction",
                    ),
                ),
                (
                    "wallet",
                    models.ForeignKey(
                        help_text="The wallet being manually adjusted",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="manual_adjustments",
                        to="wallet.wallet",
                    ),
                ),
            ],
            options={
                "verbose_name": "Manual Wallet Adjustment",
                "verbose_name_plural": "Manual Wallet Adjustments",
                "ordering": ["-created_at"],
                "unique_together": {("original_transaction", "adjustment_type")},
            },
        ),
    ]

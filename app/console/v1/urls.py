from business.v1.views import RelationshipManagerViewSet
from console.v1.views import (
    AdminBusinessChangeRequestViewSet,
    AdminBusinessManagementViewSet,
    MamaAfricaViewSet,
    ManualWalletAdjustmentViewSet,
    ProvidersViewSet,
    SystemVASProductViewSet,
)
from django.urls import include, path
from rest_framework.routers import DefaultRouter

app_name = "console"

router = DefaultRouter()
router.register(
    "business", AdminBusinessManagementViewSet, basename="business-management"
)
router.register(
    "change-requests",
    AdminBusinessChangeRequestViewSet,
    basename="business-change-requests",
)
router.register(
    "relationship-manager", RelationshipManagerViewSet, basename="relationship-manager"
)
router.register("vas-products", SystemVASProductViewSet, basename="system-vas-products")
router.register("providers", ProvidersViewSet, basename="providers")
router.register("mam-africa", MamaAfricaViewSet, basename="mama-africa")
router.register(
    "manual-wallet-adjustments",
    ManualWalletAdjustmentViewSet,
    basename="manual-wallet-adjustments",
)

urlpatterns = [
    path("", include(router.urls)),
]

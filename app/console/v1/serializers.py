import logging

from business.handlers.product_activation import (
    BaseVASProduct<PERSON><PERSON><PERSON>,
    VASProductHandlerFactory,
)
from business.models import BusinessChangeRequest, BusinessVASProduct
from common.enums import ProviderEnum
from common.kgs import generate_uuid7
from console.enums import Network, NetworkDenomination
from console.models import ManualWalletAdjustment, SystemVASProduct
from console.services import ManualWalletAdjustmentService
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import transaction
from rest_framework import serializers
from transaction.enums import TransactionClassEnum
from transaction.models import Transaction
from vas.integrations.mama_africa import MamaAfricaClient
from vas.integrations.wallet_balance import WalletBalanceGateClient

logger = logging.getLogger(__name__)


class VASProductActivationSerializer(serializers.Serializer):
    product_type = serializers.ChoiceField(choices=TransactionClassEnum.choices())
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        business = self.context["business"]
        product_type = attrs["product_type"]

        if BusinessVASProduct.objects.filter(
            business=business, product_type=product_type, is_active=True
        ).exists():
            raise serializers.ValidationError(
                {"product_type": "This product is already active for the business."}
            )

        return attrs

    @transaction.atomic
    def create(self, validated_data):
        business = self.context["business"]
        admin = self.context["admin"]
        product_type = validated_data["product_type"]
        notes = validated_data.get("notes")

        handler: BaseVASProductHandler = VASProductHandlerFactory.get_handler(
            product_type
        )
        handler.activate(business, admin, notes)

        return {"product_type": product_type}


class VASProductDeactivationSerializer(serializers.Serializer):
    product_type = serializers.ChoiceField(choices=TransactionClassEnum.choices())
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        business = self.context["business"]
        product_type = attrs["product_type"]

        try:
            self.instance = BusinessVASProduct.objects.get(
                business=business, product_type=product_type, is_active=True
            )
        except BusinessVASProduct.DoesNotExist:
            raise serializers.ValidationError(
                {
                    "product_type": "This product is not currently active for this business."
                }
            )

        return attrs

    @transaction.atomic
    def update(self, instance: BusinessVASProduct, validated_data):
        admin = self.context["admin"]
        notes = validated_data.get("notes")

        handler: BaseVASProductHandler = VASProductHandlerFactory.get_handler(
            instance.product_type
        )
        handler.deactivate(instance.business, admin, notes)

        return instance


class VASProductStatusSerializer(serializers.Serializer):
    product_type = serializers.CharField()
    is_active = serializers.BooleanField()
    activated_at = serializers.DateTimeField(allow_null=True)
    deactivated_at = serializers.DateTimeField(allow_null=True)


class VASProductStatusResponseSerializer(serializers.Serializer):
    business_id = serializers.CharField()
    business_name = serializers.CharField()
    vas_products = VASProductStatusSerializer(many=True)


class BusinessOverviewSerializer(serializers.Serializer):
    total = serializers.IntegerField()
    total_active = serializers.IntegerField()
    total_inactive = serializers.IntegerField()
    total_verified = serializers.IntegerField()


class BusinessChangeRequestOverviewSerializer(serializers.Serializer):
    total = serializers.IntegerField()
    total_pending = serializers.IntegerField()
    total_approved = serializers.IntegerField()
    total_rejected = serializers.IntegerField()


class BusinessChangeRequestApproveSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessChangeRequest
        fields = []

    def update(self, instance: BusinessChangeRequest, validated_data):
        user = self.context["request"].user
        try:
            with transaction.atomic():
                instance.approve(reviewed_by=user)
        except ValueError as e:
            raise serializers.ValidationError({"detail": str(e)})
        return instance


class BusinessChangeRequestRejectSerializer(serializers.ModelSerializer):
    rejection_note = serializers.CharField()

    class Meta:
        model = BusinessChangeRequest
        fields = ["rejection_note"]

    def update(self, instance: BusinessChangeRequest, validated_data):
        user = self.context["request"].user
        rejection_note = validated_data.get("rejection_note")
        try:
            with transaction.atomic():
                instance.reject(reviewed_by=user, note=rejection_note)
        except ValueError as e:
            raise serializers.ValidationError({"detail": str(e)})
        return instance


class SystemVASProductSerializer(serializers.ModelSerializer):

    class Meta:
        model = SystemVASProduct
        fields = ["id", "type", "is_active"]


class ProviderWalletBalanceSerializer(serializers.Serializer):
    provider = serializers.ChoiceField(choices=ProviderEnum.choices())

    def save(self, **kwargs):
        data = self.validated_data
        service_provider = data.get("provider")
        payload = {"service_provider": service_provider}
        return WalletBalanceGateClient().fetch_wallet_balance(payload)


class GenerateBulkEpinSerializer(serializers.Serializer):
    value = serializers.ChoiceField(choices=NetworkDenomination.choices())
    network = serializers.ChoiceField(choices=Network.choices())
    requested_quantity = serializers.IntegerField(min_value=1)

    def save(self, **kwargs):
        payload = self.validated_data
        payload["reference"] = generate_uuid7()
        response, status_code = MamaAfricaClient().generate_bulk_epin(payload)

        return response, status_code


class ManualWalletAdjustmentRequestSerializer(serializers.Serializer):
    """
    Serializer for creating manual wallet adjustments.
    """

    transaction_reference = serializers.CharField(
        max_length=100,
        help_text="Reference of the original transaction that had wallet issues",
    )
    adjustment_type = serializers.ChoiceField(
        choices=[("CREDIT", "Credit"), ("DEBIT", "Debit")],
        help_text="Type of adjustment to perform",
    )
    reason = serializers.CharField(
        style={"base_template": "textarea.html"},
        help_text="Detailed reason for this manual adjustment",
    )

    def validate_transaction_reference(self, value):
        """Validate that the transaction exists"""
        try:
            Transaction.objects.get(reference=value)
            return value
        except Transaction.DoesNotExist:
            raise serializers.ValidationError(
                "Transaction with this reference does not exist"
            )

    def validate_reason(self, value):
        """Validate reason is not empty"""
        if not value or not value.strip():
            raise serializers.ValidationError("Reason cannot be empty")
        return value.strip()

    def create(self, validated_data):
        """Create the manual wallet adjustment"""
        transaction_reference = validated_data["transaction_reference"]
        adjustment_type = validated_data["adjustment_type"]
        reason = validated_data["reason"]
        admin_user = self.context["request"].user

        # Get the original transaction
        original_transaction = Transaction.objects.get(reference=transaction_reference)

        # Create the adjustment using the service
        try:
            service = ManualWalletAdjustmentService(original_transaction, admin_user)
            adjustment = service.create_adjustment(adjustment_type, reason)
            return adjustment
        except DjangoValidationError as e:
            raise serializers.ValidationError(str(e))


class ManualWalletAdjustmentResponseSerializer(serializers.ModelSerializer):
    """
    Serializer for manual wallet adjustment responses.
    """

    admin_user_name = serializers.CharField(
        source="admin_user.fullname", read_only=True
    )
    admin_user_email = serializers.CharField(source="admin_user.email", read_only=True)
    merchant_name = serializers.CharField(read_only=True)
    business_id = serializers.CharField(source="business.id", read_only=True)
    wallet_type = serializers.CharField(source="wallet.type", read_only=True)
    original_transaction_reference = serializers.CharField(
        source="original_transaction.reference", read_only=True
    )
    created_transaction_reference = serializers.CharField(
        source="created_transaction.reference", read_only=True
    )

    class Meta:
        model = ManualWalletAdjustment
        fields = [
            "id",
            "adjustment_type",
            "amount",
            "reason",
            "admin_user_name",
            "admin_user_email",
            "merchant_name",
            "business_id",
            "wallet_type",
            "original_transaction_reference",
            "created_transaction_reference",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class ManualWalletAdjustmentSummarySerializer(serializers.Serializer):
    """
    Serializer for manual wallet adjustment summary statistics.
    """

    total_adjustments = serializers.IntegerField(read_only=True)
    total_credits = serializers.IntegerField(read_only=True)
    total_debits = serializers.IntegerField(read_only=True)
    total_credit_amount = serializers.DecimalField(
        max_digits=20, decimal_places=2, read_only=True
    )
    total_debit_amount = serializers.DecimalField(
        max_digits=20, decimal_places=2, read_only=True
    )
    net_adjustment = serializers.DecimalField(
        max_digits=20, decimal_places=2, read_only=True
    )

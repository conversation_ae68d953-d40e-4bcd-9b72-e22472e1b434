"""
Console app Celery tasks
"""

import logging

from celery import shared_task
from django.template.loader import get_template
from user.utils import send_email

logger = logging.getLogger(__name__)


@shared_task
def send_manual_wallet_adjustment_email(adjustment_id: str):
    """
    Send email notification when a manual wallet adjustment is made.

    Args:
        adjustment_id: ID of the ManualWalletAdjustment record
    """
    try:
        from console.models import ManualWalletAdjustment

        # Get the adjustment with related objects
        adjustment = ManualWalletAdjustment.objects.select_related(
            "wallet",
            "business",
            "admin_user",
            "original_transaction",
            "created_transaction",
        ).get(id=adjustment_id)

        business = adjustment.business
        business_owner = business.owner

        # Prepare email data
        email_data = {
            "business_owner_name": business_owner.fullname,
            "business_name": business.name or f"Business {business.id}",
            "adjustment_type": adjustment.adjustment_type,
            "amount": adjustment.amount,
            "wallet_type": adjustment.wallet.type,
            "adjustment_date": adjustment.created_at.strftime("%B %d, %Y at %I:%M %p"),
            "transaction_reference": adjustment.created_transaction.reference,
            "original_transaction_reference": adjustment.original_transaction.reference,
            "reason": adjustment.reason,
            "admin_user_name": adjustment.admin_user.fullname,
        }

        # Load email templates
        html_template = get_template("emails/manual_wallet_adjustment_template.html")
        text_template = get_template("emails/manual_wallet_adjustment_template.txt")

        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)

        # Prepare subject
        adjustment_action = (
            "credited to" if adjustment.adjustment_type == "CREDIT" else "debited from"
        )
        subject = f"Wallet Adjustment: ₦{adjustment.amount} {adjustment_action} your {adjustment.wallet.type} wallet"

        # Send email
        send_email(subject, business_owner.email, html_content, text_content)

        logger.info(
            f"Manual wallet adjustment email sent for adjustment {adjustment_id} "
            f"to {business_owner.email}"
        )

    except Exception as exc:
        logger.error(
            f"Error sending manual wallet adjustment email for adjustment {adjustment_id}: {str(exc)}"
        )

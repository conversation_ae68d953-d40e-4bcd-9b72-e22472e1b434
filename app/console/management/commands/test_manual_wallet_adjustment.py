"""
Management command to test manual wallet adjustment functionality
"""

from decimal import Decimal

from business.models import Business
from common.kgs import generate_uuid7
from console.services import ManualWalletAdjustmentService
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import transaction
from transaction.enums import (
    TransactionClassEnum,
    TransactionModeEnum,
    TransactionStatusEnum,
)
from transaction.models import Transaction

User = get_user_model()


class Command(BaseCommand):
    help = "Test the manual wallet adjustment functionality"

    def add_arguments(self, parser):
        parser.add_argument(
            "--business-email",
            type=str,
            default="<EMAIL>",
            help="Email of the business owner to test with",
        )
        parser.add_argument(
            "--admin-email",
            type=str,
            default="<EMAIL>",
            help="Email of the admin user to perform adjustment",
        )
        parser.add_argument(
            "--adjustment-type",
            type=str,
            choices=["CREDIT", "DEBIT"],
            default="CREDIT",
            help="Type of adjustment to test",
        )
        parser.add_argument(
            "--amount",
            type=float,
            default=1000.00,
            help="Amount for the test transaction",
        )

    def handle(self, *args, **options):
        business_email = options["business_email"]
        admin_email = options["admin_email"]
        adjustment_type = options["adjustment_type"]
        amount = Decimal(str(options["amount"]))

        try:
            with transaction.atomic():
                # Get or create business owner
                business_owner = self._get_or_create_business_owner(business_email)
                business = business_owner.business

                # Get or create admin user
                admin_user = self._get_or_create_admin_user(admin_email)

                # Create a test transaction
                test_transaction = self._create_test_transaction(business, amount)

                self.stdout.write(
                    self.style.SUCCESS(
                        f" Created test transaction: {test_transaction.reference}"
                    )
                )

                # Test the manual wallet adjustment
                self._test_manual_adjustment(
                    test_transaction, admin_user, adjustment_type
                )

                # Show wallet balance
                wallet = test_transaction.wallet
                wallet.refresh_from_db()
                self.stdout.write(
                    self.style.SUCCESS(f"Final wallet balance: ₦{wallet.balance}")
                )

                # Show adjustment summary
                self._show_adjustment_summary(business.id)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f" Error testing manual wallet adjustment: {str(e)}")
            )
            import traceback

            self.stdout.write(traceback.format_exc())

    def _get_or_create_business_owner(self, email):
        """Get or create business owner"""
        try:
            user = User.objects.get(email=email)
            if not hasattr(user, "business"):
                # Create business for existing user
                business = Business.objects.create(
                    name=f"Test Business for {user.firstname}",
                    owner=user,
                    email=f"business_{email}",
                )
                business._create_core_wallets()
                self.stdout.write(f"Created business for existing user: {email}")
            else:
                self.stdout.write(f"Using existing business owner: {email}")
            return user
        except User.DoesNotExist:
            # Create new user and business
            user = User.objects.create_user(
                email=email,
                password="testpassword123",
                firstname="Test",
                lastname="Business Owner",
                role="Business_Owner",
                is_active=True,
            )
            business = Business.objects.create(
                name=f"Test Business for {user.firstname}",
                owner=user,
                email=f"business_{email}",
            )
            business._create_core_wallets()
            self.stdout.write(f"Created new business owner: {email}")
            return user

    def _get_or_create_admin_user(self, email):
        """Get or create admin user"""
        try:
            admin_user = User.objects.get(email=email)
            self.stdout.write(f"Using existing admin user: {email}")
            return admin_user
        except User.DoesNotExist:
            admin_user = User.objects.create_user(
                email=email,
                password="testpassword123",
                firstname="Test",
                lastname="Admin",
                role="Admin",
                is_staff=True,
                is_active=True,
            )
            self.stdout.write(f"Created new admin user: {email}")
            return admin_user

    def _create_test_transaction(self, business, amount):
        """Create a test transaction"""
        wallet = business.get_general_wallet()

        # Ensure wallet has some balance for debit tests
        if wallet.balance < amount:
            wallet.credit(amount * 2)
            self.stdout.write(f"Added ₦{amount * 2} to wallet for testing")

        old_balance = wallet.balance

        # Create a test transaction (simulate a failed transaction)
        test_transaction = Transaction.objects.create(
            wallet=wallet,
            business=business,
            reference=generate_uuid7(),
            merchant_reference=f"TEST_{generate_uuid7()[:8]}",
            status=TransactionStatusEnum.SUCCESSFUL.value,
            mode=TransactionModeEnum.DEBIT.value,
            txn_class=TransactionClassEnum.AIRTIME.value,
            type="MTN_AIRTIME",
            amount=amount,
            charge=Decimal("50.00"),
            net_amount=amount + Decimal("50.00"),
            old_balance=old_balance,
            new_balance=old_balance,  # Simulate wallet not being updated
            narration=f"Test airtime purchase - ₦{amount}",
            is_wallet_impacted=False,  # Simulate failed wallet update
            relationship_manager=business.relationship_manager,
        )

        return test_transaction

    def _test_manual_adjustment(self, test_transaction, admin_user, adjustment_type):
        """Test the manual wallet adjustment"""
        reason = f"Test {adjustment_type.lower()} adjustment - correcting failed wallet \
            update for transaction {test_transaction.reference}"

        # Show wallet balance before adjustment
        wallet = test_transaction.wallet
        wallet.refresh_from_db()
        self.stdout.write(f"Wallet balance before adjustment: ₦{wallet.balance}")

        # Create the adjustment
        service = ManualWalletAdjustmentService(test_transaction, admin_user)
        adjustment = service.create_adjustment(adjustment_type, reason)

        self.stdout.write(
            self.style.SUCCESS(
                f" Manual {adjustment_type.lower()} adjustment created successfully!"
            )
        )
        self.stdout.write(f"   Adjustment ID: {adjustment.id}")
        self.stdout.write(f"   Amount: ₦{adjustment.amount}")
        self.stdout.write(
            f"   Created Transaction: {adjustment.created_transaction.reference}"
        )

        # Show wallet balance after adjustment
        wallet.refresh_from_db()
        self.stdout.write(f"Wallet balance after adjustment: ₦{wallet.balance}")

    def _show_adjustment_summary(self, business_id):
        """Show adjustment summary"""
        summary = ManualWalletAdjustmentService.get_adjustment_summary(business_id)

        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("ADJUSTMENT SUMMARY")
        self.stdout.write("=" * 50)
        self.stdout.write(f"Total Adjustments: {summary['total_adjustments']}")
        self.stdout.write(f"Total Credits: {summary['total_credits']}")
        self.stdout.write(f"Total Debits: {summary['total_debits']}")
        self.stdout.write(f"Total Credit Amount: ₦{summary['total_credit_amount']}")
        self.stdout.write(f"Total Debit Amount: ₦{summary['total_debit_amount']}")
        self.stdout.write(f"Net Adjustment: ₦{summary['net_adjustment']}")
        self.stdout.write("=" * 50)

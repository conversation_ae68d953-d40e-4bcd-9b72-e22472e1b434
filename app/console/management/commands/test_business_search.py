"""
Management command to test the business search functionality
"""

from console.v1.views import AdminBusinessManagementViewSet
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.test import RequestFactory
from rest_framework.request import Request

User = get_user_model()


class Command(BaseCommand):
    help = "Test the business search functionality to verify the fullname fix"

    def add_arguments(self, parser):
        parser.add_argument(
            "--search",
            type=str,
            default="jima",
            help="Search term to test with (default: jima)",
        )

    def handle(self, *args, **options):
        search_term = options["search"]

        try:
            # Create a mock request
            factory = RequestFactory()
            django_request = factory.get(
                f"/api/v1/console/business/?page_size=2&search={search_term}"
            )

            # Get or create an admin user for testing
            admin_user, _ = User.objects.get_or_create(
                email="<EMAIL>",
                defaults={
                    "firstname": "Test",
                    "lastname": "Admin",
                    "role": "Admin",
                    "is_staff": True,
                    "is_active": True,
                },
            )

            # Create DRF Request object
            request = Request(django_request)
            request.user = admin_user

            # Create the viewset instance
            viewset = AdminBusinessManagementViewSet()
            viewset.request = request
            viewset.format_kwarg = None
            viewset.action = "list"

            # Test the search functionality
            self.stdout.write(f"Testing search with term: '{search_term}'")

            # Get the queryset and apply filters
            queryset = viewset.get_queryset()
            filtered_queryset = viewset.filter_queryset(queryset)

            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ Search functionality works! Found {filtered_queryset.count()} results."
                )
            )

            for business in filtered_queryset[:5]:
                owner_name = f"{business.owner.firstname} {business.owner.lastname}"
                self.stdout.write(
                    f"  - {business.name or 'Unnamed'} (Owner: {owner_name}, Email: {business.owner.email})"
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error testing search functionality: {str(e)}")
            )
            import traceback

            self.stdout.write(traceback.format_exc())

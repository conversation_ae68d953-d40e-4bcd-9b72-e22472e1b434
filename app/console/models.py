from common.models import AuditableModel
from django.db import models
from transaction.enums import TransactionClassEnum


class ManualWalletAdjustment(AuditableModel):
    """
    Model to track manual wallet adjustments made by admin users.

    This model maintains an audit trail of manual interventions when
    transactions fail to properly credit/debit merchant wallets due to
    network issues or system glitches.
    """

    ADJUSTMENT_TYPE_CHOICES = [
        ("CREDIT", "Credit"),
        ("DEBIT", "Debit"),
    ]

    # Reference to the original problematic transaction
    original_transaction = models.ForeignKey(
        "transaction.Transaction",
        on_delete=models.PROTECT,
        related_name="manual_adjustments",
        help_text="The original transaction that had wallet issues",
    )

    # The wallet that needs adjustment
    wallet = models.ForeignKey(
        "wallet.Wallet",
        on_delete=models.PROTECT,
        related_name="manual_adjustments",
        help_text="The wallet being manually adjusted",
    )

    # Type of adjustment
    adjustment_type = models.CharField(
        max_length=10,
        choices=ADJUSTMENT_TYPE_CHOICES,
        help_text="Whether this is a credit or debit adjustment",
    )

    # Amount being adjusted
    amount = models.DecimalField(
        max_digits=20, decimal_places=2, help_text="Amount to credit or debit"
    )

    # Reason for the adjustment
    reason = models.TextField(
        help_text="Detailed reason why this manual adjustment was necessary"
    )

    # Admin user who performed the adjustment
    admin_user = models.ForeignKey(
        "user.User",
        on_delete=models.PROTECT,
        related_name="manual_adjustments_performed",
        help_text="Admin user who performed this adjustment",
    )

    # The new transaction created for this adjustment
    created_transaction = models.OneToOneField(
        "transaction.Transaction",
        on_delete=models.PROTECT,
        related_name="manual_adjustment",
        help_text="The transaction record created for this adjustment",
    )

    class Meta:
        verbose_name = "Manual Wallet Adjustment"
        verbose_name_plural = "Manual Wallet Adjustments"
        ordering = ["-created_at"]
        # Prevent duplicate adjustments on the same transaction
        unique_together = ["original_transaction", "adjustment_type"]

    def __str__(self):
        return f"{self.adjustment_type} {self.amount} to {self.wallet} by {self.admin_user.email}"

    @property
    def business(self):
        """Get the business associated with this adjustment"""
        return self.wallet.business

    @property
    def merchant_name(self):
        """Get the merchant name for this adjustment"""
        return self.business.name or f"Business {self.business.id}"


class SystemVASProduct(AuditableModel):
    type = models.CharField(max_length=50, choices=TransactionClassEnum.choices())
    is_active = models.BooleanField(default=True)

    activated_by = models.ForeignKey(
        "user.User",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="activated_system_vas_products",
    )
    deactivated_by = models.ForeignKey(
        "user.User",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="deactivated_system_vas_products",
    )

    activated_at = models.DateTimeField(auto_now_add=True)
    deactivated_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "System VAS Product"
        verbose_name_plural = "System VAS Products"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.type} -- {self.is_active}"

"""
Permission utilities for teams app.

This module provides utility functions for checking permissions based on the teams app's
Role and Permission models. It supports both business and admin users.
"""

from rest_framework import permissions
from teams.enums import SystemBaseUserRole, SystemPlatform
from teams.models import AdminProfile, Permission, TeamMember


def get_user_business_context(user):
    """
    Get user's business context (business and role).

    Args:
        user: User instance

    Returns:
        tuple: (business, role) where business is Business instance and role is Role instance
               Returns (None, None) if user has no business context
    """
    if not user or not user.is_authenticated:
        return None, None

    # Check if user is a business owner
    if (
        hasattr(user, "business")
        and user.role == SystemBaseUserRole.BusinessOwner.value
    ):
        return (
            user.business,
            None,
        )  # Business owners don't have a specific role in teams

    # Check if user is a team member
    try:
        team_member = TeamMember.objects.select_related("business", "role").get(
            user=user, status="Active"
        )
        return team_member.business, team_member.role
    except TeamMember.DoesNotExist:
        return None, None


def get_user_admin_context(user):
    """
    Get user's admin context (admin profile and role).

    Args:
        user: User instance

    Returns:
        tuple: (admin_profile, role) where admin_profile is AdminProfile instance and role is Role instance
               Returns (None, None) if user has no admin context
    """
    if not user or not user.is_authenticated:
        return None, None

    try:
        admin_profile = AdminProfile.objects.select_related("role").get(
            user=user, status="Active"
        )
        return admin_profile, admin_profile.role
    except AdminProfile.DoesNotExist:
        return None, None


def check_user_permission(user, permission_codename, business=None):
    """
    Check if user has a specific permission.

    Args:
        user: User instance
        permission_codename: Permission codename (e.g., "dispute:CREATE")
        business: Business instance (optional, for business-specific checks)

    Returns:
        bool: True if user has the permission, False otherwise
    """
    if not user or not user.is_authenticated:
        return False

    # Check business context first
    user_business, business_role = get_user_business_context(user)
    if user_business:
        # If business is specified, ensure user belongs to that business
        if business and user_business != business:
            return False

        # Business owners have all permissions for their business
        if user.role == SystemBaseUserRole.BusinessOwner.value:
            return True

        # Check team member permissions
        if business_role:
            return business_role.permissions.filter(
                codename=permission_codename
            ).exists()

    # Check admin context
    admin_profile, admin_role = get_user_admin_context(user)
    if admin_profile and admin_role:
        return admin_role.permissions.filter(codename=permission_codename).exists()

    return False


def get_user_permissions(user, business=None):
    """
    Get all permissions for a user.

    Args:
        user: User instance
        business: Business instance (optional, for business-specific checks)

    Returns:
        QuerySet: Permission objects the user has access to
    """
    if not user or not user.is_authenticated:
        return Permission.objects.none()

    # Check business context first
    user_business, business_role = get_user_business_context(user)
    if user_business:
        # If business is specified, ensure user belongs to that business
        if business and user_business != business:
            return Permission.objects.none()

        # Business owners have all business platform permissions
        if user.role == SystemBaseUserRole.BusinessOwner.value:
            return Permission.objects.filter(
                roles__platform=SystemPlatform.Business.value
            ).distinct()

        # Return team member permissions
        if business_role:
            return business_role.permissions.all()

    # Check admin context
    admin_profile, admin_role = get_user_admin_context(user)
    if admin_profile and admin_role:
        return admin_role.permissions.all()

    return Permission.objects.none()


class HasPermission(permissions.BasePermission):
    """
    Permission class that checks if user has a specific permission using teams app logic.

    Usage:
        permission_classes = [IsAuthenticated, HasPermission("dispute:CREATE")]
    """

    def __init__(self, permission_codename):
        self.permission_codename = permission_codename
        super().__init__()

    def has_permission(self, request, view):
        return check_user_permission(request.user, self.permission_codename)

    def has_object_permission(self, request, view, obj):
        """Check object-level permissions."""
        # For business objects, ensure user belongs to the same business
        if hasattr(obj, "business"):
            return check_user_permission(
                request.user, self.permission_codename, obj.business
            )

        return self.has_permission(request, view)


class HasBusinessPermission(permissions.BasePermission):
    """
    Permission class specifically for business-related permissions.
    Ensures user belongs to the business and has the required permission.
    """

    def __init__(self, permission_codename):
        self.permission_codename = permission_codename
        super().__init__()

    def has_permission(self, request, view):
        user_business, _ = get_user_business_context(request.user)
        return user_business is not None and check_user_permission(
            request.user, self.permission_codename
        )

    def has_object_permission(self, request, view, obj):
        """Ensure object belongs to user's business."""
        if hasattr(obj, "business"):
            user_business, _ = get_user_business_context(request.user)
            return user_business == obj.business and check_user_permission(
                request.user, self.permission_codename, obj.business
            )

        return self.has_permission(request, view)


class HasAdminPermission(permissions.BasePermission):
    """
    Permission class specifically for admin-related permissions.
    Ensures user is an admin and has the required permission.
    """

    def __init__(self, permission_codename):
        self.permission_codename = permission_codename
        super().__init__()

    def has_permission(self, request, view):
        admin_profile, _ = get_user_admin_context(request.user)
        return admin_profile is not None and check_user_permission(
            request.user, self.permission_codename
        )


# Convenience permission classes for common dispute permissions
class CanCreateDispute(HasBusinessPermission):
    """Permission to create disputes."""

    def __init__(self):
        super().__init__("dispute:CREATE")


class CanReadDispute(HasBusinessPermission):
    """Permission to read disputes."""

    def __init__(self):
        super().__init__("dispute:READ")


class CanUpdateDispute(HasAdminPermission):
    """Permission to update disputes (admin only)."""

    def __init__(self):
        super().__init__("dispute:UPDATE")


class CanReadAllDisputes(HasAdminPermission):
    """Permission to read all disputes across businesses (admin only)."""

    def __init__(self):
        super().__init__("dispute:READ")

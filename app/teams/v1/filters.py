import django_filters
from teams.enums import TeamMemberStatus
from teams.models import AdminProfile, TeamMember


class TeamMemberFilter(django_filters.FilterSet):
    """Filter for business team members."""

    status = django_filters.ChoiceFilter(
        choices=TeamMemberStatus.choices(), help_text="Filter by team member status"
    )
    role = django_filters.CharFilter(
        field_name="role__name",
        lookup_expr="iexact",
        help_text="Filter by role name (case-insensitive)",
    )

    class Meta:
        model = TeamMember
        fields = ["status", "role"]


class AdminTeamMemberFilter(django_filters.FilterSet):
    """Filter for admin team members."""

    status = django_filters.ChoiceFilter(
        choices=TeamMemberStatus.choices(), help_text="Filter by team member status"
    )
    role = django_filters.Char<PERSON>ilter(
        field_name="role__name",
        lookup_expr="iexact",
        help_text="Filter by role name (case-insensitive)",
    )

    class Meta:
        model = AdminProfile
        fields = ["status", "role"]

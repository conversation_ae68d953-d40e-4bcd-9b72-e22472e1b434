from django.core.management.base import BaseCommand
from teams.enums import (
    DefaultAdminRole,
    DefaultBusinessRole,
    SystemPlatform,
    SystemRoleType,
)
from teams.models import Permission, Role

ROLE_MAP = {
    SystemPlatform.Business.value: {
        DefaultBusinessRole.CustomerSupport.value: [
            ("dashboard_metrics", "CREATE"),
            ("dashboard_metrics", "READ"),
            ("products", "READ"),
            ("transactions", "READ"),
            ("change_request", "READ"),
            ("teams", "READ"),
            ("developer", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("dispute", "READ"),
        ],
        DefaultBusinessRole.Operations.value: [
            ("dashboard_metrics", "CREATE"),
            ("dashboard_metrics", "READ"),
            ("products", "READ"),
            ("transactions", "READ"),
            ("virtual_accounts_withdrawal", "CREATE"),
            ("virtual_accounts_withdrawal", "READ"),
            ("change_request", "CREATE"),
            ("change_request", "READ"),
            ("change_request", "UPDATE"),
            ("teams", "CREATE"),
            ("teams", "READ"),
            ("teams", "UPDATE"),
            ("developer", "READ"),
            ("developer", "UPDATE"),
            ("developer", "CREATE"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_wallet", "READ"),
            ("dispute", "READ"),
            ("dispute", "CREATE"),
        ],
        DefaultBusinessRole.Reconciliation.value: [
            ("dashboard_metrics", "READ"),
            ("products", "READ"),
            ("transactions", "READ"),
            ("change_request", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_wallet", "READ"),
        ],
        DefaultBusinessRole.Developer.value: [
            ("developer", "READ"),
        ],
        DefaultBusinessRole.Admin.value: [
            ("dashboard_metrics", "READ"),
            ("dashboard_metrics", "CREATE"),
            ("products", "READ"),
            ("products", "CREATE"),
            ("products", "UPDATE"),
            ("transactions", "READ"),
            ("transactions", "CREATE"),
            ("transactions", "UPDATE"),
            ("virtual_accounts", "READ"),
            ("virtual_accounts", "CREATE"),
            ("virtual_accounts", "UPDATE"),
            ("virtual_accounts_withdrawal", "READ"),
            ("virtual_accounts_withdrawal", "CREATE"),
            ("virtual_accounts_withdrawal", "UPDATE"),
            ("change_request", "READ"),
            ("change_request", "CREATE"),
            ("change_request", "UPDATE"),
            ("teams", "READ"),
            ("teams", "CREATE"),
            ("teams", "UPDATE"),
            ("developer", "READ"),
            ("developer", "CREATE"),
            ("developer", "UPDATE"),
            ("security", "READ"),
            ("security", "UPDATE"),
            ("security", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_mandate", "READ"),
            ("recurrent_debit_mandate", "CREATE"),
            ("recurrent_debit_mandate", "UPDATE"),
            ("recurrent_debit_wallet", "READ"),
            ("recurrent_debit_wallet", "CREATE"),
            ("recurrent_debit_wallet", "UPDATE"),
            ("dispute", "READ"),
            ("dispute", "CREATE"),
            ("audit_logs", "READ"),
            ("audit_logs", "CREATE"),
            ("audit_logs", "UPDATE"),
        ],
        DefaultBusinessRole.BusinessOwner.value: [
            ("dashboard_metrics", "READ"),
            ("dashboard_metrics", "CREATE"),
            ("products", "READ"),
            ("products", "CREATE"),
            ("products", "UPDATE"),
            ("transactions", "READ"),
            ("transactions", "CREATE"),
            ("transactions", "UPDATE"),
            ("virtual_accounts", "READ"),
            ("virtual_accounts", "CREATE"),
            ("virtual_accounts", "UPDATE"),
            ("virtual_accounts_withdrawal", "READ"),
            ("virtual_accounts_withdrawal", "CREATE"),
            ("virtual_accounts_withdrawal", "UPDATE"),
            ("change_request", "READ"),
            ("change_request", "CREATE"),
            ("change_request", "UPDATE"),
            ("teams", "READ"),
            ("teams", "CREATE"),
            ("teams", "UPDATE"),
            ("developer", "READ"),
            ("developer", "CREATE"),
            ("developer", "UPDATE"),
            ("security", "READ"),
            ("security", "UPDATE"),
            ("security", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_mandate", "READ"),
            ("recurrent_debit_mandate", "CREATE"),
            ("recurrent_debit_mandate", "UPDATE"),
            ("recurrent_debit_wallet", "READ"),
            ("recurrent_debit_wallet", "CREATE"),
            ("recurrent_debit_wallet", "UPDATE"),
            ("dispute", "READ"),
            ("dispute", "CREATE"),
            ("audit_logs", "READ"),
            ("audit_logs", "CREATE"),
            ("audit_logs", "UPDATE"),
        ],
    },
    SystemPlatform.Admin.value: {
        DefaultAdminRole.CustomerSupport.value: [
            ("dashboard_metrics", "CREATE"),
            ("dashboard_metrics", "READ"),
            ("products", "READ"),
            ("transactions", "READ"),
            ("change_request", "READ"),
            ("teams", "READ"),
            ("developer", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("dispute", "READ"),
        ],
        DefaultAdminRole.Operations.value: [
            ("dashboard_metrics", "CREATE"),
            ("dashboard_metrics", "READ"),
            ("products", "READ"),
            ("transactions", "READ"),
            ("virtual_accounts_withdrawal", "CREATE"),
            ("virtual_accounts_withdrawal", "READ"),
            ("change_request", "CREATE"),
            ("change_request", "READ"),
            ("change_request", "UPDATE"),
            ("teams", "CREATE"),
            ("teams", "READ"),
            ("teams", "UPDATE"),
            ("developer", "READ"),
            ("developer", "UPDATE"),
            ("developer", "CREATE"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_wallet", "READ"),
            ("dispute", "READ"),
            ("dispute", "UPDATE"),
        ],
        DefaultAdminRole.Reconciliation.value: [
            ("dashboard_metrics", "READ"),
            ("products", "READ"),
            ("transactions", "READ"),
            ("change_request", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_wallet", "READ"),
        ],
        DefaultAdminRole.Admin.value: [
            ("dashboard_metrics", "READ"),
            ("dashboard_metrics", "CREATE"),
            ("products", "READ"),
            ("products", "CREATE"),
            ("products", "UPDATE"),
            ("transactions", "READ"),
            ("transactions", "CREATE"),
            ("transactions", "UPDATE"),
            ("virtual_accounts", "READ"),
            ("virtual_accounts", "CREATE"),
            ("virtual_accounts", "UPDATE"),
            ("virtual_accounts_withdrawal", "READ"),
            ("virtual_accounts_withdrawal", "CREATE"),
            ("virtual_accounts_withdrawal", "UPDATE"),
            ("change_request", "READ"),
            ("change_request", "CREATE"),
            ("change_request", "UPDATE"),
            ("teams", "READ"),
            ("teams", "CREATE"),
            ("teams", "UPDATE"),
            ("developer", "READ"),
            ("developer", "CREATE"),
            ("developer", "UPDATE"),
            ("security", "READ"),
            ("security", "UPDATE"),
            ("security", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_mandate", "READ"),
            ("recurrent_debit_mandate", "CREATE"),
            ("recurrent_debit_mandate", "UPDATE"),
            ("recurrent_debit_wallet", "READ"),
            ("recurrent_debit_wallet", "CREATE"),
            ("recurrent_debit_wallet", "UPDATE"),
            ("dispute", "READ"),
            ("dispute", "UPDATE"),
            ("audit_logs", "READ"),
            ("audit_logs", "CREATE"),
            ("audit_logs", "UPDATE"),
        ],
        DefaultAdminRole.SuperAdmin.value: [
            ("dashboard_metrics", "READ"),
            ("dashboard_metrics", "CREATE"),
            ("products", "READ"),
            ("products", "CREATE"),
            ("products", "UPDATE"),
            ("transactions", "READ"),
            ("transactions", "CREATE"),
            ("transactions", "UPDATE"),
            ("virtual_accounts", "READ"),
            ("virtual_accounts", "CREATE"),
            ("virtual_accounts", "UPDATE"),
            ("virtual_accounts_withdrawal", "READ"),
            ("virtual_accounts_withdrawal", "CREATE"),
            ("virtual_accounts_withdrawal", "UPDATE"),
            ("change_request", "READ"),
            ("change_request", "CREATE"),
            ("change_request", "UPDATE"),
            ("teams", "READ"),
            ("teams", "CREATE"),
            ("teams", "UPDATE"),
            ("developer", "READ"),
            ("developer", "CREATE"),
            ("developer", "UPDATE"),
            ("security", "READ"),
            ("security", "UPDATE"),
            ("security", "CREATE"),
            ("recurrent_debit_transaction", "READ"),
            ("recurrent_debit_transaction", "CREATE"),
            ("recurrent_debit_mandate", "READ"),
            ("recurrent_debit_mandate", "CREATE"),
            ("recurrent_debit_mandate", "UPDATE"),
            ("recurrent_debit_wallet", "READ"),
            ("recurrent_debit_wallet", "CREATE"),
            ("recurrent_debit_wallet", "UPDATE"),
            ("dispute", "READ"),
            ("dispute", "UPDATE"),
            ("audit_logs", "READ"),
            ("audit_logs", "CREATE"),
            ("audit_logs", "UPDATE"),
        ],
        DefaultAdminRole.Developer.value: [
            ("developer", "READ"),
            ("dispute", "READ"),
        ],
    },
}

ALL_PERMISSIONS = set()
for roles in ROLE_MAP.values():
    for perms in roles.values():
        ALL_PERMISSIONS.update(perms)


class Command(BaseCommand):
    help = "Seed default permissions and roles for Business and Admin platforms"

    def handle(self, *args, **kwargs):
        self.stdout.write("\n🚀 Starting seeding of Permissions and Roles...\n")

        permission_objs = {}
        created_perms = 0
        reused_perms = 0

        self.stdout.write("🔧 Seeding Permissions...")
        for feature, action in sorted(ALL_PERMISSIONS):
            codename = f"{feature}:{action}"
            perm, created = Permission.objects.get_or_create(
                codename=codename,
                defaults={"feature": feature, "action": action.lower()},
            )
            permission_objs[codename] = perm
            if created:
                self.stdout.write(f"   ✅ Created permission: {codename}")
                created_perms += 1
            else:
                self.stdout.write(f"   ♻️  Permission already exists: {codename}")
                reused_perms += 1

        self.stdout.write(
            f"\n✅ Total permissions: {len(ALL_PERMISSIONS)} | Created: {created_perms} | Existing: {reused_perms}\n"
        )

        self.stdout.write("🔧 Seeding Roles and assigning permissions...\n")
        created_roles = 0
        reused_roles = 0

        for platform, roles in ROLE_MAP.items():
            for role_name, permissions in roles.items():
                role, created = Role.objects.get_or_create(
                    name=role_name,
                    platform=platform,
                    type=SystemRoleType.Default.value,
                    defaults={"description": f"Default {platform} role: {role_name}"},
                )
                if created:
                    self.stdout.write(
                        f"   ✅ Created role: {role_name} (platform: {platform})"
                    )
                    created_roles += 1
                else:
                    self.stdout.write(
                        f"   ♻️  Role already exists: {role_name} (platform: {platform})"
                    )
                    reused_roles += 1

                perms_to_assign = []
                for feature, action in permissions:
                    codename = f"{feature}:{action}"
                    perm = permission_objs.get(codename)
                    if not perm:
                        self.stdout.write(
                            self.style.WARNING(f"   ⚠️  Missing permission: {codename}")
                        )
                        continue
                    perms_to_assign.append(perm)

                role.permissions.set(perms_to_assign)
                self.stdout.write(
                    f"      🔗 Assigned {len(perms_to_assign)} permissions to role: {role_name}"
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"\n🎉 Done. Roles created: {created_roles} | Reused: {reused_roles}. "
                f"Permissions created: {created_perms}, reused: {reused_perms}."
            )
        )

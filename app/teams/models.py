from common.models import AuditableModel
from django.db import models
from django.utils import timezone
from teams.enums import (
    SystemFeature,
    SystemPermission,
    SystemPlatform,
    SystemRoleType,
    TeamMemberStatus,
)


class Permission(AuditableModel):
    codename = models.CharField(max_length=100, unique=True)
    feature = models.CharField(
        max_length=100,
        choices=SystemFeature.choices(),
        help_text="Feature this permission is associated with",
        db_index=True,
    )
    action = models.CharField(max_length=50, choices=SystemPermission.choices())

    def __str__(self):
        return self.codename


class Role(AuditableModel):
    name = models.Char<PERSON>ield(max_length=100)
    description = models.TextField(blank=True)
    type = models.CharField(
        max_length=20,
        choices=SystemRoleType.choices(),
        default=SystemRoleType.Default.value,
        help_text="Type of role (e.g., custom, default)",
        db_index=True,
    )
    platform = models.Char<PERSON><PERSON>(
        max_length=20,
        choices=SystemPlatform.choices(),
        help_text="Platform for which this role is applicable (e.g., admin, business, global)",
        db_index=True,
    )
    permissions = models.ManyToManyField(Permission, related_name="roles")
    business = models.ForeignKey(
        "business.Business",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="roles",
        db_index=True,
    )

    class Meta:
        unique_together = ("name", "platform")
        verbose_name = "Role"
        verbose_name_plural = "Roles"

    def __str__(self):
        return self.name


class TeamMember(AuditableModel):
    user = models.OneToOneField(
        "user.User", on_delete=models.CASCADE, related_name="teammember"
    )
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.CASCADE,
        related_name="core_team_members",
        db_index=True,
    )
    role = models.ForeignKey(
        Role, on_delete=models.PROTECT, related_name="team_members"
    )
    status = models.CharField(
        max_length=20,
        choices=TeamMemberStatus.choices(),
        default=TeamMemberStatus.Invited.value,
        db_index=True,
    )
    invited_by = models.ForeignKey(
        "user.User",
        null=True,
        on_delete=models.SET_NULL,
        related_name="invited_team_members",
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Team Member"
        verbose_name_plural = "Team Members"

    def __str__(self):
        return f"{self.user.email} -- {self.role.name}"

    def revoke_invitation(self):
        """
        Revokes the team member's invitation and anonymizes the associated user.
        Only applicable if the member has not yet accepted the invitation.
        """
        if self.status != TeamMemberStatus.Invited.value:
            raise ValueError("Only invited members can be revoked.")

        revoked_at = timezone.now()
        prefix = self._generate_revocation_prefix(revoked_at)

        # Delete any associated tokens to prevent password creation
        from user.models import Token

        Token.objects.filter(user=self.user).delete()

        self._anonymize_user(prefix)
        self.status = TeamMemberStatus.Revoked.value
        self.save()

    def _generate_revocation_prefix(self, date):
        return f"revoked_{date.strftime('%Y%m%d')}"

    def _anonymize_user(self, prefix):
        user = self.user

        if user.email:
            user.email = f"{prefix}_{user.email}"

        if hasattr(user, "phone") and user.phone:
            last6 = user.phone[-6:]
            masked = "*" * max(0, len(user.phone) - 6)
            user.phone = f"{masked}{last6}"

        user.is_active = False
        user.save()

    def deactivate(self):
        self.status = TeamMemberStatus.Deactivated.value
        self.user.is_active = False
        self.user.save()
        self.save()

    def reactivate(self):
        self.status = TeamMemberStatus.Active.value
        self.user.is_active = True
        self.user.save()
        self.save()


class AdminProfile(AuditableModel):
    user = models.OneToOneField(
        "user.User", on_delete=models.CASCADE, related_name="admin_profile"
    )
    role = models.ForeignKey(Role, on_delete=models.PROTECT, related_name="admin_users")
    status = models.CharField(
        max_length=20,
        choices=TeamMemberStatus.choices(),
        default=TeamMemberStatus.Invited.value,
        db_index=True,
    )
    invited_by = models.ForeignKey(
        "user.User",
        null=True,
        on_delete=models.SET_NULL,
        related_name="invited_admin_team_members",
    )
    joined_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)

    class Meta:
        verbose_name = "Admin Profile"
        verbose_name_plural = "Admin Profiles"

    def __str__(self):
        return f"{self.user.email} -- {self.role.name}"

    def revoke_invitation(self):
        """
        Revokes the team member's invitation and anonymizes the associated user.
        Only applicable if the member has not yet accepted the invitation.
        """
        if self.status != TeamMemberStatus.Invited.value:
            raise ValueError("Only invited members can be revoked.")

        revoked_at = timezone.now()
        prefix = self._generate_revocation_prefix(revoked_at)

        # Delete any associated tokens to prevent password creation
        from user.models import Token

        Token.objects.filter(user=self.user).delete()

        self._anonymize_user(prefix)
        self.status = TeamMemberStatus.Revoked.value
        self.save()

    def _generate_revocation_prefix(self, date):
        return f"revoked_{date.strftime('%Y%m%d')}"

    def _anonymize_user(self, prefix):
        user = self.user

        if user.email:
            user.email = f"{prefix}_{user.email}"

        if hasattr(user, "phone") and user.phone:
            last6 = user.phone[-6:]
            masked = "*" * max(0, len(user.phone) - 6)
            user.phone = f"{masked}{last6}"

        user.is_active = False
        user.save()

    def deactivate(self):
        self.status = TeamMemberStatus.Deactivated.value
        self.user.is_active = False
        self.user.save()
        self.save()

    def reactivate(self):
        self.status = TeamMemberStatus.Active.value
        self.user.is_active = True
        self.user.save()
        self.save()

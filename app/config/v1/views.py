from itertools import groupby
from operator import itemgetter

from common.serializers import EmptySerializer
from config.models import DefaultProvider, ProviderSetting
from config.v1.serializers import DefaultProviderSerializer
from django.db.models import Count
from drf_spectacular.utils import OpenApiParameter, OpenApiTypes, extend_schema
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response


class ProviderViewSet(viewsets.GenericViewSet):
    queryset = ProviderSetting.objects.all()
    http_method_names = ["post", "get"]
    filter_backends = [filters.SearchFilter]
    search_fields = ["provider"]

    @action(
        methods=["POST"],
        serializer_class=DefaultProviderSerializer,
        detail=False,
        url_path="set-provider",
    )
    def set_provider(self, request):
        serializer = self.get_serializer(
            data=request.data, context={"user": request.user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"success": True, "message": "Provider set successfully"},
            status=status.HTTP_200_OK,
        )

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="search",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description="Search providers by name (e.g., ?search=Mama)",
            )
        ],
        responses={200: OpenApiTypes.OBJECT},
    )
    @action(
        methods=["GET"],
        serializer_class=EmptySerializer,
        detail=False,
        url_path="get-providers",
    )
    def get_providers(self, request):
        queryset = ProviderSetting.objects.all()
        search_backend = filters.SearchFilter()
        queryset = search_backend.filter_queryset(self.request, queryset, self)
        provider_counts = queryset.values("provider").annotate(
            services=Count("product", distinct=True)
        )

        return Response(
            {"success": True, "data": provider_counts}, status=status.HTTP_200_OK
        )

    @action(
        methods=["GET"],
        serializer_class=EmptySerializer,
        detail=False,
        url_path="provider_details/(?P<provider>[^/.]+)",
    )
    def get_provider(self, request, provider):
        queryset = (
            ProviderSetting.objects.filter(provider=provider)
            .values("provider", "product")
            .distinct()
            .order_by("provider")
        )

        grouped_providers = {
            provider: [item["product"] for item in items]
            for provider, items in groupby(queryset, key=itemgetter("provider"))
        }
        return Response(
            {"success": True, "data": grouped_providers}, status=status.HTTP_200_OK
        )

    @action(
        methods=["GET"],
        serializer_class=EmptySerializer,
        detail=False,
        url_path="get-active-providers",
    )
    def get_active_providers(self, request):
        providers = DefaultProvider.objects.select_related("last_updated_by").all()
        data = DefaultProviderSerializer(providers, many=True).data

        return Response({"success": True, "data": data}, status=status.HTTP_200_OK)

    @action(
        methods=["GET"],
        serializer_class=EmptySerializer,
        detail=False,
        url_path="get-provider-by-product/(?P<product>[^/.]+)",
    )
    def get_provider_by_product(self, request, product):
        queryset = (
            ProviderSetting.objects.filter(product=product)
            .values("provider", "product")
            .distinct()
            .order_by("provider")
        )

        grouped_providers = {
            product: [item["provider"] for item in items]
            for product, items in groupby(queryset, key=itemgetter("product"))
        }

        return Response(
            {"success": True, "data": grouped_providers}, status=status.HTTP_200_OK
        )

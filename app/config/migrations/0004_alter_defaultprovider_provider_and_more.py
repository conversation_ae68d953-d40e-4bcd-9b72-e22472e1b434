
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("config", "0003_defaultprovider_last_updated_at_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="defaultprovider",
            name="provider",
            field=models.CharField(
                choices=[
                    ("Mtn", "Mtn"),
                    ("Glo", "Glo"),
                    ("Airtel", "Airtel"),
                    ("9Mobile", "9Mobile"),
                    ("Dstv", "Dstv"),
                    ("Gotv", "Gotv"),
                    ("Startimes", "Startimes"),
                    ("AbujaElectric", "AbujaElectric"),
                    ("BeninElectric", "BeninElectric"),
                    ("EnuguElectric", "EnuguElectric"),
                    ("EkoElectric", "EkoElectric"),
                    ("IbadanElectric", "IbadanElectric"),
                    ("IkejaElectric", "IkejaElectric"),
                    ("JosElectric", "JosElectric"),
                    ("PortharcourtElectric", "PortharcourtElectric"),
                    ("KadunaElectric", "KadunaElectric"),
                    ("KanoElectric", "KanoElectric"),
                    ("YolaElectric", "YolaElectric"),
                    ("Bet9ja", "Bet9ja"),
                    ("BangBet", "BangBet"),
                    ("SupaBet", "SupaBet"),
                    ("CloudBet", "CloudBet"),
                    ("BetLion", "BetLion"),
                    ("1xBet", "1xBet"),
                    ("MerryBet", "MerryBet"),
                    ("BetWay", "BetWay"),
                    ("BetLand", "BetLand"),
                    ("BetKing", "BetKing"),
                    ("LiveScoreBet", "LiveScoreBet"),
                    ("NaijaBet", "NaijaBet"),
                    ("Nin", "Nin"),
                    ("Bvn", "Bvn"),
                    ("PhoneNumberLookup", "PhoneNumberLookup"),
                    ("Waec", "Waec"),
                    ("Jamb", "Jamb"),
                    ("Transfer", "Transfer"),
                    ("VirtualAccount", "VirtualAccount"),
                    ("RecurringDebit", "RecurringDebit"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="defaultprovider",
            name="service",
            field=models.CharField(
                choices=[
                    ("Mtn", "Mtn"),
                    ("Glo", "Glo"),
                    ("Airtel", "Airtel"),
                    ("9Mobile", "9Mobile"),
                    ("Dstv", "Dstv"),
                    ("Gotv", "Gotv"),
                    ("Startimes", "Startimes"),
                    ("AbujaElectric", "AbujaElectric"),
                    ("BeninElectric", "BeninElectric"),
                    ("EnuguElectric", "EnuguElectric"),
                    ("EkoElectric", "EkoElectric"),
                    ("IbadanElectric", "IbadanElectric"),
                    ("IkejaElectric", "IkejaElectric"),
                    ("JosElectric", "JosElectric"),
                    ("PortharcourtElectric", "PortharcourtElectric"),
                    ("KadunaElectric", "KadunaElectric"),
                    ("KanoElectric", "KanoElectric"),
                    ("YolaElectric", "YolaElectric"),
                    ("Bet9ja", "Bet9ja"),
                    ("BangBet", "BangBet"),
                    ("SupaBet", "SupaBet"),
                    ("CloudBet", "CloudBet"),
                    ("BetLion", "BetLion"),
                    ("1xBet", "1xBet"),
                    ("MerryBet", "MerryBet"),
                    ("BetWay", "BetWay"),
                    ("BetLand", "BetLand"),
                    ("BetKing", "BetKing"),
                    ("LiveScoreBet", "LiveScoreBet"),
                    ("NaijaBet", "NaijaBet"),
                    ("Nin", "Nin"),
                    ("Bvn", "Bvn"),
                    ("PhoneNumberLookup", "PhoneNumberLookup"),
                    ("Waec", "Waec"),
                    ("Jamb", "Jamb"),
                    ("Transfer", "Transfer"),
                    ("VirtualAccount", "VirtualAccount"),
                    ("RecurringDebit", "RecurringDebit"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="providersetting",
            name="service",
            field=models.CharField(
                choices=[
                    ("Mtn", "Mtn"),
                    ("Glo", "Glo"),
                    ("Airtel", "Airtel"),
                    ("9Mobile", "9Mobile"),
                    ("Dstv", "Dstv"),
                    ("Gotv", "Gotv"),
                    ("Startimes", "Startimes"),
                    ("AbujaElectric", "AbujaElectric"),
                    ("BeninElectric", "BeninElectric"),
                    ("EnuguElectric", "EnuguElectric"),
                    ("EkoElectric", "EkoElectric"),
                    ("IbadanElectric", "IbadanElectric"),
                    ("IkejaElectric", "IkejaElectric"),
                    ("JosElectric", "JosElectric"),
                    ("PortharcourtElectric", "PortharcourtElectric"),
                    ("KadunaElectric", "KadunaElectric"),
                    ("KanoElectric", "KanoElectric"),
                    ("YolaElectric", "YolaElectric"),
                    ("Bet9ja", "Bet9ja"),
                    ("BangBet", "BangBet"),
                    ("SupaBet", "SupaBet"),
                    ("CloudBet", "CloudBet"),
                    ("BetLion", "BetLion"),
                    ("1xBet", "1xBet"),
                    ("MerryBet", "MerryBet"),
                    ("BetWay", "BetWay"),
                    ("BetLand", "BetLand"),
                    ("BetKing", "BetKing"),
                    ("LiveScoreBet", "LiveScoreBet"),
                    ("NaijaBet", "NaijaBet"),
                    ("Nin", "Nin"),
                    ("Bvn", "Bvn"),
                    ("PhoneNumberLookup", "PhoneNumberLookup"),
                    ("Waec", "Waec"),
                    ("Jamb", "Jamb"),
                    ("Transfer", "Transfer"),
                    ("VirtualAccount", "VirtualAccount"),
                    ("RecurringDebit", "RecurringDebit"),
                ],
                max_length=50,
            ),
        ),
    ]

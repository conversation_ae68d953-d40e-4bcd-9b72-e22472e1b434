from common.models import AuditableModel
from django.db import models
from django.db.models import F
from wallet.enums import WalletEnums


class Wallet(AuditableModel, WalletEnums):
    business = models.ForeignKey(
        "business.Business",
        on_delete=models.PROTECT,
        related_name="wallets",
        db_index=True,
    )
    type = models.CharField(
        max_length=30, choices=WalletEnums.WALLET_TYPE_CHOICES, db_index=True
    )
    balance = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    bank_name = models.CharField(max_length=100, blank=True, null=True)
    account_number = models.CharField(max_length=30, blank=True, null=True)
    account_name = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        unique_together = ("business", "type")

    def __str__(self):
        if self.business.name is None:
            return f"UNNAMED BUSINESS <{self.business.owner.email}> -- {self.type} -- {self.balance:,.2f}"
        return f"{self.business.name} <{self.business.owner.email}> -- {self.type} -- {self.balance:,.2f}"

    def debit(self, amount):
        self.balance = F("balance") - amount
        self.save()
        self.refresh_from_db()

    def credit(self, amount):
        self.balance = F("balance") + amount
        self.save()
        self.refresh_from_db()

    @property
    def ledger_balance(self):
        """
        The actual balance in the wallet (same as balance field).
        This represents the total amount in the wallet.
        """
        return self.balance

    @property
    def lien_amount(self):
        """
        Total amount under lien for this wallet.
        """
        from transaction.services import TransactionLienService

        return TransactionLienService.get_wallet_lien_amount(self)

    @property
    def available_balance(self):
        """
        Balance available for new transactions (ledger_balance - lien_amount).
        This is what merchants can use for new transactions.
        """
        return self.ledger_balance - self.lien_amount

    @property
    def withdrawable_balance(self):
        """
        Balance that can be withdrawn (same as available_balance).
        This is what merchants can actually withdraw from their wallet.
        """
        return self.available_balance

    def can_debit(self, amount):
        """
        Check if the wallet can be debited by the specified amount.

        Args:
            amount (Decimal): Amount to check for debit

        Returns:
            bool: True if wallet can be debited, False otherwise
        """
        return self.available_balance >= amount

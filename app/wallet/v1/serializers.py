import logging

from business.models import Business, OnboardingStage
from rest_framework import serializers
from wallet.enums import AdminActivatedWalletType, WalletEnums
from wallet.models import Wallet

logger = logging.getLogger(__name__)


class WalletSerializer(serializers.ModelSerializer):
    ledger_balance = serializers.ReadOnlyField()
    available_balance = serializers.ReadOnlyField()
    withdrawable_balance = serializers.ReadOnlyField()
    lien_amount = serializers.ReadOnlyField()

    class Meta:
        model = Wallet
        fields = (
            "balance",
            "ledger_balance",
            "available_balance",
            "withdrawable_balance",
            "lien_amount",
            "type",
            "bank_name",
            "account_number",
            "account_name",
        )

    def to_representation(self, instance):
        """
        Overrides the default representation to set test values for bank details
        if the wallet type is GENERAL and the fields are None.
        """
        representation = super().to_representation(instance)

        if instance.type == WalletEnums.GENERAL:
            if representation.get("bank_name") is None:
                representation["bank_name"] = "Sagecloud Test Bank"
            if representation.get("account_number") is None:
                representation["account_number"] = "**********"
            if representation.get("account_name") is None:
                representation["account_name"] = (
                    f"{instance.business.name.upper()}"
                    if instance.business.name
                    else "TEST BUSINESS"
                )

        return representation


class WalletActivationSerializer(serializers.Serializer):
    business = serializers.PrimaryKeyRelatedField(
        queryset=Business.objects.all(),
        help_text="The ID of the merchant for whom to activate the wallet.",
    )
    wallet_type = serializers.ChoiceField(
        choices=AdminActivatedWalletType.choices(),
        required=True,
        help_text="The type of wallet to activate.",
        error_messages={
            "invalid_choice": f"Invalid wallet type. Valid choices are: {', '.join(AdminActivatedWalletType.values())}"
        },
    )

    def validate(self, data):
        """
        Overall validation to ensure merchant has completed onboarding
        and the wallet type does not already exist for the business.
        """
        business = data.get("business")
        wallet_type = data.get("wallet_type")

        if business.onboarding_stage != OnboardingStage.Completed.value:
            raise serializers.ValidationError(
                {"business": "Merchant has not completed onboarding."}
            )
        if Wallet.objects.filter(business=business, type=wallet_type).exists():
            raise serializers.ValidationError(
                {
                    "wallet_type": f"{wallet_type.replace('_', ' ').title()} Wallet already exists for this merchant."
                }
            )

        return data

    def create(self, validated_data):
        """
        Create and return a new `Wallet` instance, given the validated data.
        """
        business = validated_data["business"]
        wallet_type = validated_data["wallet_type"]

        wallet = Wallet.objects.create(
            business=business,
            type=wallet_type,
        )
        return wallet

import logging
from decimal import Decimal

from audit.models import AuditLog
from audit.utils import log_user_action
from business.models import Business
from common.kgs import generate_uuid7
from django.db import models, transaction
from transaction.enums import (
    TransactionClassEnum,
    TransactionModeEnum,
    TransactionStatusEnum,
)
from transaction.models import Transaction
from wallet.exceptions import WalletException
from wallet.models import Wallet

logger = logging.getLogger(__name__)


class WalletService:
    def __init__(self, wallet: Wallet):
        """
        Initialize a WalletService instance for a given Wallet instance.

        Args:
            wallet (Wallet): Wallet instance to perform operations on.
        """
        self.wallet = wallet

    @transaction.atomic
    def debit(
        self,
        amount: Decimal,
        charge: Decimal,
        *,
        business: "Business",
        txn_class: str,
        type: str,
        narration: str,
        merchant_reference: str = None,
        provider: str,
    ) -> Transaction:
        """
        Debit a given amount from the wallet.

        Args:
            amount (Decimal): The amount to debit from the wallet.
            charge (Decimal):
            business (Business): The business associated with the wallet.
            txn_class (str): The transaction class.
            type (str): The transaction type.
            narration (str): A description of the debit transaction.
            merchant_reference (str): A reference provided by the merchant.
            provider (str): provider routing this transaction.
        Returns:
            Transaction: The transaction instance created.
        """

        if amount <= 0:
            raise WalletException("Debit amount must be positive.")

        wallet = Wallet.objects.select_for_update().filter(pk=self.wallet.id).first()
        if not wallet:
            raise WalletException("Invalid Wallet.")

        net_amount = amount + charge

        # Check available balance (considering liens) instead of just balance
        if not wallet.can_debit(net_amount):
            raise WalletException(
                f"Insufficient available balance to debit {net_amount}. "
                f"Available: {wallet.available_balance}, Required: {net_amount}"
            )

        old_balance = wallet.balance
        wallet.debit(net_amount)

        new_balance = wallet.balance

        reference = generate_uuid7()
        txn = Transaction.objects.create(
            wallet=self.wallet,
            business=business,
            reference=reference,
            merchant_reference=merchant_reference or reference,
            status=TransactionStatusEnum.PENDING.value,
            mode=TransactionModeEnum.DEBIT.value,
            txn_class=txn_class,
            type=type,
            amount=amount,
            charge=charge,
            net_amount=net_amount,
            old_balance=old_balance,
            new_balance=new_balance,
            narration=narration,
            is_wallet_impacted=True,
            provider=provider,
            relationship_manager=business.relationship_manager,
        )

        # Log wallet debit operation
        try:
            log_user_action(
                request=None,  # Service layer doesn't have request context
                user=business.owner,  # Use business owner as the user
                action=AuditLog.WALLET_UPDATE,
                description=f"Wallet debited: {amount} (charge: {charge})",
                resource_type="Wallet",
                resource_id=str(wallet.id),
                metadata={
                    "operation": "DEBIT",
                    "amount": str(amount),
                    "charge": str(charge),
                    "net_amount": str(net_amount),
                    "old_balance": str(old_balance),
                    "new_balance": str(new_balance),
                    "transaction_reference": reference,
                    "business_id": str(business.id),
                    "wallet_type": wallet.type,
                },
            )
        except Exception as e:
            logger.warning(f"Failed to log wallet debit audit: {e}")

        return txn

    @transaction.atomic
    def credit(
        self,
        amount: Decimal,
        *,
        business: "Business",
        txn_class: TransactionClassEnum,
        type: str,
        narration: str,
        merchant_reference: str,
        impact_wallet: bool = True,
    ) -> Transaction:
        """
        Credits the specified amount to the wallet and records the transaction.

        Args:
            amount (Decimal): The amount to be credited to the wallet.
            business (Business): The business associated with the wallet.
            txn_class (str): The transaction class, indicating the category of the transaction.
            type (str): The type of transaction being processed.
            narration (str): A description or note associated with the transaction.
            merchant_reference (str): A unique reference provided by the merchant for the transaction.
            impact_wallet (bool, optional): If True, the wallet balance is updated. Defaults to True.

        Returns:
            Transaction: The created transaction record.

        Raises:
            WalletException: If the credit amount is not positive.
        """

        if amount <= 0:
            raise WalletException("Credit amount must be positive.")

        self.wallet.refresh_from_db(fields=["balance"])

        old_balance = self.wallet.balance

        if impact_wallet:
            self.wallet.balance = models.F("balance") + amount
            self.wallet.save(update_fields=["balance"])
            self.wallet.refresh_from_db(fields=["balance"])

        new_balance = self.wallet.balance

        txn = Transaction.objects.create(
            wallet=self.wallet,
            business=business,
            reference=generate_uuid7(),
            merchant_reference=merchant_reference,
            status=TransactionStatusEnum.SUCCESSFUL.value,
            mode=TransactionModeEnum.CREDIT.value,
            txn_class=txn_class,
            type=type,
            amount=amount,
            old_balance=old_balance,
            new_balance=new_balance,
            narration=narration,
            is_wallet_impacted=impact_wallet,
        )

        # Log wallet credit operation
        try:
            log_user_action(
                request=None,  # Service layer doesn't have request context
                user=business.owner,  # Use business owner as the user
                action=(
                    AuditLog.WALLET_FUNDING if impact_wallet else AuditLog.WALLET_UPDATE
                ),
                description=f"Wallet credited: {amount}"
                + (" (balance impacted)" if impact_wallet else " (no balance impact)"),
                resource_type="Wallet",
                resource_id=str(self.wallet.id),
                metadata={
                    "operation": "CREDIT",
                    "amount": str(amount),
                    "old_balance": str(old_balance),
                    "new_balance": str(new_balance),
                    "transaction_reference": txn.reference,
                    "business_id": str(business.id),
                    "wallet_type": self.wallet.type,
                    "impact_wallet": impact_wallet,
                },
            )
        except Exception as e:
            logger.warning(f"Failed to log wallet credit audit: {e}")

        return txn

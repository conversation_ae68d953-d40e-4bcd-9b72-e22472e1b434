from decimal import Decimal

from business.models import Business
from common.dtos import CoreServiceResponse
from common.enums import CoreServiceResponseStatus, VasGateStatus
from django.db import transaction
from ledger.enums import LedgerTypeEnum
from ledger.tasks import credit_ledger
from rest_framework import serializers
from transaction.dtos import CreateBaseTransaction<PERSON>arams
from transaction.enums import TransactionClassEnum, TransactionStatusEnum
from transaction.handlers.base import TransactionHandler
from transaction.handlers.withdrawal import WithdrawalTransactionHandler
from user.enums import PinEnum
from user.models import User
from user.utils import get_data_from_cache
from vas.integrations.transfers import FundsTransferVASGateClient
from wallet.enums import WalletEnums

WITHDRAWAL_WALLET_CHOICES = [
    (WalletEnums.COMMISSION, "Commission Wallet"),
    (WalletEnums.KOLOMONI_VIRTUAL_ACCOUNT, "Kolomoni Virtual Account Wallet"),
    (WalletEnums.WEMA_VIRTUAL_ACCOUNT, "Wema Virtual Account Wallet"),
    (WalletEnums.ACCESS_VIRTUAL_ACCOUNT, "Access Virtual Account Wallet"),
]


class WithdrawalSerializer(serializers.Serializer):
    """
    Serializer for merchant withdrawal requests.
    Allows merchants to withdraw funds from their wallets to their settlement accounts.
    Requires PIN authentication for security.
    """

    amount = serializers.DecimalField(
        max_digits=20, decimal_places=2, min_value=Decimal("1.00")
    )
    wallet_type = serializers.ChoiceField(
        choices=WITHDRAWAL_WALLET_CHOICES, help_text="Type of wallet to withdraw from"
    )
    pin_token = serializers.CharField(
        write_only=True,
        help_text="PIN verification token obtained from /business/verify-pin endpoint",
    )

    def validate(self, attrs):
        """
        Validate withdrawal request including PIN token authentication, settlement account and sufficient funds.
        """
        business: Business = self.context["business"]
        user: User = business.owner
        amount = attrs["amount"]
        wallet_type = attrs["wallet_type"]
        pin_token = attrs["pin_token"]

        # Validate PIN token first
        token_data = get_data_from_cache(pin_token)
        if not token_data:
            raise serializers.ValidationError(
                {
                    "pin_token": "Invalid or expired PIN token. Please verify your PIN again."
                }
            )

        if token_data.get("action") != PinEnum.Withdrawal.value:
            raise serializers.ValidationError(
                {"pin_token": "PIN token is not valid for withdrawal operations."}
            )

        if token_data.get("user_id") != user.id:
            raise serializers.ValidationError(
                {"pin_token": "PIN token does not belong to the current user."}
            )

        # Check if business has active settlement account
        settlement_account = business.get_active_settlement_account()
        if not settlement_account:
            raise serializers.ValidationError(
                "No active settlement account found. Please add a settlement account first."
            )

        # Check if business can withdraw from the specified wallet
        can_withdraw, reason = business.can_withdraw_from_wallet(wallet_type, amount)
        if not can_withdraw:
            raise serializers.ValidationError(reason)

        # Store settlement account details for later use
        attrs["settlement_account"] = settlement_account

        return attrs

    def save(self, **kwargs):
        """
        Process the withdrawal request.
        """
        data = self.validated_data
        business: Business = self.context["business"]
        settlement_account = data["settlement_account"]
        amount = data["amount"]
        wallet_type = data["wallet_type"]

        # Generate reference and narration internally
        reference = None  # Will be auto-generated by transaction handler
        wallet_name = dict(self.fields["wallet_type"].choices).get(
            wallet_type, wallet_type
        )
        narration = f"Withdrawal of ₦{amount} from {wallet_name} to {settlement_account.account_name}"

        wallet = business._get_wallet(wallet_type, for_update=True)

        handler = TransactionHandler()
        params = self.__create_base_txn_params(
            wallet, business, reference, amount, narration, settlement_account
        )

        # Debit the wallet
        txn = handler.debit_wallet(params)

        # Create VAS transaction record
        vas_extra_fields = self.__get_extra_fields(settlement_account, wallet_type)
        vas_txn = WithdrawalTransactionHandler().create_vas_transaction(
            txn, vas_extra_fields
        )

        # Create ledger entry
        credit_ledger.delay(LedgerTypeEnum.WITHDRAWAL.value, txn.id)

        # Make external API call for funds transfer
        data["reference"] = txn.reference
        _, response = FundsTransferVASGateClient().funds_transfer(
            {
                "bank_code": settlement_account.bank_code,
                "account_number": settlement_account.account_number,
                "amount": float(amount),
                "reference": txn.reference,
                "narration": narration,
            }
        )

        # Process API response
        if response.status == VasGateStatus.Success.value:
            with transaction.atomic():
                txn.status = TransactionStatusEnum.SUCCESSFUL.value
                txn.save()

                handler.update_vas_transaction(txn, vas_txn, {"status": txn.status})

            return CoreServiceResponse(
                success=True,
                status=CoreServiceResponseStatus.Success.value,
                message="Withdrawal successful.",
                reference=txn.reference,
                data={
                    **response.data,
                    "wallet_type": wallet_type,
                    "settlement_account": {
                        "account_number": settlement_account.account_number,
                        "account_name": settlement_account.account_name,
                        "bank_name": settlement_account.bank_name,
                    },
                },
            )
        else:
            # Handle failed withdrawal
            with transaction.atomic():
                txn.status = TransactionStatusEnum.FAILED.value
                txn.save()

                handler.update_vas_transaction(txn, vas_txn, {"status": txn.status})

            return CoreServiceResponse(
                success=False,
                status=CoreServiceResponseStatus.Failed.value,
                message=response.message or "Withdrawal failed.",
                reference=txn.reference,
                data=response.data or {},
            )

    @staticmethod
    def __create_base_txn_params(
        wallet, business, reference, amount, narration, settlement_account
    ):
        """Create base transaction parameters."""
        return CreateBaseTransactionParams(
            wallet=wallet,
            business=business,
            amount=amount,
            txn_class=TransactionClassEnum.WITHDRAWAL.value,
            type=TransactionClassEnum.WITHDRAWAL.value,
            narration=narration,
            reference=reference,
        )

    @staticmethod
    def __get_extra_fields(settlement_account, wallet_type):
        """Get extra fields for VAS transaction."""
        return {
            "settlement_account_number": settlement_account.account_number,
            "settlement_account_name": settlement_account.account_name,
            "settlement_bank_name": settlement_account.bank_name,
            "settlement_bank_code": settlement_account.bank_code,
            "source_wallet_type": wallet_type,
        }


class WalletBalanceSerializer(serializers.Serializer):
    """
    Serializer to get wallet balances for withdrawal.
    """

    def to_representation(self, business: Business):
        """
        Return wallet balances for the business.
        """
        wallets_data = []
        withdrawable_wallets = WITHDRAWAL_WALLET_CHOICES

        for wallet_type, wallet_name in withdrawable_wallets:
            try:
                wallet = business._get_wallet(wallet_type)
                wallets_data.append(
                    {
                        "wallet_type": wallet_type,
                        "wallet_name": wallet_name,
                        "balance": wallet.balance,
                        "available_balance": wallet.available_balance,
                        "withdrawable_balance": wallet.withdrawable_balance,
                        "lien_amount": wallet.lien_amount,
                    }
                )
            except Exception:
                # Wallet doesn't exist, skip it
                continue

        return {
            "settlement_account": self._get_settlement_account_data(business),
            "wallets": wallets_data,
        }

    def _get_settlement_account_data(self, business: Business):
        """Get settlement account data."""
        settlement_account = business.get_active_settlement_account()
        if settlement_account:
            return {
                "account_number": settlement_account.account_number,
                "account_name": settlement_account.account_name,
                "bank_name": settlement_account.bank_name,
                "bank_code": settlement_account.bank_code,
            }
        return None

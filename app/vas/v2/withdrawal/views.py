from common.decorators import enforce_unique_reference
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import generics, status
from rest_framework.response import Response

from ..auth.authentication import BusinessJWTAuthentication
from .serializers import WalletBalanceSerializer, WithdrawalSerializer


@extend_schema_view(
    post=extend_schema(
        summary="Process Withdrawal",
        description="Process a withdrawal request from merchant wallet to settlement account. "
        "Merchants can withdraw from commission wallet or virtual account wallets.",
        tags=["vas-withdrawal"],
    )
)
class WithdrawalView(generics.GenericAPIView):
    """
    API endpoint for processing merchant withdrawals.

    Allows merchants to withdraw funds from their wallets (commission or virtual account wallets)
    to their registered settlement accounts.
    """

    serializer_class = WithdrawalSerializer
    authentication_classes = [BusinessJWTAuthentication]

    @enforce_unique_reference
    def post(self, request, *args, **kwargs):
        """
        Process a withdrawal request.

        The withdrawal will:
        1. Validate the business has an active settlement account
        2. Check sufficient funds in the specified wallet
        3. Debit the wallet and create transaction records
        4. Make external API call to transfer funds
        5. Update transaction status based on API response
        """
        business = request.user
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        result = serializer.save()
        return Response(result.to_dict(), status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        summary="Get Wallet Balances",
        description="Get wallet balances and settlement account information for withdrawal. "
        "Shows available balances for all withdrawable wallets.",
        tags=["vas-withdrawal"],
    )
)
class WalletBalanceView(generics.GenericAPIView):
    """
    API endpoint to get wallet balances for withdrawal.

    Returns information about:
    - Available wallet balances (commission and virtual account wallets)
    - Settlement account details
    - Withdrawable amounts (considering liens)
    """

    serializer_class = WalletBalanceSerializer
    authentication_classes = [BusinessJWTAuthentication]

    def get(self, request, *args, **kwargs):
        """
        Get wallet balances and settlement account information.
        """
        business = request.user
        serializer = self.get_serializer()
        data = serializer.to_representation(business)

        return Response(
            {
                "success": True,
                "message": "Wallet balances retrieved successfully",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

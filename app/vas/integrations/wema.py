import logging

from common.dtos import VasGateServiceResponse
from vas.integrations.base import BaseVASGateClient

logger = logging.getLogger(__name__)


class WemaVASGateClient(BaseVASGateClient):
    """
    Wema Bank integration client for admin operations.
    Handles name enquiry, funds transfer, and account statements.
    """

    def __init__(self):
        super().__init__()

    def name_enquiry(self, payload) -> tuple[int, VasGateServiceResponse]:

        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/transfer/name-enquiry/",
            data={**payload},
        )

        return status_code, VasGateServiceResponse(**(response or {}))

    def funds_transfer(self, payload) -> tuple[int, VasGateServiceResponse]:

        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/transfer/funds-transfer/",
            data={**payload},
        )

        return status_code, VasGateServiceResponse(**(response or {}))

    def account_statements(self, payload) -> tuple[int, VasGateServiceResponse]:

        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/wallet-balance/statement/get-statement/",
            data={**payload},
        )

        return status_code, VasGateServiceResponse(**(response or {}))

    def get_balance(self, payload) -> tuple[int, VasGateServiceResponse]:

        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/wallet-balance/balance/check-balance/",
            data={**payload},
        )

        return status_code, VasGateServiceResponse(**(response or {}))

from vas.integrations.base import BaseVASGateClient


class MamaAfricaClient(BaseVASGateClient):
    def __init__(self):
        super().__init__()

    def generate_bulk_epin(self, payload) -> tuple:
        response, status_code = self._make_request(
            method="POST",
            url=f"{self.base_url}/api/v1/console-services/generate-bulk-epin/",
            data=payload,
        )

        return response, status_code

from decimal import Decimal

from audit.models import AuditLog
from audit.utils import log_user_action
from business.models import Business
from common.enums import VABankProvider, VasGateStatus
from common.kgs import generate_uuid7
from core import settings
from django.db import transaction
from transaction.enums import (
    TransactionClassEnum,
    TransactionModeEnum,
    TransactionStatusEnum,
)
from transaction.models import Transaction
from transaction.models.virtual_account import VirtualAccountVasTransaction
from vas.integrations.virtual_account import VirtualAccountVasClient
from vas.v3.virtual_account.dtos import KolomoniWebhookData
from vas.v3.virtual_account.exceptions import VirtualAccountError
from vas.v3.virtual_account.utils import register_kolomoni_va_transaction_single_task
from virtual_account.models import VirtualAccount


class VirtualAccountHandler:

    @staticmethod
    def create_virtual_account(
        business: Business, data: dict, is_for_wallet_funding=False
    ) -> dict:
        data["bvn"] = data["bvn"] or "***********"
        status_code, response = VirtualAccountVasClient().create_account(
            {
                "bvn": data["bvn"],
                "account_name": data["account_name"],
                "email": data["email"],
                "provider": data["bank"],
            }
        )

        if not response.status or response.status == VasGateStatus.Failed.value:
            raise VirtualAccountError(response.message)

        VirtualAccount.objects.create(
            business=business,
            account_name=response.data["account_name"],
            account_number=response.data["account_number"],
            account_email=response.data["account_email"],
            bank_code=response.data["bank_code"],
            bank_name=data["bank"],
            is_static=True,
            bvn=data["bvn"],
            account_reference=response.data["account_reference"],
            is_for_wallet_funding=is_for_wallet_funding,
        )
        return response.data


class KolomoniWebhookHandler:

    def handle(self, data: KolomoniWebhookData):
        if not self.__is_valid_credit_account(
            data.recipient_account_number, settings.KOLOMONI_VA_ACCOUNT_PREFIX
        ):
            raise VirtualAccountError("This account number is invalid")

        if self.__is_duplicate_transaction(data.session_id):
            raise VirtualAccountError("Duplicate transaction", 409)

        va = self.get_virtual_account(data.recipient_account_number)

        business = va.business
        reference = generate_uuid7()
        charge = 0  # @TODO: get this from the fee settings

        with transaction.atomic():

            wallet = business.get_kolomoni_va_wallet(for_update=True)

            Transaction.objects.create(
                wallet=wallet,
                business=business,
                reference=reference,
                merchant_reference=reference,
                status=TransactionStatusEnum.PENDING.value,
                mode=TransactionModeEnum.CREDIT.value,
                txn_class=TransactionClassEnum.VIRTUAL_ACCOUNT.value,
                type=VABankProvider.Kolomoni.value,
                amount=Decimal(data.amount),
                charge=Decimal(charge),
                net_amount=Decimal(int(data.amount) - charge),
                old_balance=0,
                new_balance=0,
                narration=self.__narration(va),
                is_wallet_impacted=False,
            )

            va_txn = VirtualAccountVasTransaction.objects.create(
                wallet=wallet,
                reference=reference,
                merchant_reference=reference,
                status=TransactionStatusEnum.PENDING.value,
                mode=TransactionModeEnum.CREDIT.value,
                amount=Decimal(data.amount),
                charge=Decimal(charge),
                net_amount=Decimal(int(data.amount) - charge),
                narration=self.__narration(va),
                business=business,
                session_id=data.session_id,
                source_account_number=data.sender_account_number,
                source_account_name=data.sender_account_name,
                source_bank_code=data.sender_bank_code,
                source_bank_name=data.sender_bank,
                recipient_account_number=data.recipient_account_number,
                recipient_account_name=data.recipient_account_name,
                recipient_bank_name=VABankProvider.Kolomoni.bank_name(),
                recipient_bank_code=VABankProvider.Kolomoni.bank_code(),
            )

            register_kolomoni_va_transaction_single_task(va_txn)

            # Log webhook processing
            try:
                log_user_action(
                    request=None,  # Webhook doesn't have request context
                    user=business.owner,  # Use business owner as the user
                    action=AuditLog.WEBHOOK_RECEIVED,
                    description=f"Kolomoni webhook processed: {data.amount} from {data.sender_account_name}",
                    resource_type="Webhook",
                    resource_id=data.session_id,
                    metadata={
                        "webhook_type": "KOLOMONI_VIRTUAL_ACCOUNT",
                        "session_id": data.session_id,
                        "amount": str(data.amount),
                        "sender_account": data.sender_account_number,
                        "sender_name": data.sender_account_name,
                        "recipient_account": data.recipient_account_number,
                        "business_id": str(business.id),
                        "transaction_reference": reference,
                        "virtual_account_id": str(va.id),
                    },
                )
            except Exception:
                # Don't fail webhook processing if audit logging fails
                pass

    @staticmethod
    def __is_valid_credit_account(credit_account: str, account_prefix: str) -> bool:
        return credit_account.startswith(account_prefix)

    @staticmethod
    def __is_duplicate_transaction(session_id: str) -> bool:
        return VirtualAccountVasTransaction.objects.filter(
            session_id=session_id
        ).exists()

    @staticmethod
    def get_virtual_account(account_number: str) -> VirtualAccount:
        virtual_account = VirtualAccount.objects.filter(
            account_number=account_number
        ).first()
        if not virtual_account:
            raise VirtualAccountError("Invalid Credit Account", 404)

        return virtual_account

    @staticmethod
    def __narration(virtual_account: VirtualAccount) -> str:
        if virtual_account.is_for_wallet_funding:
            return f"Wallet funding | {virtual_account.account_number}"
        else:
            return f"Virtual-Account Inflow | {virtual_account.account_number} | {virtual_account.account_name}"

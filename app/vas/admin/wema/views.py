import logging

from common.pagination import LargeDatasetKeySetPagination
from common.permissions import IsAdmin
from django_filters import FilterSet
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import filters, generics, status, viewsets
from rest_framework.response import Response
from transaction.models.wema_payout import WemaPayoutHistory

from .serializers import (
    WemaAccountStatementsSerializer,
    WemaFundsTransferSerializer,
    WemaGetBalanceSerializer,
    WemaNameEnquirySerializer,
    WemaPayoutHistorySerializer,
)

logger = logging.getLogger(__name__)


@extend_schema_view(
    post=extend_schema(
        summary="Wema Name Enquiry",
        description="Perform name enquiry for account verification using Wema provider. Admin access required.",
        tags=["Admin Wema"],
    )
)
class AdminWemaNameEnquiryView(generics.GenericAPIView):
    """Admin endpoint for Wema name enquiry operations"""

    serializer_class = WemaNameEnquirySerializer
    permission_classes = [IsAdmin]

    def post(self, request, *args, **kwargs):
        """Perform name enquiry using Wema provider"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            result = serializer.save()
            return Response(result.to_dict(), status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error in Wema name enquiry: {str(e)}")
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "An error occurred during name enquiry",
                    "data": {},
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@extend_schema_view(
    post=extend_schema(
        summary="Wema Funds Transfer",
        description="Perform funds transfer using Wema provider. Creates payout history record. Admin access required.",
        tags=["Admin Wema"],
    )
)
class AdminWemaFundsTransferView(generics.GenericAPIView):
    """Admin endpoint for Wema funds transfer operations"""

    serializer_class = WemaFundsTransferSerializer
    permission_classes = [IsAdmin]

    def post(self, request, *args, **kwargs):
        """Perform funds transfer using Wema provider"""
        serializer = self.get_serializer(
            data=request.data, context={"user": request.user}
        )
        serializer.is_valid(raise_exception=True)

        try:
            result = serializer.save()
            return Response(result.to_dict(), status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error in Wema funds transfer: {str(e)}")
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "An error occurred during funds transfer",
                    "data": {},
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AdminWemaPayoutHistoryFilter(FilterSet):
    """Filter for Wema payout history"""

    class Meta:
        model = WemaPayoutHistory
        fields = {
            "status": ["exact"],
            "initiated_by": ["exact"],
            "recipient_bank_code": ["exact"],
            "created_at": ["gte", "lte"],
            "amount": ["gte", "lte"],
        }


@extend_schema_view(
    list=extend_schema(
        summary="List Wema Payout History",
        description="Get paginated list of Wema payout history records. Admin access required.",
        tags=["Admin Wema"],
    ),
    retrieve=extend_schema(
        summary="Get Wema Payout History Detail",
        description="Get detailed information about a specific Wema payout. Admin access required.",
        tags=["Admin Wema"],
    ),
)
class AdminWemaPayoutHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """Admin viewset for viewing Wema payout history"""

    permission_classes = [IsAdmin]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = AdminWemaPayoutHistoryFilter
    pagination_class = LargeDatasetKeySetPagination
    search_fields = ["reference", "recipient_account_number", "recipient_account_name"]
    ordering_fields = ["created_at", "amount", "status"]
    ordering = ["-created_at"]
    serializer_class = WemaPayoutHistorySerializer

    def get_queryset(self):
        """Get all Wema payout history records"""
        return WemaPayoutHistory.objects.all().select_related("initiated_by")


@extend_schema_view(
    post=extend_schema(
        summary="Wema Account Statements",
        description="Get account statements using Wema provider. Admin access required.",
        tags=["Admin Wema"],
    )
)
class AdminWemaAccountStatementsView(generics.GenericAPIView):
    """Admin endpoint for Wema account statements operations"""

    serializer_class = WemaAccountStatementsSerializer
    permission_classes = [IsAdmin]

    def post(self, request, *args, **kwargs):
        """Get account statements using Wema provider"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            result = serializer.save()
            if result.success:
                return Response(result.to_dict(), status=status.HTTP_200_OK)
            else:
                # Wema API failed or returned error
                return Response(result.to_dict(), status=status.HTTP_502_BAD_GATEWAY)

        except Exception as e:
            logger.error(f"Error in Wema account statements: {str(e)}")
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "An error occurred while getting account statements",
                    "data": {},
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@extend_schema_view(
    post=extend_schema(
        summary="Wema Get Balance",
        description="Get account balance using Wema provider. Admin access required.",
        tags=["Admin Wema"],
    )
)
class AdminWemaGetBalanceView(generics.GenericAPIView):
    """Admin endpoint for Wema get balance operations"""

    serializer_class = WemaGetBalanceSerializer
    permission_classes = [IsAdmin]

    def post(self, request, *args, **kwargs):
        """Get account balance using Wema provider"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            result = serializer.save()
            if result.success:
                return Response(result.to_dict(), status=status.HTTP_200_OK)
            else:
                # Wema API failed or returned error
                return Response(result.to_dict(), status=status.HTTP_502_BAD_GATEWAY)

        except Exception as e:
            logger.error(f"Error in Wema get balance: {str(e)}")
            return Response(
                {
                    "success": False,
                    "status": "failed",
                    "message": "An error occurred while getting account balance",
                    "data": {},
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

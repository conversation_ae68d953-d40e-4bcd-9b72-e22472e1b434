from django.urls import include, path
from rest_framework.routers import DefaultRouter

from .views import (
    AdminWemaAccountStatementsView,
    AdminWemaFundsTransferView,
    AdminWemaGetBalanceView,
    AdminWemaNameEnquiryView,
    AdminWemaPayoutHistoryViewSet,
)

app_name = "admin_wema"

# Router for viewsets
router = DefaultRouter()
router.register(
    "payout-history",
    AdminWemaPayoutHistoryViewSet,
    basename="admin-wema-payout-history",
)

urlpatterns = [
    # Wema operations endpoints
    path(
        "name-enquiry/",
        AdminWemaNameEnquiryView.as_view(),
        name="admin-wema-name-enquiry",
    ),
    path(
        "funds-transfer/",
        AdminWemaFundsTransferView.as_view(),
        name="admin-wema-funds-transfer",
    ),
    path(
        "account-statements/",
        AdminWemaAccountStatementsView.as_view(),
        name="admin-wema-account-statements",
    ),
    path(
        "get-balance/",
        AdminWemaGetBalanceView.as_view(),
        name="admin-wema-get-balance",
    ),
    path("", include(router.urls)),
]

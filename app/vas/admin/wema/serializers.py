import logging
from decimal import Decimal

from common.dtos import CoreServiceResponse
from common.enums import CoreServiceResponseStatus, VasGateStatus
from common.kgs import generate_uuid7
from config.models import Bank
from django.db.models import Q
from rest_framework import serializers
from transaction.enums import TransactionStatusEnum
from transaction.models.wema_payout import WemaPayoutHistory
from vas.integrations.wema import WemaVASGateClient

logger = logging.getLogger(__name__)


class WemaPayoutHistorySerializer(serializers.ModelSerializer):
    """Serializer for Wema payout history"""

    initiated_by_email = serializers.Char<PERSON><PERSON>(
        source="initiated_by.email", read_only=True
    )
    initiated_by_name = serializers.CharField(
        source="initiated_by.fullname", read_only=True
    )

    class Meta:
        model = WemaPayoutHistory
        fields = "__all__"


class WemaNameEnquirySerializer(serializers.Serializer):
    """Serializer for Wema name enquiry operations"""

    account_number = serializers.Char<PERSON>ield(max_length=10, min_length=10)
    bank_code = serializers.Char<PERSON>ield(max_length=6)

    def validate_bank_code(self, value):
        """Validate that the bank code exists"""
        bank = Bank.objects.filter(
            Q(institution_code=value) | Q(bank_code=value)
        ).first()
        if not bank:
            raise serializers.ValidationError("Invalid bank code provided")
        return value

    def save(self, **kwargs):
        """Perform name enquiry using Wema provider"""
        data = self.validated_data

        client = WemaVASGateClient()
        status_code, response = client.name_enquiry(
            {
                "account_number": data["account_number"],
                "bank_code": data["bank_code"],
            }
        )

        return CoreServiceResponse(
            success=response.status == VasGateStatus.Success.value,
            status=response.status,
            message=response.message,
            data=response.data,
        )


class WemaFundsTransferSerializer(serializers.Serializer):
    """Serializer for Wema funds transfer operations"""

    reference = serializers.CharField(required=False)
    bank_code = serializers.CharField(max_length=6)
    account_number = serializers.CharField(max_length=10, min_length=10)
    account_name = serializers.CharField(max_length=255)
    amount = serializers.DecimalField(
        max_digits=20, decimal_places=2, min_value=Decimal("1.00")
    )
    narration = serializers.CharField(max_length=200, min_length=3)

    def validate_bank_code(self, value):
        """Validate that the bank code exists"""
        bank = Bank.objects.filter(
            Q(institution_code=value) | Q(bank_code=value)
        ).first()
        if not bank:
            raise serializers.ValidationError("Invalid bank code provided")
        return value

    def validate(self, attrs):
        """Additional validation and data preparation"""
        data = super().validate(attrs)

        # Generate reference if not provided
        if not data.get("reference"):
            data["reference"] = generate_uuid7()

        # Get bank details
        bank_code = attrs.get("bank_code")
        bank = Bank.objects.filter(
            Q(institution_code=bank_code) | Q(bank_code=bank_code)
        ).first()

        data["bank_code"] = bank.institution_code
        data["bank_name"] = bank.name

        return data

    def save(self, **kwargs):
        """Perform funds transfer and create payout history"""
        data = self.validated_data
        user = self.context.get("user")

        if not user:
            raise serializers.ValidationError("User context is required")

        # Create payout history record
        payout_history = WemaPayoutHistory.objects.create(
            reference=data["reference"],
            recipient_account_number=data["account_number"],
            recipient_account_name=data["account_name"],
            recipient_bank_name=data["bank_name"],
            recipient_bank_code=data["bank_code"],
            amount=data["amount"],
            narration=data["narration"],
            initiated_by=user,
            status=TransactionStatusEnum.PENDING.value,
        )

        try:
            # Make the transfer request
            client = WemaVASGateClient()
            status_code, response = client.funds_transfer(
                {
                    "bank_code": data["bank_code"],
                    "account_number": data["account_number"],
                    "amount": float(data["amount"]),
                    "reference": data["reference"],
                    "narration": data["narration"],
                }
            )

            # Update payout history based on response
            if response.status == VasGateStatus.Success.value:
                payout_history.mark_as_successful(response.data)

                return CoreServiceResponse(
                    success=True,
                    status=CoreServiceResponseStatus.Success.value,
                    message="Wema funds transfer successful.",
                    reference=data["reference"],
                    data={**response.data, "payout_history_id": payout_history.id},
                )

            elif response.status == VasGateStatus.Failed.value:
                payout_history.mark_as_failed(response.data, response.message)

                return CoreServiceResponse(
                    success=False,
                    status=CoreServiceResponseStatus.Failed.value,
                    message=response.message or "Wema funds transfer failed.",
                    reference=data["reference"],
                    data={**response.data, "payout_history_id": payout_history.id},
                )

            else:  # Pending
                payout_history.mark_as_pending(response.data)

                return CoreServiceResponse(
                    success=False,
                    status=CoreServiceResponseStatus.Pending.value,
                    message="Wema funds transfer is pending.",
                    reference=data["reference"],
                    data={**response.data, "payout_history_id": payout_history.id},
                )

        except Exception as e:
            logger.error(f"Error processing Wema transfer: {str(e)}")
            payout_history.mark_as_failed(error_message=str(e))

            return CoreServiceResponse(
                success=False,
                status=CoreServiceResponseStatus.Failed.value,
                message="An error occurred while processing the transfer.",
                reference=data["reference"],
                data={"payout_history_id": payout_history.id},
            )


class WemaAccountStatementsSerializer(serializers.Serializer):
    """Serializer for Wema account statements operations"""

    start_date = serializers.DateField(
        help_text="Start date for account statements (YYYY-MM-DD format)"
    )
    end_date = serializers.DateField(
        help_text="End date for account statements (YYYY-MM-DD format)"
    )

    def validate(self, attrs):
        """Validate date range"""
        start_date = attrs.get("start_date")
        end_date = attrs.get("end_date")

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError(
                "start_date cannot be later than end_date"
            )

        return attrs

    def save(self):
        """Perform account statements using Wema provider"""
        data = self.validated_data
        try:
            client = WemaVASGateClient()
            status_code, response = client.account_statements(
                {
                    "start_date": data["start_date"],
                    "end_date": data["end_date"],
                }
            )

            # Check if the API call failed (network issues, timeouts, etc.)
            if status_code != 200 or not response or not response.status:
                return CoreServiceResponse(
                    success=False,
                    status=CoreServiceResponseStatus.Pending.value,
                    message=(
                        response.message
                        if response and response.message
                        else "Failed to connect to Wema API"
                    ),
                    data={},
                )

        except Exception as e:
            logger.error(f"Error processing Wema account statements: {str(e)}")
            return CoreServiceResponse(
                success=False,
                status=CoreServiceResponseStatus.Failed.value,
                message="An error occurred while processing the account statements.",
                data={},
            )

        return CoreServiceResponse(
            success=response.status == VasGateStatus.Success.value,
            status=response.status,
            message=response.message,
            data=response.data,
        )


class WemaGetBalanceSerializer(serializers.Serializer):
    """Serializer for Wema get balance operations"""

    def save(self):
        """Perform get balance using Wema provider"""
        try:
            client = WemaVASGateClient()
            status_code, response = client.get_balance({})

            # Check if the API call failed (network issues, timeouts, etc.)
            if status_code != 200 or not response or not response.status:
                return CoreServiceResponse(
                    success=False,
                    status=CoreServiceResponseStatus.Pending.value,
                    message=(
                        response.message
                        if response and response.message
                        else "Failed to connect to Wema API"
                    ),
                    data={},
                )

        except Exception as e:
            logger.error(f"Error processing Wema get balance: {str(e)}")
            return CoreServiceResponse(
                success=False,
                status=CoreServiceResponseStatus.Failed.value,
                message="An error occurred while processing the get balance.",
                data={},
            )

        return CoreServiceResponse(
            success=response.status == VasGateStatus.Success.value,
            status=response.status,
            message=response.message,
            data=response.data,
        )

from common.enums import CustomEnum


class SocialMediaChannel(CustomEnum):
    Facebook = "Facebook"
    Twitter = "Twitter"
    Instagram = "Instagram"
    LinkedIn = "LinkedIn"


class DocumentStatus(CustomEnum):
    Pending = "Pending"
    Approved = "Approved"
    Rejected = "Rejected"
    ChangeRequested = "ChangeRequested"


class DocumentName(CustomEnum):
    CertificateOfIncorporation = "Certificate Of Incorporation"
    MemorandumOfAssociation = "Memorandum Of Association"
    ProofOfAddress = "Proof of Address"
    DirectorNin = "Director Nin"

    @classmethod
    def for_upload_documents_screen(cls):
        subset = [
            cls.CertificateOfIncorporation,
            cls.MemorandumOfAssociation,
            cls.ProofOfAddress,
        ]
        return [(member.value, member.value) for member in subset]


class OnboardingStage(CustomEnum):
    BusinessInformation = "BusinessInformation"
    Documentation = "Documentation"
    SocialAccount = "SocialAccount"
    DirectorsAndOwners = "DirectorsAndOwners"
    SettlementDetails = "SettlementDetails"
    Completed = "Completed"


class BusinessStatus(CustomEnum):
    Inactive = "Inactive"
    Verified = "Verified"  # customer-services' action
    Active = "Active"  # operations' action


class BusinessStatusUpdateAction(CustomEnum):
    ACTIVATE = "ACTIVATE"
    REACTIVATE = "REACTIVATE"
    DEACTIVATE = "DEACTIVATE"
    VERIFY = "VERIFY"


class BusinessRiskLevel(CustomEnum):
    LOW = "LOW"
    HIGH = "HIGH"


class BusinessSection(CustomEnum):
    BusinessInformation = "BusinessInformation"
    DirectorsAndOwners = "DirectorsAndOwners"
    SettlementDetails = "SettlementDetails"
    DocumentationProofOfAddress = "Documentation_Proof_Of_Address"
    DocumentationCertificateOfIncorporation = (
        "Documentation_Certificate_Of_Incorporation"
    )
    DocumentationMemorandumOfAssociation = "Documentation_Memorandum_Of_Association"

    @classmethod
    def choices(cls):
        return [(tag.value, tag.name.replace("_", " ").title()) for tag in cls]


class ChangeRequestStatus(CustomEnum):
    Pending = "Pending"
    Approved = "Approved"
    Rejected = "Rejected"


class ChangeRequestType(CustomEnum):
    Creation = "Creation"
    Modification = "Modification"

from business.enums import DocumentName, OnboardingStage, SocialMediaChannel
from business.models import Business, Document, SocialMedia


class OnboardingWorkflowHandler:
    def __init__(self, business: Business):
        self.business = business

    def _update_stage(self, new_stage: OnboardingStage):
        """Update onboarding stage if not already completed."""
        if self.business.onboarding_stage != OnboardingStage.Completed.value:
            self.business.onboarding_stage = new_stage.value
            self.business.save()

    @staticmethod
    def _get_required_documents():
        """Get list of required document names."""
        return [name for name, *_ in DocumentName.for_upload_documents_screen()]

    @staticmethod
    def _get_required_channels():
        """Get list of required social media channels."""
        return [channel for channel, *_ in SocialMediaChannel.choices()]

    def _has_all_required_documents(self):
        """Check if business has all required documents."""
        required_documents = self._get_required_documents()
        return Document.objects.filter(
            business=self.business, document_name__in=required_documents
        ).count() >= len(
            required_documents
        )  # TODO: Filter Documents Created Via Change Requests

    def _has_social_media_presence(self):
        """Check if business has required social media presence."""
        required_channels = self._get_required_channels()
        return SocialMedia.objects.filter(
            business=self.business, channel__in=required_channels
        ).exists()

    def _is_business_information_complete(self):
        """Check if business information is complete."""
        return bool(self.business.rc_number)

    def _is_documentation_complete(self):
        """Check if documentation stage is complete."""
        return self._has_all_required_documents()

    def _are_directors_complete(self):
        """Check if directors information is complete."""
        return self.business.directors.exists()

    def _are_settlement_details_complete(self):
        """Check if settlement details are complete."""
        return self.business.settlement_details.exists()

    def advance_from_business_information(self):
        """Advance from business information to documentation stage."""
        self._update_stage(OnboardingStage.Documentation)

    def advance_from_social_media(self):
        """Advance from social media to directors stage if requirements are met."""
        if self._has_social_media_presence():
            self._update_stage(OnboardingStage.SocialAccount)

    def advance_from_documentation(self):
        """Advance from documentation to directors stage if requirements are met."""
        if self._is_documentation_complete():
            self._update_stage(OnboardingStage.DirectorsAndOwners)

    def advance_from_directors(self):
        """Advance from directors to settlement details stage."""
        self._update_stage(OnboardingStage.SettlementDetails)

    def complete_onboarding(self):
        """Mark onboarding as completed."""
        self._update_stage(OnboardingStage.Completed)

    def onboarding_stages(self):
        """Get the completion status of all onboarding stages."""
        return {
            "completed_business_information": self._is_business_information_complete(),
            "completed_documentation": self._is_documentation_complete(),
            "completed_directors": self._are_directors_complete(),
            "completed_social_media": self._has_social_media_presence(),
            "completed_settlement_details": self._are_settlement_details_complete(),
        }

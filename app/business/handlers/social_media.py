from business.handlers.onboarding_workflow import OnboardingWorkflowHandler
from business.models import Business, SocialMedia
from django.db import transaction


class SocialMediaHandler:
    @staticmethod
    def add(business: Business, data):
        with transaction.atomic():
            SocialMedia.objects.update_or_create(
                business=business,
                channel=data["channel"],
                defaults={"url": data["url"]},
            )
            OnboardingWorkflowHandler(business).advance_from_social_media()

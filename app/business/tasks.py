from business.handlers.api_config_handler import APIConfigHandler
from business.models import Business
from core.celery import APP
from django.db import transaction
from django.forms import model_to_dict
from fees.handlers.fee_creation_handler import <PERSON>e<PERSON>andler
from fees.models import GeneralFee
from transaction.models import Transaction
from user.models import User


@APP.task()
def initialize_business_configurations(business_id):
    business = Business.objects.filter(pk=business_id).first()
    if not business:
        return "Invalid business id"

    set_fees(business)
    set_api_key(business)

    return "Fees and api configs created successfully"


def set_api_key(business):
    APIConfigHandler().save_pub_key(business)

    return "API key set successfully"


def set_fees(business):
    general_fees = GeneralFee.objects.all()
    for gf in general_fees:
        bands = list(gf.general_fee_bands.all().values())
        base_data = model_to_dict(gf)
        payload = {
            **base_data,
            "business": business,
            "business_fee_bands": bands,
        }
        FeeHandler().create_business_fee(payload)

    return "Fees created successfully"


@APP.task()
def assign_relationship_manager(business_ids, relationship_manager_id):
    businesses = list(Business.objects.filter(pk__in=business_ids))
    relationship_manager = User.objects.filter(pk=relationship_manager_id).first()

    if not relationship_manager:
        raise ValueError("Invalid relationship manager ID")

    with transaction.atomic():
        for business in businesses:
            business.relationship_manager = relationship_manager
            business.save()
            Transaction.objects.filter(business=business).update(
                relationship_manager=relationship_manager
            )

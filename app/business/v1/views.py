from audit.models import <PERSON>tLog
from audit.utils import log_api_key_action, log_user_action
from business.enums import BusinessStatus, ChangeRequestStatus, DocumentStatus
from business.handlers.onboarding_workflow import OnboardingWorkflowHandler
from business.models import (
    APIConfig,
    Business,
    BusinessChangeRequest,
    Director,
    Document,
    SettlementDetail,
    SocialMedia,
)
from business.v1.filters import BusinessChangeRequestFilter, BusinessFilter
from business.v1.serializers import (
    AddDirectorSerializer,
    AddSettlementDetailsChangeRequestSerializer,
    AddSettlementDetailsSerializer,
    AddUpdateDirectorChangeRequestSerializer,
    APIConfigSettingsSerializer,
    AssignRelationshipManagerSerializer,
    BusinessChangeRequestDetailSerializer,
    BusinessChangeRequestMinimalSerializer,
    BusinessInformationChangeRequestSerializer,
    BusinessInformationSerializer,
    BusinessListSerializer,
    DirectorListSerializer,
    DocumentMiniSerializer,
    GeneratePrivateKeySerializer,
    MultipleSocialMediaSerializer,
    SettlementDetailsSerializer,
    SocialMediaSerializer,
    UploadDocumentChangeRequestSerializer,
    UploadDocumentSerializer,
    UploadMultipleDocumentChangeRequestSerializer,
    UploadMultipleDocumentsSerializer,
    VerifyAccountNumberSerializer,
)
from common.decorators import merchant_onboarding_required
from common.serializers import EmptySerializer
from django.db.models import Q, Sum
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter, extend_schema
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from teams.enums import DefaultAdminRole
from user.models import User
from user.v1.serializers import (
    CreatePINSerializer,
    ResetPINSerializer,
    UserSerializer,
    VerifyBusiness2FASerializer,
    VerifyPINSerializer,
)
from wallet.enums import WalletEnums


class OnboardBusinessViewSet(viewsets.GenericViewSet):
    """
    ┌─────────────────────────────┐
    │  Business Onboarding Stages │
    └─────────────────────────────┘

    1️⃣  Business Information
        - Collect basic business details (name, email, phone, etc.)

    2️⃣  Documentation
        - Upload and verify required documents

    3️⃣  Directors Details
        - Capture personal and identification information of key individuals

    4️⃣  Settlement Details
        - Provide bank or payment account information for settlements

    📌 Each stage must be completed in order to successfully onboard a business.
    """

    permission_classes = [IsAuthenticated]
    queryset = User.objects.all()

    @action(
        methods=["POST"],
        detail=False,
        url_path="create-pin",
        serializer_class=CreatePINSerializer,
    )
    def create_pin(self, request):
        user = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save(user)

        # Log PIN creation
        log_user_action(
            request=request,
            user=user,
            action=AuditLog.PIN_CHANGE,
            description="Transaction PIN created",
            resource_type="User",
            resource_id=str(user.id),
        )

        data = {"success": True, "message": "PIN set successfully."}
        return Response(data, status=status.HTTP_200_OK)

    @action(
        methods=["POST"],
        detail=False,
        url_path="verify-pin",
        serializer_class=VerifyPINSerializer,
    )
    def verify_pin(self, request):
        user = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        data = serializer.create(serializer.validated_data)
        token = data["key"]
        pin_action = data["action"]
        data = {
            "success": True,
            "message": f"PIN validated for '{pin_action}'.",
            "token": token,
        }
        return Response(data, status=status.HTTP_200_OK)

    @action(
        methods=["POST"],
        detail=False,
        url_path="verify-action-2fa",
        serializer_class=VerifyBusiness2FASerializer,
    )
    def verify_reset_2fa(self, request):
        user: User = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        token = serializer.create(serializer.validated_data)
        data = {
            "success": True,
            "message": "2FA Code verified. Use this token to complete your action.",
            "token": token,
        }
        return Response(data, status=status.HTTP_200_OK)

    @action(
        methods=["POST"],
        detail=False,
        url_path="reset-pin",
        serializer_class=ResetPINSerializer,
    )
    def reset_pin(self, request):
        user = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(
            {"success": True, "message": "PIN reset successful."},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="submit-information",
        serializer_class=BusinessInformationSerializer,
    )
    def submit_business_information(self, request):
        business: Business = request.user.business
        serializer = self.serializer_class(business, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Business information submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-information",
        serializer_class=BusinessInformationSerializer,
    )
    def business_information(self, request):
        business: Business = request.user.business
        serializer = self.serializer_class(business)
        return Response(
            {"success": True, "data": serializer.data},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="submit-information-change-request",
        serializer_class=BusinessInformationChangeRequestSerializer,
    )
    @merchant_onboarding_required
    def submit_information_request(self, request):
        business: Business = request.user.business
        serializer = self.get_serializer(instance=business, data=request.data)
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Business information change request submitted.",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="upload-documents",
        serializer_class=UploadDocumentSerializer,
    )
    def upload_documents(self, request):
        user: User = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Document uploaded successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="upload-multiple-documents",
        serializer_class=UploadMultipleDocumentsSerializer,
    )
    def upload_batch_documents(self, request):
        user: User = request.user
        serializer = self.get_serializer(
            data={}, context={"request": request, "user": user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Documents uploaded successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="upload-documents-change-request",
        serializer_class=UploadDocumentChangeRequestSerializer,
    )
    def submit_document_change_request(self, request):
        user: User = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Document change request submitted successfully",
                "data": data,
            },
            status=status.HTTP_201_CREATED,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="upload-multiple-documents-change-request",
        serializer_class=UploadMultipleDocumentChangeRequestSerializer,
    )
    def upload_multiple_documents_change_request(self, request):
        user = request.user
        serializer = self.get_serializer(
            data={}, context={"request": request, "user": user}
        )
        serializer.is_valid(raise_exception=True)
        change_requests = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_requests, many=True).data

        return Response(
            {
                "success": True,
                "message": "Document change requests submitted successfully",
                "data": data,
            },
            status=status.HTTP_201_CREATED,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-documents",
        serializer_class=DocumentMiniSerializer,
    )
    def fetch_documents(self, request):

        business: Business = request.user.business
        documents = (
            Document.objects.filter(business=business)
            .exclude(status=DocumentStatus.ChangeRequested.value)
            .exclude(status=DocumentStatus.Rejected.value)
            .order_by("document_name", "-created_at")
        )

        return Response(
            {
                "success": True,
                "data": DocumentMiniSerializer(documents, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-director",
        serializer_class=AddDirectorSerializer,
    )
    def add_director(self, request):
        user: User = request.user
        serializer = AddDirectorSerializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Director details submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-or-update-director-change-request",
        serializer_class=AddUpdateDirectorChangeRequestSerializer,
    )
    @merchant_onboarding_required
    def submit_director_change_request(self, request):
        business: Business = request.user.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Business Settlement Details change request submitted.",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-directors",
        serializer_class=DirectorListSerializer,
    )
    def fetch_directors(self, request):
        """Fetch all directors for the current business"""
        business: Business = request.user.business
        directors = Director.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": DirectorListSerializer(directors, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="delete-director/(?P<director_id>[^/.]+)",
        serializer_class=EmptySerializer,
    )
    def delete_director(self, request, director_id=None):
        business: Business = request.user.business

        try:
            director = Director.objects.get(id=director_id, business=business)
            director.delete()

            return Response(
                {"success": True, "message": "Director deleted successfully"},
                status=status.HTTP_200_OK,
            )
        except Director.DoesNotExist:
            return Response(
                {"success": False, "message": "Director not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-settlement-details",
        serializer_class=AddSettlementDetailsSerializer,
    )
    def add_settlement_details(self, request):
        user: User = request.user
        serializer = AddSettlementDetailsSerializer(
            data=request.data, context={"user": user}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Settlement details submitted successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-settlement-details-change-request",
        serializer_class=AddSettlementDetailsChangeRequestSerializer,
    )
    @merchant_onboarding_required
    def submit_settlement_details_change_request(self, request):
        business: Business = request.user.business
        serializer = self.get_serializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        change_request = serializer.save()
        data = BusinessChangeRequestMinimalSerializer(change_request).data

        return Response(
            {
                "success": True,
                "message": "Business Settlement Details change request submitted.",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="fetch-settlement-details",
        serializer_class=SettlementDetailsSerializer,
    )
    def fetch_settlement_details(self, request):
        business: User = request.user.business

        settlement_details = SettlementDetail.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": SettlementDetailsSerializer(settlement_details, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="verify-account-number",
        serializer_class=VerifyAccountNumberSerializer,
    )
    def verify_account_number(self, request):
        serializer = VerifyAccountNumberSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        result = serializer.save()

        return Response(
            {"success": True, "data": result},
            status=status.HTTP_200_OK,
        )

    @extend_schema(
        summary="Get Bank List",
        description="Retrieve a list of all available banks with their names and bank codes. \
            This endpoint returns cached data for better performance.",
        tags=["business-onboarding"],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "success": {"type": "boolean", "example": True},
                    "data": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "bank_name": {
                                    "type": "string",
                                    "example": "Access Bank",
                                },
                                "bank_code": {"type": "string", "example": "044"},
                            },
                        },
                    },
                },
            }
        },
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="bank-list",
        serializer_class=EmptySerializer,
    )
    def get_bank_list(self, request):
        from datetime import timedelta

        from config.models import Bank
        from django.core.cache import cache

        cache_key = "business::bank::list"
        cache_timeout = timedelta(hours=24)

        cached_banks = cache.get(cache_key)
        if cached_banks:
            banks = cached_banks
        else:
            banks = list(
                Bank.objects.values("name", "institution_code").order_by("name")
            )
            banks = [
                {"bank_name": bank["name"], "institution_code": bank["institution_code"]}
                for bank in banks
            ]
            cache.set(cache_key, banks, timeout=cache_timeout.total_seconds())

        return Response(
            {
                "success": True,
                "data": banks,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="onboarding-stages",
        serializer_class=EmptySerializer,
    )
    def onboarding_stages(self, request):
        business: Business = request.user.business
        return Response(
            OnboardingWorkflowHandler(business).onboarding_stages(),
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="add-social-media",
        serializer_class=MultipleSocialMediaSerializer,
    )
    def add_social_media(self, request):
        """Add or update a social media channel for the business"""
        user: User = request.user
        serializer = self.get_serializer(data=request.data, context={"user": user})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {"success": True, "message": "Social media channel added successfully"},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["GET"],
        detail=False,
        url_path="social-media",
        serializer_class=SocialMediaSerializer,
    )
    def list_social_media(self, request):
        """List all social media channels for the business"""
        business: Business = request.user.business
        social_media = SocialMedia.objects.filter(business=business)

        return Response(
            {
                "success": True,
                "data": SocialMediaSerializer(social_media, many=True).data,
            },
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["DELETE"],
        detail=False,
        url_path="social-media/(?P<social_media_id>[^/.]+)",
        serializer_class=EmptySerializer,
    )
    # TODO: Maybe we take this out??
    def delete_social_media(self, request, social_media_id=None):
        """Delete a social media channel"""
        business: Business = request.user.business

        try:
            social_media = SocialMedia.objects.get(
                id=social_media_id, business=business
            )
            social_media.delete()

            return Response(
                {
                    "success": True,
                    "message": "Social media channel deleted successfully",
                },
                status=status.HTTP_200_OK,
            )
        except SocialMedia.DoesNotExist:
            return Response(
                {
                    "success": False,
                    "message": "Social media channel not found",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(
        methods=["GET"],
        detail=False,
        url_path="relationship-manager",
        serializer_class=UserSerializer,
    )
    def retrieve_relationship_manager(self, request):
        business: Business = request.user.business
        relationship_manager = business.relationship_manager
        if not relationship_manager:
            return Response(
                {
                    "success": False,
                    "message": "Relationship manager not yet assigned",
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        data = self.serializer_class(relationship_manager).data
        return Response(
            {
                "success": True,
                "data": data,
            },
            status=status.HTTP_200_OK,
        )


class APIConfigViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    queryset = APIConfig.objects.all()

    @action(
        methods=["GET"],
        detail=False,
        url_path="get-settings",
        serializer_class=EmptySerializer,
    )
    def get_config_settings(self, request):
        business: Business = request.user.business
        api_config = (
            APIConfig.objects.values(
                "public_key", "webhook_url", "webhook_signature", "whitelisted_ips"
            )
            .filter(business=business)
            .first()
        )

        return Response({"success": True, "data": api_config})

    @action(
        methods=["POST"],
        detail=False,
        url_path="generate-private-key",
        serializer_class=GeneratePrivateKeySerializer,
    )
    @merchant_onboarding_required
    def generate_private_key(self, request):
        user: User = request.user
        serializer = GeneratePrivateKeySerializer(
            data=request.data, context={"user": user}
        )
        serializer.is_valid(raise_exception=True)
        private_key = serializer.save()

        # Log API key generation
        log_api_key_action(
            request=request,
            user=user,
            action_type="GENERATE",
            api_key_id=f"private_key_{user.business.id}",
        )

        return Response(
            {
                "success": True,
                "data": {"private_key": private_key},
            }
        )

    @action(
        methods=["POST"],
        detail=False,
        url_path="settings",
        serializer_class=APIConfigSettingsSerializer,
    )
    @merchant_onboarding_required
    def config_settings(self, request):
        business: Business = request.user.business
        serializer = APIConfigSettingsSerializer(
            data=request.data, context={"business": business}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {
                "success": True,
                "message": "Settings updated successfully.",
            }
        )


class BusinessChangeRequestViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    queryset = BusinessChangeRequest.objects.all()
    serializer_class = BusinessChangeRequestMinimalSerializer
    filter_backends = [
        DjangoFilterBackend,
    ]
    filterset_class = BusinessChangeRequestFilter
    pagination_class = None

    def get_serializer_class(self):
        if self.action == "retrieve":
            return BusinessChangeRequestDetailSerializer
        return super().get_serializer_class()

    @merchant_onboarding_required
    def list(self, request):
        business: Business = request.user.business
        queryset = self.filter_queryset(self.get_queryset())
        queryset = queryset.filter(business=business).order_by("-created_at")
        data = self.serializer_class(queryset, many=True).data

        return Response(
            {
                "success": True,
                "data": data,
            }
        )

    @merchant_onboarding_required
    def retrieve(self, request, pk=None):
        business: Business = request.user.business
        try:
            change_request = self.get_queryset().get(pk=pk, business=business)
        except BusinessChangeRequest.DoesNotExist:
            return Response(
                {"success": False, "message": "Change request not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        data = (self.get_serializer(change_request).data,)
        return Response(
            {
                "success": True,
                "data": data,
            }
        )

    @merchant_onboarding_required
    def destroy(self, request, pk=None):
        business = request.user.business
        change_request = get_object_or_404(BusinessChangeRequest, pk=pk)

        if change_request.business != business:
            return Response(
                {
                    "success": False,
                    "message": "You do not have permission to delete this request.",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        if change_request.status != ChangeRequestStatus.Pending.value:
            return Response(
                {"success": False, "message": "Only pending requests can be deleted."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # delete associated document if existing
        document: Document = Document.objects.filter(
            pk=change_request.object_id,
            business=business,
            status=DocumentStatus.ChangeRequested.value,
        )
        if document:
            document.delete()
        change_request.delete()

        return Response(
            {"success": True, "message": "Change request deleted successfully."},
            status=status.HTTP_200_OK,
        )


class RelationshipManagerViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    http_method_names = ["get", "post"]
    queryset = User.objects.filter(
        role=DefaultAdminRole.RelationshipManager.value
    ).all()
    serializer_class = UserSerializer
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
    ]
    search_fields = ["email", "firstname", "lastname", "phone"]

    @action(
        methods=["POST"],
        detail=False,
        url_path="assign",
        serializer_class=AssignRelationshipManagerSerializer,
    )
    def assign_relationship_manager(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(
            {
                "success": True,
                "message": "Processing of businesses assignments to a relationship manager has started.",
            }
        )

    def retrieve(self, request, *args, **kwargs):
        relationship_manager = get_object_or_404(User, pk=kwargs["pk"])
        queryset = self.get_queryset()

        data = {
            "result": UserSerializer(queryset.first()).data,
            "no_assigned_merchants": Business.objects.filter(
                relationship_manager=relationship_manager
            ).count(),
        }

        return Response(data, status=status.HTTP_200_OK)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="name",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description="Filter by business name",
            ),
            OpenApiParameter(
                name="phone",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description="Filter by business phone",
            ),
            OpenApiParameter(
                name="rc_number",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description="Filter by business rc number",
            ),
            OpenApiParameter(
                name="email",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description="Filter by business email",
            ),
            OpenApiParameter(
                name="status",
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                required=False,
                description="Filter by business status",
                enum=BusinessStatus.values(),
            ),
        ],
    )
    @action(
        methods=["GET"],
        detail=False,
        url_path="assigned-businesses/(?P<relationship_manager_id>[^/.]+)",
        serializer_class=EmptySerializer,
    )
    def assigned_businesses(self, request, relationship_manager_id=None):
        qs = Business.objects.annotate(
            general_wallet_balance=Sum(
                "wallets__balance", filter=Q(wallets__type=WalletEnums.GENERAL)
            )
        ).filter(relationship_manager=relationship_manager_id)

        filterset = BusinessFilter(request.GET, queryset=qs)
        qs = filterset.qs

        page = self.paginate_queryset(qs)
        if page is not None:
            serializer = BusinessListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = BusinessListSerializer(qs, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)

from common.enums import ProductEnum, ServiceEnum
from fees.enums import BandedFeeType, FeeType
from fees.handlers.fee_creation_handler import FeeHandler
from fees.models import (
    BusinessFee,
    BusinessFeeBand,
    GeneralFee,
    GeneralFeeBand,
    ProviderFee,
    ProviderFeeBand,
)
from fees.tasks import apply_fees_to_all_businesses
from fees.validation_mixins import FeeValidationMixin
from rest_framework import serializers


class ProviderFeeBandSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProviderFeeBand
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at", "provider_fee")


class BusinessFeeBandSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessFeeBand
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at", "business_fee")


class GeneralFeeBandSerializer(serializers.ModelSerializer):
    class Meta:
        model = GeneralFeeBand
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at", "general_fee")


class CreateProviderFeeSerializer(FeeValidationMixin, serializers.ModelSerializer):
    provider_fee_bands = ProviderFeeBandSerializer(many=True, required=False)

    class Meta:
        model = ProviderFee
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at")

    def create(self, validated_data):

        FeeHandler().create_provider_fee(validated_data)

        return validated_data

    @staticmethod
    def _band_category():
        return "provider_fee_bands"


class CreateBulkProviderFeeSerializer(serializers.Serializer):
    provider_fees = CreateProviderFeeSerializer(many=True)

    def create(self, validated_data):
        provider_fees_data = validated_data["provider_fees"]
        created_fees = []
        for provider_fee_data in provider_fees_data:
            serializer = CreateProviderFeeSerializer()
            created_fee = serializer.create(provider_fee_data)
            created_fees.append(created_fee)

        return created_fees


class CreateBusinessFeeSerializer(FeeValidationMixin, serializers.ModelSerializer):
    business_fee_bands = BusinessFeeBandSerializer(many=True, required=False)

    class Meta:
        model = BusinessFee
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at")

    def create(self, validated_data):
        FeeHandler().create_business_fee(validated_data)

        return validated_data

    @staticmethod
    def _band_category():
        return "business_fee_bands"


class CreateBulkBusinessFeeSerializer(serializers.Serializer):
    business_fees = CreateBusinessFeeSerializer(many=True)

    def create(self, validated_data):
        business_fees_data = validated_data["business_fees"]
        created_fees = []
        for business_fee_data in business_fees_data:
            serializer = CreateBusinessFeeSerializer()
            created_fee = serializer.create(business_fee_data)
            created_fees.append(created_fee)

        return created_fees


class CreateGeneralFeeSerializer(FeeValidationMixin, serializers.ModelSerializer):

    general_fee_bands = GeneralFeeBandSerializer(many=True, required=False)

    class Meta:
        model = GeneralFee
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at")

    def create(self, validated_data):
        FeeHandler().create_general_fee(validated_data)

        return validated_data

    @staticmethod
    def _band_category():
        return "general_fee_bands"


class CreateBulkGeneralFeeSerializer(serializers.Serializer):
    general_fees = CreateGeneralFeeSerializer(many=True)

    def create(self, validated_data):
        general_fees_data = validated_data["general_fees"]
        created_fees = []
        for general_fee_data in general_fees_data:
            serializer = CreateGeneralFeeSerializer()
            created_fee = serializer.create(general_fee_data)
            created_fees.append(created_fee)

        return created_fees


class UpdateBusinessFeeSerializer(FeeValidationMixin, serializers.ModelSerializer):
    business_fee_bands = BusinessFeeBandSerializer(many=True, required=False)

    class Meta:
        model = BusinessFee
        fields = ["fee_type", "amount", "cap_amount", "business_fee_bands"]

    def update(self, instance, validated_data):
        # Update the main fee fields
        instance.fee_type = validated_data.get("fee_type", instance.fee_type)
        instance.amount = validated_data.get("amount", instance.amount)
        instance.cap_amount = validated_data.get("cap_amount", instance.cap_amount)
        instance.save()

        # Handle banded fee updates
        if validated_data.get("fee_type") == FeeType.Banded.value:
            business_fee_bands = validated_data.get("business_fee_bands", [])
            if business_fee_bands:
                for band in business_fee_bands:
                    BusinessFeeBand.objects.update_or_create(
                        business_fee=instance,
                        lower_bound=band["lower_bound"],
                        defaults={
                            "fee_type": band.get("fee_type", BandedFeeType.Fixed.value),
                            "amount": band.get("amount", 0),
                            "upper_bound": band.get("upper_bound", 0),
                            "upper_bound_infinite": band.get(
                                "upper_bound_infinite", False
                            ),
                        },
                    )

        return instance

    @staticmethod
    def _band_category():
        return "business_fee_bands"


class UpdateProviderFeeSerializer(FeeValidationMixin, serializers.ModelSerializer):
    provider_fee_bands = ProviderFeeBandSerializer(many=True, required=False)

    class Meta:
        model = ProviderFee
        fields = ["fee_type", "amount", "cap_amount", "provider_fee_bands"]

    def update(self, instance, validated_data):
        # Update the main fee fields
        instance.fee_type = validated_data.get("fee_type", instance.fee_type)
        instance.amount = validated_data.get("amount", instance.amount)
        instance.cap_amount = validated_data.get("cap_amount", instance.cap_amount)
        instance.save()

        # Handle banded fee updates
        if validated_data.get("fee_type") == FeeType.Banded.value:
            provider_fee_bands = validated_data.get("provider_fee_bands", [])
            if provider_fee_bands:
                for band in provider_fee_bands:
                    ProviderFeeBand.objects.update_or_create(
                        provider_fee=instance,
                        lower_bound=band["lower_bound"],
                        defaults={
                            "fee_type": band.get("fee_type", BandedFeeType.Fixed.value),
                            "amount": band.get("amount", 0),
                            "upper_bound": band.get("upper_bound", 0),
                            "upper_bound_infinite": band.get(
                                "upper_bound_infinite", False
                            ),
                        },
                    )

        return instance

    @staticmethod
    def _band_category():
        return "provider_fee_bands"


class UpdateGeneralFeeSerializer(FeeValidationMixin, serializers.ModelSerializer):
    general_fee_bands = GeneralFeeBandSerializer(many=True, required=False)

    class Meta:
        model = GeneralFee
        fields = ["fee_type", "amount", "cap_amount", "general_fee_bands"]

    def update(self, instance, validated_data):
        # Update the main fee fields
        instance.fee_type = validated_data.get("fee_type", instance.fee_type)
        instance.amount = validated_data.get("amount", instance.amount)
        instance.cap_amount = validated_data.get("cap_amount", instance.cap_amount)
        instance.save()

        # Handle banded fee updates
        if validated_data.get("fee_type") == FeeType.Banded.value:
            general_fee_bands = validated_data.get("general_fee_bands", [])
            if general_fee_bands:
                for band in general_fee_bands:
                    GeneralFeeBand.objects.update_or_create(
                        general_fee=instance,
                        lower_bound=band["lower_bound"],
                        defaults={
                            "fee_type": band.get("fee_type", BandedFeeType.Fixed.value),
                            "amount": band.get("amount", 0),
                            "upper_bound": band.get("upper_bound", 0),
                            "upper_bound_infinite": band.get(
                                "upper_bound_infinite", False
                            ),
                        },
                    )

        return instance

    @staticmethod
    def _band_category():
        return "general_fee_bands"


class BusinessFeeSerializer(serializers.ModelSerializer):
    business_fee_bands = BusinessFeeBandSerializer(many=True)

    class Meta:
        model = BusinessFee
        fields = "__all__"


class ProviderFeeSerializer(serializers.ModelSerializer):
    provider_fee_bands = ProviderFeeBandSerializer(many=True)

    class Meta:
        model = ProviderFee
        fields = "__all__"


class GeneralFeeSerializer(serializers.ModelSerializer):
    general_fee_bands = GeneralFeeBandSerializer(many=True)

    class Meta:
        model = GeneralFee
        fields = "__all__"


class ApplyFeesToAllMerchantsSerializer(serializers.Serializer):
    product = serializers.ChoiceField(choices=ProductEnum.choices())
    service = serializers.ChoiceField(choices=ServiceEnum.choices(), required=False)

    def create(self, validated_data):
        product = validated_data.get("product")
        service = validated_data.get("service", None)

        filters = {"product": product}
        if service:
            filters["service"] = service

        general_fees = GeneralFee.objects.filter(**filters).first()
        if not general_fees:
            raise serializers.ValidationError(
                {"product": "No general fees exist for the variables set"}
            )

        apply_fees_to_all_businesses.delay(product, service)
        return validated_data

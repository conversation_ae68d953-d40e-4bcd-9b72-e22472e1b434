
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("fees", "0002_providerfee_remove_venderfeeband_vender_fee_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="businessfee",
            name="service",
            field=models.CharField(
                choices=[
                    ("Mtn", "Mtn"),
                    ("Glo", "Glo"),
                    ("Airtel", "Airtel"),
                    ("9Mobile", "9Mobile"),
                    ("Dstv", "Dstv"),
                    ("Gotv", "Gotv"),
                    ("Startimes", "Startimes"),
                    ("AbujaElectric", "AbujaElectric"),
                    ("BeninElectric", "BeninElectric"),
                    ("EnuguElectric", "EnuguElectric"),
                    ("EkoElectric", "EkoElectric"),
                    ("IbadanElectric", "IbadanElectric"),
                    ("IkejaElectric", "IkejaElectric"),
                    ("JosElectric", "JosElectric"),
                    ("PortharcourtElectric", "PortharcourtElectric"),
                    ("KadunaElectric", "KadunaElectric"),
                    ("KanoElectric", "KanoElectric"),
                    ("YolaElectric", "YolaElectric"),
                    ("Bet9ja", "Bet9ja"),
                    ("BangBet", "BangBet"),
                    ("SupaBet", "SupaBet"),
                    ("CloudBet", "CloudBet"),
                    ("BetLion", "BetLion"),
                    ("1xBet", "1xBet"),
                    ("MerryBet", "MerryBet"),
                    ("BetWay", "BetWay"),
                    ("BetLand", "BetLand"),
                    ("BetKing", "BetKing"),
                    ("LiveScoreBet", "LiveScoreBet"),
                    ("NaijaBet", "NaijaBet"),
                    ("Nin", "Nin"),
                    ("Bvn", "Bvn"),
                    ("PhoneNumberLookup", "PhoneNumberLookup"),
                    ("Waec", "Waec"),
                    ("Jamb", "Jamb"),
                    ("Transfer", "Transfer"),
                    ("VirtualAccount", "VirtualAccount"),
                    ("RecurringDebit", "RecurringDebit"),
                ],
                db_index=True,
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="generalfee",
            name="service",
            field=models.CharField(
                choices=[
                    ("Mtn", "Mtn"),
                    ("Glo", "Glo"),
                    ("Airtel", "Airtel"),
                    ("9Mobile", "9Mobile"),
                    ("Dstv", "Dstv"),
                    ("Gotv", "Gotv"),
                    ("Startimes", "Startimes"),
                    ("AbujaElectric", "AbujaElectric"),
                    ("BeninElectric", "BeninElectric"),
                    ("EnuguElectric", "EnuguElectric"),
                    ("EkoElectric", "EkoElectric"),
                    ("IbadanElectric", "IbadanElectric"),
                    ("IkejaElectric", "IkejaElectric"),
                    ("JosElectric", "JosElectric"),
                    ("PortharcourtElectric", "PortharcourtElectric"),
                    ("KadunaElectric", "KadunaElectric"),
                    ("KanoElectric", "KanoElectric"),
                    ("YolaElectric", "YolaElectric"),
                    ("Bet9ja", "Bet9ja"),
                    ("BangBet", "BangBet"),
                    ("SupaBet", "SupaBet"),
                    ("CloudBet", "CloudBet"),
                    ("BetLion", "BetLion"),
                    ("1xBet", "1xBet"),
                    ("MerryBet", "MerryBet"),
                    ("BetWay", "BetWay"),
                    ("BetLand", "BetLand"),
                    ("BetKing", "BetKing"),
                    ("LiveScoreBet", "LiveScoreBet"),
                    ("NaijaBet", "NaijaBet"),
                    ("Nin", "Nin"),
                    ("Bvn", "Bvn"),
                    ("PhoneNumberLookup", "PhoneNumberLookup"),
                    ("Waec", "Waec"),
                    ("Jamb", "Jamb"),
                    ("Transfer", "Transfer"),
                    ("VirtualAccount", "VirtualAccount"),
                    ("RecurringDebit", "RecurringDebit"),
                ],
                db_index=True,
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="providerfee",
            name="service",
            field=models.CharField(
                choices=[
                    ("Mtn", "Mtn"),
                    ("Glo", "Glo"),
                    ("Airtel", "Airtel"),
                    ("9Mobile", "9Mobile"),
                    ("Dstv", "Dstv"),
                    ("Gotv", "Gotv"),
                    ("Startimes", "Startimes"),
                    ("AbujaElectric", "AbujaElectric"),
                    ("BeninElectric", "BeninElectric"),
                    ("EnuguElectric", "EnuguElectric"),
                    ("EkoElectric", "EkoElectric"),
                    ("IbadanElectric", "IbadanElectric"),
                    ("IkejaElectric", "IkejaElectric"),
                    ("JosElectric", "JosElectric"),
                    ("PortharcourtElectric", "PortharcourtElectric"),
                    ("KadunaElectric", "KadunaElectric"),
                    ("KanoElectric", "KanoElectric"),
                    ("YolaElectric", "YolaElectric"),
                    ("Bet9ja", "Bet9ja"),
                    ("BangBet", "BangBet"),
                    ("SupaBet", "SupaBet"),
                    ("CloudBet", "CloudBet"),
                    ("BetLion", "BetLion"),
                    ("1xBet", "1xBet"),
                    ("MerryBet", "MerryBet"),
                    ("BetWay", "BetWay"),
                    ("BetLand", "BetLand"),
                    ("BetKing", "BetKing"),
                    ("LiveScoreBet", "LiveScoreBet"),
                    ("NaijaBet", "NaijaBet"),
                    ("Nin", "Nin"),
                    ("Bvn", "Bvn"),
                    ("PhoneNumberLookup", "PhoneNumberLookup"),
                    ("Waec", "Waec"),
                    ("Jamb", "Jamb"),
                    ("Transfer", "Transfer"),
                    ("VirtualAccount", "VirtualAccount"),
                    ("RecurringDebit", "RecurringDebit"),
                ],
                db_index=True,
                max_length=50,
            ),
        ),
    ]

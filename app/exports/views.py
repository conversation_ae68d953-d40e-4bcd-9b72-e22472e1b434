"""
Simplified export views that leverage existing GET endpoints and filters
"""

import logging

from audit.filters import <PERSON>tLogFilter
from dispute.filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema
from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from transaction.v1.filters import CommissionFilter, TransactionFilter

from .export_services import get_export_service
from .models import ExportRequest
from .serializers import (
    AuditLogsExportSerializer,
    CommissionsExportSerializer,
    DisputesExportSerializer,
    ExportRequestSerializer,
    TransactionsExportSerializer,
)
from .tasks import process_export_task
from .utils import log_export_activity

logger = logging.getLogger(__name__)


class BaseExportView(APIView):
    """Base view for simplified export endpoints that reuse existing GET endpoint logic"""

    permission_classes = [permissions.IsAuthenticated]
    export_type = None
    filter_class = None
    serializer_class = None

    def _create_export_request(self, user, validated_data):
        """Create export request using the specific export service"""
        export_service = get_export_service(self.export_type)

        # For merchant exports, remove business_id filter (they can only see their own data)
        filters = validated_data.copy()
        if "business_id" in filters:
            del filters["business_id"]

        return export_service.create_export_request(
            user=user,
            business=user.business,
            fields_to_export=[],  # Use default fields
            filters=filters,
        )

    def _get_serializer_class(self):
        """Get the appropriate serializer class for this export type"""
        return self.serializer_class

    def post(self, request, *args, **kwargs):
        """Create export request using existing filter logic"""
        serializer_class = self._get_serializer_class()
        serializer = serializer_class(data=request.data)

        if not serializer.is_valid():
            logger.warning(
                f"Invalid data for {self.export_type} export: {serializer.errors}"
            )
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Create export request
            export_request = self._create_export_request(
                request.user, serializer.validated_data
            )

            # Log the export activity
            log_export_activity(
                user=request.user,
                action=self.export_type,
                export_request_id=export_request.id,
                details=f"Export request created with filters: {serializer.validated_data}",
            )

            # Start the background task
            process_export_task.delay(export_request.id)

            # Return the export request details
            response_serializer = ExportRequestSerializer(export_request)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except ValueError as e:
            logger.warning(
                f"Validation error creating {self.export_type} export: {str(e)}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating {self.export_type} export: {str(e)}")
            return Response(
                {"error": "Failed to create export request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class TransactionsExportView(BaseExportView):
    """Export transactions using existing transaction filters"""

    export_type = ExportRequest.ExportType.ALL_TRANSACTIONS
    filter_class = TransactionFilter
    serializer_class = TransactionsExportSerializer

    @extend_schema(
        summary="Export Transactions",
        description="Export transactions with the same filters as the transactions GET endpoint. \
            Supports filtering by status, type, txn_class, and date ranges. \
                Note: business_id filter is not available for merchants (they can only export their own data).",
        request=TransactionsExportSerializer,
        responses={
            201: ExportRequestSerializer,
            400: {"description": "Bad Request - Invalid filters or validation error"},
            500: {
                "description": "Internal Server Error - Failed to create export request"
            },
        },
        tags=["Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class CommissionsExportView(BaseExportView):
    """Export commissions using existing commission filters"""

    export_type = ExportRequest.ExportType.COMMISSIONS
    filter_class = CommissionFilter
    serializer_class = CommissionsExportSerializer

    @extend_schema(
        summary="Export Commissions",
        description="Export commissions with the same filters as the commissions GET endpoint. \
            Supports filtering by txn_class, reference fields, and date ranges. \
                Note: business_id filter is not available for merchants (they can only export their own data).",
        request=CommissionsExportSerializer,
        responses={
            201: ExportRequestSerializer,
            400: {"description": "Bad Request - Invalid filters or validation error"},
            500: {
                "description": "Internal Server Error - Failed to create export request"
            },
        },
        tags=["Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class DisputesExportView(BaseExportView):
    """Export disputes using existing dispute filters"""

    export_type = ExportRequest.ExportType.DISPUTES
    filter_class = DisputeFilter
    serializer_class = DisputesExportSerializer

    @extend_schema(
        summary="Export Disputes",
        description="Export disputes with the same filters as the disputes GET endpoint. \
            Supports filtering by status, dispute_type, vas_service, transaction_reference, \
                merchant_name, amount ranges, date ranges, and search fields.",
        request=DisputesExportSerializer,
        responses={
            201: ExportRequestSerializer,
            400: {"description": "Bad Request - Invalid filters or validation error"},
            500: {
                "description": "Internal Server Error - Failed to create export request"
            },
        },
        tags=["Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class AuditLogsExportView(BaseExportView):
    """Export audit logs using existing audit log filters"""

    export_type = ExportRequest.ExportType.AUDIT_LOGS
    filter_class = AuditLogFilter
    serializer_class = AuditLogsExportSerializer

    @extend_schema(
        summary="Export Audit Logs",
        description="Export audit logs with the same filters as the audit logs GET endpoint. \
            Supports filtering by action, status, email, ip_address, resource_type, resource_id, \
                date ranges, and search fields. \
                Only available to business owners.",
        request=AuditLogsExportSerializer,
        responses={
            201: ExportRequestSerializer,
            400: {"description": "Bad Request - Invalid filters or validation error"},
            500: {
                "description": "Internal Server Error - Failed to create export request"
            },
        },
        tags=["Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

import logging
from datetime import datetime
from typing import Any, Dict, List

from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f} {size_names[i]}"


def generate_export_filename(
    export_type: str, business_name: str, timestamp: datetime = None
) -> str:
    """
    Generate a standardized filename for exports
    """
    if timestamp is None:
        timestamp = timezone.now()

    # Clean business name for filename
    clean_business_name = "".join(
        c for c in business_name if c.isalnum() or c in (" ", "-", "_")
    ).rstrip()
    clean_business_name = clean_business_name.replace(" ", "_")

    # Clean export type
    clean_export_type = export_type.replace(" ", "_").lower()

    # Format timestamp
    timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")

    return f"{clean_export_type}_{clean_business_name}_{timestamp_str}.csv"


def validate_date_range(
    start: str = None, end: str = None, date_from: str = None, date_to: str = None
) -> Dict[str, Any]:
    """
    Validate and parse date range filters
    Support both new (start/end) and legacy (date_from/date_to) field names
    """
    result = {"valid": True, "errors": []}

    # Use new field names if provided, otherwise fall back to legacy names
    start_date = start or date_from
    end_date = end or date_to

    if start_date:
        try:
            parsed_from = datetime.fromisoformat(start_date.replace("Z", "+00:00"))
            result["start"] = parsed_from
            result["date_from"] = parsed_from  # For backward compatibility
        except ValueError:
            result["valid"] = False
            result["errors"].append(
                "Invalid start date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"
            )

    if end_date:
        try:
            parsed_to = datetime.fromisoformat(end_date.replace("Z", "+00:00"))
            result["end"] = parsed_to
            result["date_to"] = parsed_to  # For backward compatibility
        except ValueError:
            result["valid"] = False
            result["errors"].append(
                "Invalid end date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"
            )

    # Validate date range logic
    if result.get("start") and result.get("end"):
        if result["start"] > result["end"]:
            result["valid"] = False
            result["errors"].append("start date cannot be later than end date")

    return result


def get_model_display_name(model_name: str) -> str:
    """
    Get human-readable display name for model
    """
    model_display_names = {
        "transaction.Transaction": "Transactions",
        "audit.AuditLog": "Audit Logs",
        "wallet.Wallet": "Wallets/Commissions",
    }

    return model_display_names.get(model_name, model_name)


def calculate_estimated_processing_time(record_count: int) -> int:
    """
    Estimate processing time in seconds based on record count
    """
    # Base processing rate: ~1000 records per second
    base_rate = 1000

    # Add overhead for file operations
    overhead_seconds = 5

    estimated_seconds = (record_count / base_rate) + overhead_seconds

    # Minimum 10 seconds, maximum 30 minutes
    return max(10, min(1800, int(estimated_seconds)))


def sanitize_field_value(value: Any) -> str:
    """
    Sanitize field values for CSV export
    """
    if value is None:
        return ""

    # Convert to string
    str_value = str(value)

    # Remove or replace problematic characters
    str_value = str_value.replace("\n", " ").replace("\r", " ")
    str_value = str_value.replace("\t", " ")

    # Limit length to prevent extremely large cells
    if len(str_value) > 1000:
        str_value = str_value[:997] + "..."

    return str_value


def get_export_limits() -> Dict[str, int]:
    """
    Get export limits from settings or defaults
    """
    return {
        "max_records_per_export": getattr(settings, "EXPORT_MAX_RECORDS", 100000),
        "max_concurrent_exports": getattr(settings, "EXPORT_MAX_CONCURRENT", 5),
        "max_file_size_mb": getattr(settings, "EXPORT_MAX_FILE_SIZE_MB", 100),
        "default_expiry_days": getattr(settings, "EXPORT_DEFAULT_EXPIRY_DAYS", 7),
    }


def check_export_permissions(user, model_name: str) -> Dict[str, Any]:
    """
    Check if user has permission to export from the specified model
    """
    result = {"allowed": True, "reason": ""}

    # Check if user has business
    if not hasattr(user, "business") or not user.business:
        result["allowed"] = False
        result["reason"] = "User must be associated with a business"
        return result

    # Model-specific permission checks
    if model_name == "audit.AuditLog":
        # Only business owners can export their audit logs
        if user != user.business.owner:
            result["allowed"] = False
            result["reason"] = "Only business owners can export audit logs"

    elif model_name in ["transaction.Transaction", "wallet.Wallet"]:
        # Business members can export transaction and wallet data
        pass  # Already checked business association above

    else:
        result["allowed"] = False
        result["reason"] = f"Export not allowed for model: {model_name}"

    return result


def get_s3_config() -> Dict[str, str]:
    """
    Get S3 configuration from Django settings
    """
    return {
        "bucket_name": getattr(settings, "AWS_STORAGE_BUCKET_NAME", ""),
        "access_key": getattr(settings, "AWS_ACCESS_KEY_ID", ""),
        "secret_key": getattr(settings, "AWS_SECRET_ACCESS_KEY", ""),
        "region": getattr(settings, "AWS_S3_REGION_NAME", ""),
        "endpoint_url": getattr(settings, "AWS_S3_ENDPOINT_URL", None),
    }


def log_export_activity(
    user, action: str, export_request_id: int = None, details: str = None
):
    """
    Log export-related activities for audit purposes
    """
    try:
        from audit.models import AuditLog
        from audit.utils import log_user_action

        # Use the standard DATA_EXPORT action for all export activities
        log_user_action(
            request=None,  # Export happens in background task
            user=user,
            action=AuditLog.DATA_EXPORT,
            description=details or f"Data export: {action}",
            resource_type="ExportRequest",
            resource_id=str(export_request_id) if export_request_id else None,
            metadata={
                "export_request_id": export_request_id,
                "export_action": action,
                "export_type": action.replace("_EXPORT_CREATED", "").replace(
                    "ADMIN_", ""
                ),
            },
        )
    except Exception as e:
        logger.warning(f"Failed to log export activity: {str(e)}")


class ExportValidator:
    """
    Validator class for export requests
    """

    @staticmethod
    def validate_export_request(
        user, model_name: str, fields: List[str], filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Comprehensive validation of export request
        """
        result = {"valid": True, "errors": []}

        # Check permissions
        perm_check = check_export_permissions(user, model_name)
        if not perm_check["allowed"]:
            result["valid"] = False
            result["errors"].append(perm_check["reason"])

        # Validate fields
        if not fields:
            result["valid"] = False
            result["errors"].append("At least one field must be specified")

        # Validate date filters
        if filters:
            date_validation = validate_date_range(
                start=filters.get("start"),
                end=filters.get("end"),
                date_from=filters.get("date_from"),
                date_to=filters.get("date_to"),
            )
            if not date_validation["valid"]:
                result["valid"] = False
                result["errors"].extend(date_validation["errors"])

        return result


def serialize_filters_for_json(filters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert filter values to JSON-serializable format.
    Handles datetime and date objects that can't be directly stored in JSONField.
    """
    if not filters:
        return {}

    serialized_filters = {}
    for key, value in filters.items():
        if value is None:
            serialized_filters[key] = value
        elif isinstance(value, datetime):
            # Convert datetime objects to ISO format strings
            serialized_filters[key] = value.isoformat()
        elif hasattr(
            value, "isoformat"
        ):  # Handle date objects (they have isoformat method)
            # Convert date objects to ISO format strings
            serialized_filters[key] = value.isoformat()
        else:
            # Keep other values as-is
            serialized_filters[key] = value

    return serialized_filters

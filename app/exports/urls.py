"""
Simplified export URLs that leverage existing GET endpoint filters
"""

from django.urls import include, path

from .views import (
    AuditLogsExportView,
    CommissionsExportView,
    DisputesExportView,
    TransactionsExportView,
)

urlpatterns = [
    # ============================================================================
    # SIMPLIFIED EXPORT ENDPOINTS
    # ============================================================================
    # These endpoints use the same filters as existing GET endpoints
    path(
        "exports/transactions/",
        TransactionsExportView.as_view(),
        name="export-transactions",
    ),
    path(
        "exports/commissions/",
        CommissionsExportView.as_view(),
        name="export-commissions",
    ),
    path(
        "exports/disputes/",
        DisputesExportView.as_view(),
        name="export-disputes",
    ),
    path(
        "exports/audit-logs/",
        AuditLogsExportView.as_view(),
        name="export-audit-logs",
    ),
    # Admin export endpoints
    path("", include("exports.admin_urls")),
]

"""
Simplified admin export views that leverage existing GET endpoints and filters
"""

import logging

from audit.filters import AuditLogFilter
from dispute.filters import Dispute<PERSON>ilter
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema
from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from teams.permissions import get_user_admin_context

# Import existing filters to reuse their logic (admin can see all businesses)
from transaction.v1.filters import CommissionFilter, TransactionFilter

from .admin_export_services import get_admin_export_service
from .models import ExportRequest
from .serializers import (
    AdminAuditLogsExportSerializer,
    AdminCommissionsExportSerializer,
    AdminDisputesExportSerializer,
    AdminTransactionsExportSerializer,
    ExportRequestSerializer,
)
from .tasks import process_export_task
from .utils import log_export_activity

User = get_user_model()
logger = logging.getLogger(__name__)


class IsPlatformAdmin(permissions.BasePermission):
    """
    Permission class that ensures user is a platform admin (has AdminProfile).
    This distinguishes platform admins from business admins.
    """

    message = "Only platform administrators are authorized to perform this action."

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        # Check if user has an active AdminProfile (platform admin)
        admin_profile, _ = get_user_admin_context(request.user)
        return admin_profile is not None


class BaseAdminExportView(APIView):
    """Base view for admin export actions that reuse existing GET endpoint logic"""

    permission_classes = [IsPlatformAdmin]
    export_type = None
    filter_class = None
    serializer_class = None

    def _create_admin_export_request(self, user, validated_data):
        """Create admin export request using the specific export service"""
        export_service = get_admin_export_service(self.export_type)

        # For admin exports, all data is filters (no fields_to_export)
        filters = validated_data

        return export_service.create_export_request(
            user=user,
            business=None,  # Admin exports across all businesses
            fields_to_export=[],  # Use default fields
            filters=filters,
        )

    def _get_serializer_class(self):
        """Get the appropriate serializer class for this admin export type"""
        return self.serializer_class

    def post(self, request, *args, **kwargs):
        """Create admin export request using existing filter logic"""
        serializer_class = self._get_serializer_class()
        serializer = serializer_class(data=request.data)

        if not serializer.is_valid():
            logger.warning(
                f"Invalid data for admin {self.export_type} export: {serializer.errors}"
            )
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Create export request
            export_request = self._create_admin_export_request(
                request.user, serializer.validated_data
            )

            # Log the export activity
            log_export_activity(
                user=request.user,
                action=f"ADMIN_{self.export_type}_EXPORT_CREATED",
                export_request_id=export_request.id,
                details=f"Admin export request created with filters: {serializer.validated_data}",
            )

            # Start the background task
            process_export_task.delay(export_request.id)

            # Return the export request details
            response_serializer = ExportRequestSerializer(export_request)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except ValueError as e:
            logger.warning(
                f"Validation error creating admin {self.export_type} export: {str(e)}"
            )
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error creating admin {self.export_type} export: {str(e)}")
            return Response(
                {"error": "Failed to create export request"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AdminTransactionsExportView(BaseAdminExportView):
    """Admin export view for transactions across all merchants"""

    export_type = ExportRequest.ExportType.ALL_TRANSACTIONS
    filter_class = TransactionFilter
    serializer_class = AdminTransactionsExportSerializer

    @extend_schema(
        summary="Admin Export Transactions",
        description="Export transactions across all merchants with the same filters \
            Supports filtering by business_id, status, type, txn_class, and date ranges. Admin access required.",
        request=AdminTransactionsExportSerializer,
        tags=["Admin Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class AdminCommissionsExportView(BaseAdminExportView):
    """Admin export view for commissions across all merchants"""

    export_type = ExportRequest.ExportType.COMMISSIONS
    filter_class = CommissionFilter
    serializer_class = AdminCommissionsExportSerializer

    @extend_schema(
        summary="Admin Export Commissions",
        description="Export commissions across all merchants with the same filters as the commissions GET endpoint.\
              Supports filtering by business_id, txn_class, and date ranges. Admin access required.",
        request=AdminCommissionsExportSerializer,
        responses={
            201: ExportRequestSerializer,
            400: {"description": "Bad Request - Invalid filters or validation error"},
            403: {"description": "Forbidden - Admin access required"},
            500: {
                "description": "Internal Server Error - Failed to create export request"
            },
        },
        tags=["Admin Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class AdminDisputesExportView(BaseAdminExportView):
    """Admin export view for disputes across all merchants"""

    export_type = ExportRequest.ExportType.DISPUTES
    filter_class = DisputeFilter
    serializer_class = AdminDisputesExportSerializer

    @extend_schema(
        summary="Admin Export Disputes",
        description="Export disputes across all merchants with the same filters as the disputes GET endpoint. \
            Supports filtering by business_id, status, dispute_type, vas_service, transaction_reference, \
                merchant_name, amount ranges, and date ranges. Admin access required.",
        request=AdminDisputesExportSerializer,
        responses={
            201: ExportRequestSerializer,
            400: {"description": "Bad Request - Invalid filters or validation error"},
            403: {"description": "Forbidden - Admin access required"},
            500: {
                "description": "Internal Server Error - Failed to create export request"
            },
        },
        tags=["Admin Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class AdminAuditLogsExportView(BaseAdminExportView):
    """Admin export view for audit logs across all merchants"""

    export_type = ExportRequest.ExportType.AUDIT_LOGS
    filter_class = AuditLogFilter
    serializer_class = AdminAuditLogsExportSerializer

    @extend_schema(
        summary="Admin Export Audit Logs",
        description="Export audit logs across all merchants with the same filters as the audit logs GET endpoint. \
            Supports filtering by business_id, action, status, email, ip_address, \
                resource_type, resource_id, and date ranges. Admin access required.",
        request=AdminAuditLogsExportSerializer,
        responses={
            201: ExportRequestSerializer,
            400: {"description": "Bad Request - Invalid filters or validation error"},
            403: {"description": "Forbidden - Admin access required"},
            500: {
                "description": "Internal Server Error - Failed to create export request"
            },
        },
        tags=["Admin Exports"],
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class AdminExportTypesView(APIView):
    """Get available admin export types and their fields"""

    permission_classes = [IsPlatformAdmin]

    def get(self, request, export_type=None):
        """Get available fields for admin export type"""
        try:
            if export_type:
                # Get fields for specific export type
                export_service = get_admin_export_service(export_type)
                fields = export_service.get_default_fields()
                return Response(
                    {"export_type": export_type, "available_fields": fields}
                )
            else:
                # Get all admin export types and their fields
                from .admin_export_services import ADMIN_EXPORT_SERVICES

                all_fields = {}
                for exp_type in ADMIN_EXPORT_SERVICES.keys():
                    try:
                        export_service = get_admin_export_service(exp_type)
                        all_fields[exp_type] = {
                            "display_name": dict(ExportRequest.ExportType.choices)[
                                exp_type
                            ],
                            "available_fields": export_service.get_default_fields(),
                        }
                    except ValueError:
                        continue

                return Response({"admin_export_types": all_fields})

        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error getting admin export types: {str(e)}")
            return Response(
                {"error": "Failed to get export types"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AdminExportHistoryView(APIView):
    """Get admin export history"""

    permission_classes = [IsPlatformAdmin]

    def get(self, request):
        """Get admin export requests history"""
        try:
            # Get admin export requests (where business is None)
            admin_exports = ExportRequest.objects.filter(
                business__isnull=True
            ).order_by("-created_at")[
                :50
            ]  # Limit to last 50

            serializer = ExportRequestSerializer(admin_exports, many=True)
            return Response(
                {"results": serializer.data, "count": admin_exports.count()}
            )

        except Exception as e:
            logger.error(f"Error getting admin export history: {str(e)}")
            return Response(
                {"error": "Failed to get export history"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

"""
Admin-specific export services for cross-merchant data exports
"""

from datetime import datetime
from typing import Any, Dict, List

from django.db.models import QuerySet

from .export_services import BaseExportService
from .models import ExportRequest
from .utils import serialize_filters_for_json


class AdminBaseExportService(BaseExportService):
    """Base class for admin export services that can export across all merchants"""

    def apply_business_filter(self, queryset: QuerySet, business) -> QuerySet:
        """Override to allow admin exports across all businesses"""
        if business is None:

            return queryset
        else:
            return queryset.filter(business=business)

    def apply_admin_filters(
        self, queryset: QuerySet, filters: Dict[str, Any]
    ) -> QuerySet:
        """Apply admin-specific filters"""

        if filters.get("business_id"):
            queryset = queryset.filter(business_id=filters["business_id"])

        # Filter by business name (partial match)
        if filters.get("business_name"):
            queryset = queryset.filter(
                business__name__icontains=filters["business_name"]
            )

        # Filter by business owner email
        if filters.get("business_owner_email"):
            queryset = queryset.filter(
                business__owner__email__icontains=filters["business_owner_email"]
            )

        return queryset

    def build_queryset(self, business, filters: Dict[str, Any]) -> QuerySet:
        """Build queryset with admin-specific filtering"""
        model_class = self.get_model_class()
        queryset = model_class.objects.all()

        # Apply business filter (None for admin = all businesses)
        queryset = self.apply_business_filter(queryset, business)

        queryset = self.apply_date_filters(queryset, filters)

        queryset = self.apply_admin_filters(queryset, filters)

        # Apply custom filters
        queryset = self.apply_custom_filters(queryset, filters)

        return queryset.order_by("-created_at")

    def create_export_request(
        self,
        user,
        business,
        fields_to_export: List[str] = None,
        filters: Dict[str, Any] = None,
    ) -> ExportRequest:
        """Create admin export request (bypasses business ownership validation)"""
        from datetime import timedelta

        from django.contrib.contenttypes.models import ContentType
        from django.utils import timezone
        from django.utils.text import slugify

        if not fields_to_export:
            fields_to_export = self.get_default_fields()

        # For admin exports, we create the export request directly
        # bypassing the business ownership validation
        try:
            # Get the model class
            model_class = self.get_model_class()
            content_type = ContentType.objects.get_for_model(model_class)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"admin_{slugify(self.export_type)}_{timestamp}.csv"

            # Serialize filters to ensure JSON compatibility
            serialized_filters = serialize_filters_for_json(filters or {})

            # Create export request
            export_request = ExportRequest.objects.create(
                user=user,
                business=business,  # None for admin exports
                export_type=self.export_type,
                content_type=content_type,
                model_name=self.model_name,
                fields_to_export=fields_to_export,
                filters=serialized_filters,
                filename=filename,
                expires_at=timezone.now() + timedelta(days=7),  # 7 days expiration
            )

            return export_request

        except Exception as e:
            raise ValueError(f"Error creating admin export request: {str(e)}")


class AdminAllTransactionsExportService(AdminBaseExportService):
    """Admin export service for all transactions across all merchants"""

    export_type = ExportRequest.ExportType.ALL_TRANSACTIONS
    model_name = "transaction.Transaction"
    default_fields = [
        "id",
        "reference",
        "merchant_reference",
        "status",
        "mode",
        "txn_class",
        "type",
        "amount",
        "charge",
        "revenue",
        "net_amount",
        "business.name",
        "business.owner.email",
        "narration",
        "provider",
        "created_at",
    ]

    def apply_custom_filters(
        self, queryset: QuerySet, filters: Dict[str, Any]
    ) -> QuerySet:
        """Apply transaction-specific filters"""
        if filters.get("status"):
            queryset = queryset.filter(status=filters["status"])

        if filters.get("txn_class"):
            queryset = queryset.filter(txn_class=filters["txn_class"])

        if filters.get("provider"):
            queryset = queryset.filter(provider=filters["provider"])

        return queryset


class AdminDisputesExportService(AdminBaseExportService):
    """Admin export service for disputes across all merchants"""

    export_type = ExportRequest.ExportType.DISPUTES
    model_name = "dispute.Dispute"
    default_fields = [
        "id",
        "dispute_type",
        "topic",
        "transaction_reference",
        "vas_service",
        "amount",
        "charge",
        "status",
        "message",
        "business.name",
        "business.owner.email",
        "merchant_name",
        "created_at",
        "resolved_at",
    ]

    def apply_custom_filters(
        self, queryset: QuerySet, filters: Dict[str, Any]
    ) -> QuerySet:
        """Apply dispute-specific filters"""
        if filters.get("status"):
            queryset = queryset.filter(status=filters["status"])

        if filters.get("dispute_type"):
            queryset = queryset.filter(dispute_type=filters["dispute_type"])

        if filters.get("vas_service"):
            queryset = queryset.filter(vas_service=filters["vas_service"])

        if filters.get("transaction_reference"):
            queryset = queryset.filter(
                transaction_reference__icontains=filters["transaction_reference"]
            )

        if filters.get("merchant_name"):
            queryset = queryset.filter(
                merchant_name__icontains=filters["merchant_name"]
            )

        return queryset


class AdminAuditLogsExportService(AdminBaseExportService):
    """Admin export service for audit logs across all merchants"""

    export_type = ExportRequest.ExportType.AUDIT_LOGS
    model_name = "audit.AuditLog"
    default_fields = [
        "id",
        "email",
        "action",
        "description",
        "ip_address",
        "user_agent",
        "status",
        "resource_type",
        "resource_id",
        "business.name",
        "created_at",
    ]

    def apply_custom_filters(
        self, queryset: QuerySet, filters: Dict[str, Any]
    ) -> QuerySet:
        """Apply audit log-specific filters"""
        if filters.get("action"):
            queryset = queryset.filter(action=filters["action"])

        # Handle multiple actions filter
        if filters.get("actions"):
            actions_list = filters["actions"]
            if isinstance(actions_list, list) and actions_list:
                # Filter out empty strings and None values
                valid_actions = [action for action in actions_list if action]
                if valid_actions:
                    queryset = queryset.filter(action__in=valid_actions)

        if filters.get("status"):
            queryset = queryset.filter(status=filters["status"])

        if filters.get("resource_type"):
            queryset = queryset.filter(resource_type=filters["resource_type"])

        if filters.get("user_email"):
            queryset = queryset.filter(email__icontains=filters["user_email"])

        return queryset


class AdminCommissionsExportService(AdminBaseExportService):
    """Admin export service for commissions across all merchants"""

    export_type = ExportRequest.ExportType.COMMISSIONS
    model_name = "transaction.CommissionTransaction"
    default_fields = [
        "id",
        "reference",
        "source_transaction_reference",
        "txn_class",
        "amount",
        "narration",
        "business.name",
        "business.owner.email",
        "created_at",
    ]

    def apply_custom_filters(
        self, queryset: QuerySet, filters: Dict[str, Any]
    ) -> QuerySet:
        """Apply commission-specific filters"""
        if filters.get("txn_class"):
            queryset = queryset.filter(txn_class=filters["txn_class"])

        return queryset


# Admin export service registry
ADMIN_EXPORT_SERVICES = {
    ExportRequest.ExportType.ALL_TRANSACTIONS: AdminAllTransactionsExportService,
    ExportRequest.ExportType.DISPUTES: AdminDisputesExportService,
    ExportRequest.ExportType.AUDIT_LOGS: AdminAuditLogsExportService,
    ExportRequest.ExportType.COMMISSIONS: AdminCommissionsExportService,
}


def get_admin_export_service(export_type: str) -> AdminBaseExportService:
    """Get the appropriate admin export service for the given export type"""
    service_class = ADMIN_EXPORT_SERVICES.get(export_type)
    if not service_class:
        raise ValueError(f"Unsupported admin export type: {export_type}")
    return service_class()

import logging

from celery import shared_task
from django.template.loader import get_template
from django.utils import timezone
from user.utils import send_email

from .export_services import (
    AirtimeTransactionsExportService,
    AllTransactionsExportService,
    AuditLogsExportService,
    BettingTransactionsExportService,
    CableTVTransactionsExportService,
    CommissionsExportService,
    DataTransactionsExportService,
    DisputesExportService,
    EducationTransactionsExportService,
    ElectricityTransactionsExportService,
    EpinTransactionsExportService,
    KYCTransactionsExportService,
    VirtualAccountTransactionsExportService,
)
from .models import ExportRequest
from .services import ExportService

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_export_task(self, export_request_id: int):
    """
    Celery task to process export request in background
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)
        logger.info(f"Processing export request {export_request_id}")

        # Process the export
        export_service = ExportService()
        success = export_service.process_export(export_request)

        if success:
            # Send email notification
            send_export_ready_email.delay(export_request_id)
            logger.info(f"Successfully processed export request {export_request_id}")
        else:
            # Send failure notification
            send_export_failed_email.delay(export_request_id)
            logger.error(f"Failed to process export request {export_request_id}")

    except ExportRequest.DoesNotExist:
        logger.error(f"Export request {export_request_id} not found")
    except Exception as exc:
        logger.error(f"Error processing export request {export_request_id}: {str(exc)}")

        # Retry the task
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying export request {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            # Mark as failed after max retries
            try:
                export_request = ExportRequest.objects.get(id=export_request_id)
                export_request.status = ExportRequest.Status.FAILED
                export_request.error_message = (
                    f"Failed after {self.max_retries} retries: {str(exc)}"
                )
                export_request.save()
            except ExportRequest.DoesNotExist:
                pass


@shared_task
def send_export_ready_email(export_request_id: int):
    """
    Send email notification when export is ready for download
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)

        if export_request.email_sent:
            logger.info(f"Email already sent for export request {export_request_id}")
            return

        # Prepare email data
        email_data = {
            "user_name": export_request.user.fullname,
            "export_type": export_request.get_export_type_display(),
            "filename": export_request.filename,
            "download_url": export_request.download_url,
            "total_records": export_request.total_records,
            "file_size_mb": (
                round(export_request.file_size / (1024 * 1024), 2)
                if export_request.file_size
                else 0
            ),
            "expires_at": export_request.expires_at,
            "business_name": (
                export_request.business.name
                if export_request.business
                else "Admin Export"
            ),
        }

        # Load email templates
        html_template = get_template("emails/export_ready_template.html")
        text_template = get_template("emails/export_ready_template.txt")

        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)

        # Send email
        subject = f"Your {export_request.get_export_type_display()} Export is Ready"
        send_email(subject, export_request.user.email, html_content, text_content)

        # Mark email as sent
        export_request.email_sent = True
        export_request.email_sent_at = timezone.now()
        export_request.save(update_fields=["email_sent", "email_sent_at"])

        logger.info(f"Export ready email sent for request {export_request_id}")

    except ExportRequest.DoesNotExist:
        logger.error(f"Export request {export_request_id} not found")
    except Exception as exc:
        logger.error(
            f"Error sending export ready email for request {export_request_id}: {str(exc)}"
        )


@shared_task
def cleanup_expired_exports():
    """
    Periodic task to clean up expired export files
    """
    try:
        export_service = ExportService()
        export_service.cleanup_expired_exports()
        logger.info("Completed cleanup of expired exports")
    except Exception as exc:
        logger.error(f"Error during export cleanup: {str(exc)}")


@shared_task
def send_export_failed_email(export_request_id: int):
    """
    Send email notification when export fails
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)

        # Prepare email data
        email_data = {
            "user_name": export_request.user.fullname,
            "export_type": export_request.get_export_type_display(),
            "error_message": export_request.error_message,
            "business_name": (
                export_request.business.name
                if export_request.business
                else "Admin Export"
            ),
        }

        # Load email templates
        html_template = get_template("emails/export_failed_template.html")
        text_template = get_template("emails/export_failed_template.txt")

        html_content = html_template.render(email_data)
        text_content = text_template.render(email_data)

        # Send email
        subject = f"Export Failed - {export_request.get_export_type_display()}"
        send_email(subject, export_request.user.email, html_content, text_content)

        logger.info(f"Export failed email sent for request {export_request_id}")

    except ExportRequest.DoesNotExist:
        logger.error(f"Export request {export_request_id} not found")
    except Exception as exc:
        logger.error(
            f"Error sending export failed email for request {export_request_id}: {str(exc)}"
        )


# ============================================================================
# DEDICATED EXPORT TASKS - Simplified approach with specific tasks per export type
# ============================================================================


def _process_specific_export(
    export_request_id: int, export_service_class, task_name: str
):
    """
    Helper function to process specific export types
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)
        logger.info(f"Processing {task_name} export request {export_request_id}")

        # Create specific export service instance
        export_service = export_service_class()

        # Process the export using the specific service
        success = export_service.process_export(export_request)

        if success:
            # Send email notification
            send_export_ready_email.delay(export_request_id)
            logger.info(
                f"Successfully processed {task_name} export request {export_request_id}"
            )
        else:
            # Send failure notification
            send_export_failed_email.delay(export_request_id)
            logger.error(
                f"Failed to process {task_name} export request {export_request_id}"
            )

        return success

    except ExportRequest.DoesNotExist:
        logger.error(f"Export request {export_request_id} not found")
        return False
    except Exception as exc:
        logger.error(
            f"Error processing {task_name} export request {export_request_id}: {str(exc)}"
        )
        raise exc


def _mark_export_as_failed(export_request_id: int, exc: Exception, max_retries: int):
    """
    Helper function to mark export as failed after max retries
    """
    try:
        export_request = ExportRequest.objects.get(id=export_request_id)
        export_request.status = ExportRequest.Status.FAILED
        export_request.error_message = f"Failed after {max_retries} retries: {str(exc)}"
        export_request.save()
        logger.error(
            f"Marked export request {export_request_id} as failed after {max_retries} retries"
        )
    except ExportRequest.DoesNotExist:
        logger.error(
            f"Could not mark export request {export_request_id} as failed - not found"
        )


@shared_task(bind=True, max_retries=3)
def process_airtime_export_task(self, export_request_id: int):
    """
    Process airtime transactions export
    """
    try:
        return _process_specific_export(
            export_request_id, AirtimeTransactionsExportService, "airtime transactions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying airtime export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_data_export_task(self, export_request_id: int):
    """
    Process data transactions export
    """
    try:
        return _process_specific_export(
            export_request_id, DataTransactionsExportService, "data transactions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying data export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_electricity_export_task(self, export_request_id: int):
    """
    Process electricity transactions export
    """
    try:
        return _process_specific_export(
            export_request_id,
            ElectricityTransactionsExportService,
            "electricity transactions",
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying electricity export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_cable_tv_export_task(self, export_request_id: int):
    """
    Process cable TV transactions export
    """
    try:
        return _process_specific_export(
            export_request_id, CableTVTransactionsExportService, "cable TV transactions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying cable TV export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_betting_export_task(self, export_request_id: int):
    """
    Process betting transactions export
    """
    try:
        return _process_specific_export(
            export_request_id, BettingTransactionsExportService, "betting transactions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying betting export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_education_export_task(self, export_request_id: int):
    """
    Process education transactions export
    """
    try:
        return _process_specific_export(
            export_request_id,
            EducationTransactionsExportService,
            "education transactions",
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying education export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_kyc_export_task(self, export_request_id: int):
    """
    Process KYC transactions export
    """
    try:
        return _process_specific_export(
            export_request_id, KYCTransactionsExportService, "KYC transactions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying KYC export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_epin_export_task(self, export_request_id: int):
    """
    Process epin transactions export
    """
    try:
        return _process_specific_export(
            export_request_id, EpinTransactionsExportService, "epin transactions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying epin export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_virtual_account_export_task(self, export_request_id: int):
    """
    Process virtual account transactions export
    """
    try:
        return _process_specific_export(
            export_request_id,
            VirtualAccountTransactionsExportService,
            "virtual account transactions",
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying virtual account export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_all_transactions_export_task(self, export_request_id: int):
    """
    Process all transactions export
    """
    try:
        return _process_specific_export(
            export_request_id, AllTransactionsExportService, "all transactions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying all transactions export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_commissions_export_task(self, export_request_id: int):
    """
    Process commissions export
    """
    try:
        return _process_specific_export(
            export_request_id, CommissionsExportService, "commissions"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying commissions export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_disputes_export_task(self, export_request_id: int):
    """
    Process disputes export
    """
    try:
        return _process_specific_export(
            export_request_id, DisputesExportService, "disputes"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying disputes export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)


@shared_task(bind=True, max_retries=3)
def process_audit_logs_export_task(self, export_request_id: int):
    """
    Process audit logs export
    """
    try:
        return _process_specific_export(
            export_request_id, AuditLogsExportService, "audit logs"
        )
    except Exception as exc:
        if self.request.retries < self.max_retries:
            logger.info(
                f"Retrying audit logs export {export_request_id} (attempt {self.request.retries + 1})"
            )
            raise self.retry(countdown=60 * (2**self.request.retries))
        else:
            _mark_export_as_failed(export_request_id, exc, self.max_retries)

"""
Admin-specific export URLs
"""

from django.urls import path

from .admin_views import (
    AdminAuditLogsExportView,
    AdminCommissionsExportView,
    AdminDisputesExportView,
    AdminExportHistoryView,
    AdminExportTypesView,
    AdminTransactionsExportView,
)

urlpatterns = [
    # ============================================================================
    # SIMPLIFIED ADMIN EXPORT ENDPOINTS
    # ============================================================================
    # These endpoints use the same filters as existing GET endpoints
    path(
        "admin/exports/transactions/",
        AdminTransactionsExportView.as_view(),
        name="admin-export-transactions",
    ),
    path(
        "admin/exports/commissions/",
        AdminCommissionsExportView.as_view(),
        name="admin-export-commissions",
    ),
    path(
        "admin/exports/disputes/",
        AdminDisputesExportView.as_view(),
        name="admin-export-disputes",
    ),
    path(
        "admin/exports/audit-logs/",
        AdminAuditLogsExportView.as_view(),
        name="admin-export-audit-logs",
    ),
    # Admin helper endpoints
    path(
        "admin/exports/types/",
        AdminExportTypesView.as_view(),
        name="admin-export-types",
    ),
    path(
        "admin/exports/types/<str:export_type>/",
        AdminExportTypesView.as_view(),
        name="admin-export-types-by-type",
    ),
    path(
        "admin/exports/history/",
        AdminExportHistoryView.as_view(),
        name="admin-export-history",
    ),
]

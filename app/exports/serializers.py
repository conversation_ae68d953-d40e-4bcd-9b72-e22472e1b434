from audit.models import AuditLog
from common.enums import (
    AirtimeNetworkEnum,
    BetBillerEnum,
    CableTVBillerEnum,
    EducationProviderEnum,
    ElectricityDiscoEnum,
    KYCTypeEnum,
    VABankProvider,
)
from dispute.enums import DisputeStatus, DisputeType
from rest_framework import serializers
from transaction.enums import TransactionClassEnum, TransactionStatusEnum

from .models import ExportField, ExportRequest


def get_transaction_class_choices():
    """Get transaction class choices from the enum"""
    return [
        (choice.value, choice.value.replace("_", " ").title())
        for choice in TransactionClassEnum
    ]


def get_transaction_status_choices():
    """Get transaction status choices from the enum"""
    return [(choice.value, choice.value.title()) for choice in TransactionStatusEnum]


def get_dispute_status_choices():
    """Get dispute status choices from the enum"""
    return [
        (choice.value, choice.value.replace("_", " ").title())
        for choice in DisputeStatus
    ]


def get_dispute_type_choices():
    """Get dispute type choices from the enum"""
    return [(choice.value, choice.value.title()) for choice in DisputeType]


def get_audit_log_action_choices():
    """Get audit log action choices from the model"""
    return AuditLog.ACTION_CHOICES


def get_audit_log_status_choices():
    """Get audit log status choices from the model"""
    return AuditLog.STATUS_CHOICES


def get_transaction_type_choices():
    """Get all possible transaction type choices from various enums"""
    choices = []

    # Network providers (Airtime, Data, Epin)
    for network in AirtimeNetworkEnum:
        choices.append((network.value, network.value))

    # Cable TV providers
    for provider in CableTVBillerEnum:
        choices.append((provider.value, provider.value))

    # Betting providers
    for provider in BetBillerEnum:
        choices.append((provider.value, provider.value))

    # Electricity providers
    for provider in ElectricityDiscoEnum:
        choices.append((provider.value, provider.value))

    # KYC types
    for kyc_type in KYCTypeEnum:
        choices.append((kyc_type.value, kyc_type.value))

    # Education providers
    for provider in EducationProviderEnum:
        choices.append((provider.value, provider.value))

    # Virtual Account banks
    for bank in VABankProvider:
        choices.append((bank.value.upper(), bank.value.upper()))

    # Add common transaction types
    additional_types = [
        ("TRANSFER", "Transfer"),
        ("VIRTUAL_ACCOUNT", "Virtual Account"),
        ("RECURRING_DEBIT", "Recurring Debit"),
    ]
    choices.extend(additional_types)

    # Remove duplicates and sort
    unique_choices = list(set(choices))
    unique_choices.sort(key=lambda x: x[0])

    return unique_choices


class ExportFieldSerializer(serializers.ModelSerializer):
    """Serializer for ExportField model"""

    class Meta:
        model = ExportField
        fields = [
            "field_name",
            "field_label",
            "field_type",
            "is_default",
            "is_sensitive",
            "order",
        ]


class ExportRequestSerializer(serializers.ModelSerializer):
    """Serializer for ExportRequest model"""

    progress_percentage = serializers.ReadOnlyField()
    is_expired = serializers.ReadOnlyField()
    export_type_display = serializers.CharField(
        source="get_export_type_display", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)

    class Meta:
        model = ExportRequest
        fields = [
            "id",
            "export_type",
            "export_type_display",
            "model_name",
            "filename",
            "status",
            "status_display",
            "total_records",
            "processed_records",
            "progress_percentage",
            "file_size",
            "download_url",
            "error_message",
            "expires_at",
            "is_expired",
            "email_sent",
            "email_sent_at",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "filename",
            "status",
            "total_records",
            "processed_records",
            "file_size",
            "download_url",
            "error_message",
            "expires_at",
            "email_sent",
            "email_sent_at",
            "created_at",
            "updated_at",
        ]


class TransactionsExportSerializer(serializers.Serializer):
    """Export serializer that matches TransactionFilter exactly"""

    # From TransactionFilter Meta fields
    status = serializers.ChoiceField(
        choices=get_transaction_status_choices(),
        required=False,
        allow_blank=True,
        help_text="Transaction status filter",
    )
    type = serializers.ChoiceField(
        choices=get_transaction_type_choices(),
        required=False,
        allow_blank=True,
        help_text="Transaction type filter (provider/network)",
    )
    txn_class = serializers.ChoiceField(
        choices=get_transaction_class_choices(),
        required=False,
        allow_blank=True,
        help_text="Transaction class filter",
    )

    # From DateFilter (inherited by TransactionFilter)
    start = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="Start date for filtering (YYYY-MM-DD)",
    )
    end = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="End date for filtering (YYYY-MM-DD)",
    )


class CommissionsExportSerializer(serializers.Serializer):
    """Export serializer that matches CommissionFilter exactly"""

    # From CommissionFilter Meta fields
    txn_class = serializers.ChoiceField(
        choices=get_transaction_class_choices(),
        required=False,
        allow_blank=True,
        help_text="Transaction class filter",
    )

    # From DateFilter (inherited by CommissionFilter)
    start = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="Start date for filtering (YYYY-MM-DD)",
    )
    end = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="End date for filtering (YYYY-MM-DD)",
    )

    # From CommissionTransactionViewSet search_fields
    reference = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Commission reference (partial match)",
    )
    source_transaction__reference = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Source transaction reference (partial match)",
    )
    source_transaction__merchant_reference = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Source transaction merchant reference (partial match)",
    )


class DisputesExportSerializer(serializers.Serializer):
    """Export serializer that matches DisputeFilter exactly"""

    # From DisputeFilter fields
    status = serializers.ChoiceField(
        choices=get_dispute_status_choices(),
        required=False,
        allow_blank=True,
        help_text="Filter by dispute status",
    )
    dispute_type = serializers.ChoiceField(
        choices=get_dispute_type_choices(),
        required=False,
        allow_blank=True,
        help_text="Filter by dispute type",
    )
    vas_service = serializers.ChoiceField(
        choices=get_transaction_class_choices(),
        required=False,
        allow_blank=True,
        help_text="Filter by VAS service type (transaction class)",
    )
    transaction_reference = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by transaction reference (partial match)",
    )
    created_at_after = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="Filter disputes created after this date",
    )
    created_at_before = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="Filter disputes created before this date",
    )
    transaction_date_after = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="Filter by transaction date after",
    )
    transaction_date_before = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="Filter by transaction date before",
    )

    # From DisputeViewSet search_fields
    message = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Search in dispute message (partial match)",
    )
    topic = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Search in dispute topic (partial match)",
    )


class AuditLogsExportSerializer(serializers.Serializer):
    """Export serializer that matches AuditLogFilter exactly"""

    # From AuditLogFilter date filters
    start = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="Start date for filtering (YYYY-MM-DD HH:MM:SS)",
    )
    end = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="End date for filtering (YYYY-MM-DD HH:MM:SS)",
    )

    # Date shortcuts
    last_24h = serializers.BooleanField(
        required=False,
        help_text="Filter logs from last 24 hours",
    )
    last_7d = serializers.BooleanField(
        required=False,
        help_text="Filter logs from last 7 days",
    )
    last_30d = serializers.BooleanField(
        required=False,
        help_text="Filter logs from last 30 days",
    )

    # Action filters
    action = serializers.ChoiceField(
        choices=get_audit_log_action_choices(),
        required=False,
        allow_blank=True,
        help_text="Filter by action type",
    )
    actions = serializers.ListField(
        child=serializers.ChoiceField(
            choices=get_audit_log_action_choices(), allow_blank=True
        ),
        required=False,
        allow_empty=True,
        help_text="Filter by multiple action types (array of action values)",
    )

    # Status filters
    status = serializers.ChoiceField(
        choices=get_audit_log_status_choices(),
        required=False,
        allow_blank=True,
        help_text="Filter by status",
    )

    # User filters
    email = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by user email (partial match)",
    )
    user_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by user ID",
    )

    # IP address filter
    ip_address = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by IP address (partial match)",
    )

    # Resource filters
    resource_type = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by resource type",
    )
    resource_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by resource ID",
    )

    # Success/failure filters
    successful_only = serializers.BooleanField(
        required=False,
        help_text="Show only successful actions",
    )
    failed_only = serializers.BooleanField(
        required=False,
        help_text="Show only failed actions",
    )

    # From AuditLogViewSet search_fields
    description = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Search in audit log description (partial match)",
    )


# Admin versions with business_id filter
class AdminTransactionsExportSerializer(TransactionsExportSerializer):
    """Admin version of transactions export with cross-merchant capability"""

    business_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by business ID (admin only)",
    )


class AdminCommissionsExportSerializer(CommissionsExportSerializer):
    """Admin version of commissions export with cross-merchant capability"""

    business_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by business ID (admin only)",
    )


class AdminDisputesExportSerializer(DisputesExportSerializer):
    """Admin version of disputes export with cross-merchant capability"""

    business_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by business ID (admin only)",
    )
    merchant_name = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by merchant name (partial match)",
    )


class AdminAuditLogsExportSerializer(AuditLogsExportSerializer):
    """Admin version of audit logs export with cross-merchant capability"""

    business_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by business ID (admin only)",
    )


class BaseExportSerializer(serializers.Serializer):
    """Base serializer for legacy export endpoints"""

    # Common date filters
    start = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="Start date for filtering (YYYY-MM-DD HH:MM:SS)",
    )
    end = serializers.DateTimeField(
        required=False,
        allow_null=True,
        help_text="End date for filtering (YYYY-MM-DD HH:MM:SS)",
    )

    def to_internal_value(self, data):
        """Clean up empty string values before validation"""

        cleaned_data = data.copy() if hasattr(data, "copy") else dict(data)

        # Convert empty strings to None for datetime fields
        datetime_fields = ["start", "end"]
        decimal_fields = []

        for field in datetime_fields + decimal_fields:
            if field in cleaned_data and cleaned_data[field] == "":
                cleaned_data[field] = None

        return super().to_internal_value(cleaned_data)

    status = serializers.ChoiceField(
        choices=[
            ("PENDING", "Pending"),
            ("PROCESSING", "Processing"),
            ("COMPLETED", "Completed"),
            ("FAILED", "Failed"),
        ],
        required=False,
        allow_blank=True,
        help_text="Transaction status filter",
    )

    reference = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text="Transaction reference to search for (partial match)",
    )

    def validate(self, attrs):
        """Validate date range and clean up empty values"""
        # Validate date range
        start = attrs.get("start")
        end = attrs.get("end")

        if start and end and start > end:
            raise serializers.ValidationError(
                "start date must be earlier than or equal to end date"
            )

        # Remove empty string values from all fields
        cleaned_attrs = {}
        for key, value in attrs.items():
            if value is not None and value != "" and value != []:
                cleaned_attrs[key] = value

        return cleaned_attrs


class AdminExportSerializer(BaseExportSerializer):
    """Admin export serializer with business_id filter for cross-merchant exports"""

    business_id = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Filter by specific business ID (admin only)",
    )

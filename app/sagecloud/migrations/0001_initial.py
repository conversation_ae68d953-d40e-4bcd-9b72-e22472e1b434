# Generated by Django 5.1.7 on 2025-07-20 13:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ActionEvents",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("batch_id", models.<PERSON>r<PERSON><PERSON>(max_length=36)),
                ("user_id", models.PositiveBigIntegerField()),
                ("name", models.CharField(max_length=255)),
                ("actionable_type", models.CharField(max_length=255)),
                ("actionable_id", models.PositiveBigIntegerField()),
                ("target_type", models.Char<PERSON>ield(max_length=255)),
                ("target_id", models.PositiveBigIntegerField()),
                ("model_type", models.CharField(max_length=255)),
                ("model_id", models.PositiveBigIntegerField(blank=True, null=True)),
                ("fields", models.TextField()),
                ("status", models.<PERSON>r<PERSON>ield(max_length=25)),
                ("exception", models.TextField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("original", models.TextField(blank=True, null=True)),
                ("changes", models.TextField(blank=True, null=True)),
            ],
            options={
                "db_table": "action_events",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Activities",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("user_id", models.IntegerField()),
                ("info", models.TextField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "activities",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ApiLogs",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("user_id", models.CharField(blank=True, max_length=255, null=True)),
                ("url", models.CharField(blank=True, max_length=255, null=True)),
                ("method", models.CharField(blank=True, max_length=255, null=True)),
                ("client_ip", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "duration",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                ("request_body", models.TextField(blank=True, null=True)),
                ("response_code", models.IntegerField(blank=True, null=True)),
                ("response_body", models.TextField(blank=True, null=True)),
                ("provider", models.CharField(blank=True, max_length=255, null=True)),
                ("provider_request_body", models.TextField(blank=True, null=True)),
                ("provider_response_code", models.IntegerField(blank=True, null=True)),
                ("provider_response_body", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "transaction_id",
                    models.PositiveBigIntegerField(blank=True, null=True),
                ),
            ],
            options={
                "db_table": "api_logs",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ApiLogsV2",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("client_ip", models.CharField(max_length=45)),
                ("method", models.CharField(max_length=255)),
                ("url", models.CharField(max_length=255)),
                ("start_time", models.BigIntegerField()),
                ("merchant_request_body", models.JSONField()),
                ("provider_request_body", models.JSONField(blank=True, null=True)),
                ("finish_time", models.BigIntegerField(blank=True, null=True)),
                ("merchant_response_body", models.JSONField(blank=True, null=True)),
                ("merchant_response_code", models.IntegerField(blank=True, null=True)),
                ("provider_response_body", models.JSONField(blank=True, null=True)),
                ("provider_response_code", models.IntegerField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "api_logs_v2",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BankAccounts",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("bank_code", models.CharField(blank=True, max_length=255, null=True)),
                ("bank_name", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "currency_code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
                ("provider", models.CharField(blank=True, max_length=255, null=True)),
                ("status", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "account_email",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "account_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "reservation_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("account_number", models.CharField(max_length=255)),
                ("business_id", models.IntegerField(blank=True, null=True)),
                ("third_party", models.IntegerField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "account_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("payment_id", models.IntegerField(blank=True, null=True)),
                ("static", models.IntegerField(blank=True, null=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("is_blocked", models.IntegerField()),
                ("bvn", models.CharField(blank=True, max_length=255, null=True)),
                ("block_reason", models.TextField(blank=True, null=True)),
            ],
            options={
                "db_table": "bank_accounts",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BankAccountTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bank_account_id", models.PositiveBigIntegerField()),
                ("transaction_id", models.PositiveBigIntegerField()),
            ],
            options={
                "db_table": "bank_account_transaction",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Banks",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("bank_code", models.CharField(blank=True, max_length=255, null=True)),
                ("institution_code", models.CharField(max_length=255)),
                ("category", models.IntegerField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "etranzact_code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "banks",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BeneficialOwners",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.IntegerField()),
                ("name", models.CharField(max_length=255)),
                ("email", models.CharField(max_length=255)),
                ("phone", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "beneficial_owners",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BillerItems",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("biller_id", models.PositiveBigIntegerField()),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                ("amount", models.CharField(blank=True, max_length=255, null=True)),
                ("code", models.CharField(blank=True, max_length=255, null=True)),
                ("allowance", models.CharField(blank=True, max_length=255, null=True)),
                ("duration", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "biller_items",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Billers",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                ("biller_type", models.CharField(max_length=11)),
                ("provider", models.CharField(max_length=255)),
                (
                    "biller_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "category_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("label1", models.CharField(blank=True, max_length=255, null=True)),
                ("narration", models.CharField(blank=True, max_length=255, null=True)),
                ("short_name", models.CharField(blank=True, max_length=255, null=True)),
                ("charge", models.CharField(blank=True, max_length=255, null=True)),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
                ("logo_url", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "billers",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Businesses",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("info", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                ("email", models.CharField(max_length=255, unique=True)),
                ("address", models.CharField(blank=True, max_length=255, null=True)),
                ("status", models.CharField(max_length=9)),
                ("address_2", models.CharField(blank=True, max_length=255, null=True)),
                ("logo", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "charge_back_email",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                ("website", models.CharField(blank=True, max_length=255, null=True)),
                ("city", models.CharField(blank=True, max_length=255, null=True)),
                ("state", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "postal_code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("country", models.CharField(max_length=255)),
                ("facebook", models.CharField(blank=True, max_length=255, null=True)),
                ("twitter", models.CharField(blank=True, max_length=255, null=True)),
                ("instagram", models.CharField(blank=True, max_length=255, null=True)),
                ("linkedin", models.CharField(blank=True, max_length=255, null=True)),
                ("youtube", models.CharField(blank=True, max_length=255, null=True)),
                ("category", models.CharField(blank=True, max_length=255, null=True)),
                ("size", models.IntegerField(blank=True, null=True)),
                ("bvn", models.CharField(blank=True, max_length=255, null=True)),
                ("type", models.CharField(blank=True, max_length=6, null=True)),
                ("rc_number", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "rc_registration_document",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("bn_number", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "bn_registration_document",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "cac_it_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "cac_it_registration_document",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "bank_account_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("bank_name", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "bank_account_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "certificate_of_incorporation",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "articles_of_association",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("cac_form", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "utility_bill",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("bank_code", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "other_document",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "alert_balance",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("has_reserved_account", models.IntegerField()),
                ("user_id", models.IntegerField(blank=True, null=True)),
                ("lock_update", models.IntegerField()),
            ],
            options={
                "db_table": "businesses",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BusinessKeys",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.BigIntegerField()),
                ("public_key", models.CharField(blank=True, max_length=255, null=True)),
                ("secret_key", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "encryption_key",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("iv", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "callback_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "webhook_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ip_whitelist",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "ip_blacklist",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("vas_settings", models.JSONField(blank=True, null=True)),
                ("payment_gateway_settings", models.JSONField(blank=True, null=True)),
                ("virtual_account_settings", models.JSONField(blank=True, null=True)),
                (
                    "webhook_signature",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "db_table": "business_keys",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CableTvAddOns",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("type", models.CharField(max_length=255)),
                ("add_on_product_code", models.CharField(max_length=255)),
                ("product_code", models.CharField(max_length=255)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                ("price", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("month", models.IntegerField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "cable_tv_add_ons",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CableTvBouquets",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("type", models.CharField(max_length=9)),
                ("code", models.CharField(max_length=255)),
                ("name", models.CharField(max_length=255)),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("price", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "cable_tv_bouquets",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CapricornDataPlans",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("service_type", models.CharField(max_length=255)),
                ("allowance", models.CharField(blank=True, max_length=255, null=True)),
                ("price", models.CharField(max_length=255)),
                ("validity", models.CharField(blank=True, max_length=255, null=True)),
                ("datacode", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "capricorn_data_plans",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ChangeRequests",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("requester_type", models.CharField(max_length=255)),
                ("requester_id", models.PositiveBigIntegerField()),
                (
                    "entity_type",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("entity_id", models.PositiveBigIntegerField(blank=True, null=True)),
                ("service", models.CharField(blank=True, max_length=255, null=True)),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
                ("status", models.CharField(max_length=255)),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("metadata", models.JSONField(blank=True, null=True)),
                (
                    "last_updated_by",
                    models.PositiveBigIntegerField(blank=True, null=True),
                ),
                ("last_updated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "change_requests",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Commissions",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.IntegerField()),
                ("product", models.CharField(blank=True, max_length=255, null=True)),
                ("amount", models.FloatField()),
                ("info", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "commissions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CommissionTransactions",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("wallet_id", models.IntegerField()),
                ("business_id", models.IntegerField()),
                ("amount", models.FloatField()),
                ("prev_balance", models.FloatField()),
                ("new_balance", models.FloatField()),
                ("currency", models.CharField(max_length=255)),
                ("type", models.CharField(blank=True, max_length=6, null=True)),
                ("product", models.CharField(blank=True, max_length=255, null=True)),
                ("info", models.TextField()),
                ("reference", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "commission_transactions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CorporateDataWallets",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("balance", models.DecimalField(decimal_places=3, max_digits=10)),
                (
                    "account_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("commission", models.DecimalField(decimal_places=3, max_digits=10)),
                ("status", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "corporate_data_wallets",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="DataPlans",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("type", models.CharField(max_length=255)),
                ("code", models.CharField(max_length=255)),
                ("description", models.CharField(max_length=255)),
                ("amount", models.CharField(max_length=255)),
                ("price", models.CharField(blank=True, max_length=255, null=True)),
                ("value", models.CharField(max_length=255)),
                ("duration", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "data_plans",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Directors",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.IntegerField()),
                ("name", models.CharField(max_length=255)),
                ("email", models.CharField(max_length=255)),
                ("phone", models.CharField(max_length=255)),
                ("bvn", models.CharField(blank=True, max_length=255, null=True)),
                ("nin", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "directors",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="DisputeMessages",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("dispute_id", models.IntegerField()),
                ("admin_id", models.IntegerField(blank=True, null=True)),
                ("text", models.TextField(blank=True, null=True)),
                ("type", models.IntegerField()),
                ("image_url", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "dispute_messages",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Disputes",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.IntegerField()),
                ("reference", models.CharField(blank=True, max_length=255, null=True)),
                ("subject", models.CharField(max_length=255)),
                ("status", models.IntegerField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "disputes",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="EducationPlans",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("service", models.CharField(max_length=255)),
                ("type", models.CharField(max_length=255)),
                ("price", models.DecimalField(decimal_places=2, max_digits=8)),
                ("provider", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "education_plans",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Epins",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("reference", models.CharField(max_length=255)),
                ("amount", models.CharField(max_length=255)),
                ("pin", models.CharField(max_length=255)),
                ("serial", models.CharField(max_length=255)),
                ("instruction", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "epins",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="FailedJobs",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "uuid",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                ("connection", models.TextField()),
                ("queue", models.TextField()),
                ("payload", models.TextField()),
                ("exception", models.TextField()),
                ("failed_at", models.DateTimeField()),
            ],
            options={
                "db_table": "failed_jobs",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Fees",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.BigIntegerField()),
                ("products", models.JSONField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "fees",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="GeneralFees",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("last_updated_by", models.PositiveBigIntegerField()),
                ("old_fee_settings", models.JSONField()),
                ("new_fee_settings", models.JSONField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "general_fees",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="IswBanks",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("b_id", models.CharField(max_length=255)),
                ("cbn_code", models.CharField(max_length=255)),
                ("bank_name", models.CharField(max_length=255)),
                ("bank_code", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "isw_banks",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="JambPins",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("reference", models.CharField(blank=True, max_length=255, null=True)),
                ("amount", models.CharField(blank=True, max_length=255, null=True)),
                ("pin", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "profile_code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("phone", models.CharField(blank=True, max_length=255, null=True)),
                ("full_name", models.CharField(blank=True, max_length=255, null=True)),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "instruction",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "jamb_pins",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="JobBatches",
            fields=[
                (
                    "id",
                    models.CharField(max_length=255, primary_key=True, serialize=False),
                ),
                ("name", models.CharField(max_length=255)),
                ("total_jobs", models.IntegerField()),
                ("pending_jobs", models.IntegerField()),
                ("failed_jobs", models.IntegerField()),
                ("failed_job_ids", models.TextField()),
                ("options", models.TextField(blank=True, null=True)),
                ("cancelled_at", models.IntegerField(blank=True, null=True)),
                ("created_at", models.IntegerField()),
                ("finished_at", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "job_batches",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Jobs",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("queue", models.CharField(max_length=255)),
                ("payload", models.TextField()),
                ("attempts", models.PositiveIntegerField()),
                ("reserved_at", models.PositiveIntegerField(blank=True, null=True)),
                ("available_at", models.PositiveIntegerField()),
                ("created_at", models.PositiveIntegerField()),
            ],
            options={
                "db_table": "jobs",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Kyc",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("slug", models.CharField(max_length=255)),
                ("description", models.CharField(max_length=255)),
                ("cost", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "kyc",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Kycs",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("level", models.CharField(max_length=255)),
                ("daily_limit", models.FloatField()),
                ("max_balance", models.FloatField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "kycs",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="MamaAfricaVendors",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("value", models.IntegerField()),
                ("product_id", models.IntegerField()),
                ("vendor_id", models.IntegerField()),
                ("denom_code", models.CharField(max_length=10)),
                ("service", models.CharField(max_length=20)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "mama_africa_vendors",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Mandates",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.PositiveBigIntegerField()),
                ("reference", models.CharField(max_length=255)),
                (
                    "mandate_code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("subscriber_code", models.CharField(max_length=255)),
                ("account_number", models.CharField(max_length=255)),
                ("account_name", models.CharField(max_length=255)),
                ("bank_code", models.CharField(max_length=255)),
                ("bank_name", models.CharField(max_length=255)),
                ("status", models.CharField(max_length=255)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("customer_phone_number", models.CharField(max_length=255)),
                ("customer_email", models.CharField(max_length=255)),
                ("customer_address", models.CharField(max_length=255)),
                ("bvn", models.CharField(blank=True, max_length=255, null=True)),
                ("biller_id", models.CharField(blank=True, max_length=255, null=True)),
                ("kyc", models.CharField(blank=True, max_length=255, null=True)),
                ("frequency", models.CharField(blank=True, max_length=255, null=True)),
                ("frequency_interval", models.BigIntegerField()),
                ("meta_data", models.JSONField(blank=True, null=True)),
                ("narration", models.CharField(max_length=255)),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                ("debit_day", models.BigIntegerField(blank=True, null=True)),
                ("debit_month", models.BigIntegerField(blank=True, null=True)),
                ("last_processed_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "mandates",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="MandateTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mandate_id", models.PositiveBigIntegerField()),
                ("transaction_id", models.PositiveBigIntegerField()),
            ],
            options={
                "db_table": "mandate_transaction",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Migrations",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("migration", models.CharField(max_length=255)),
                ("batch", models.IntegerField()),
            ],
            options={
                "db_table": "migrations",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Permissions",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("guard_name", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "permissions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Roles",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("guard_name", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "roles",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="NovaFieldAttachments",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("attachable_type", models.CharField(max_length=255)),
                ("attachable_id", models.PositiveBigIntegerField()),
                ("attachment", models.CharField(max_length=255)),
                ("disk", models.CharField(max_length=255)),
                ("url", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "nova_field_attachments",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="NovaNotifications",
            fields=[
                (
                    "id",
                    models.CharField(max_length=36, primary_key=True, serialize=False),
                ),
                ("type", models.CharField(max_length=255)),
                ("notifiable_type", models.CharField(max_length=255)),
                ("notifiable_id", models.PositiveBigIntegerField()),
                ("data", models.TextField()),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "nova_notifications",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="NovaPendingFieldAttachments",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("draft_id", models.CharField(max_length=255)),
                ("attachment", models.CharField(max_length=255)),
                ("disk", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "nova_pending_field_attachments",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="OauthAccessTokens",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("user_id", models.PositiveBigIntegerField(blank=True, null=True)),
                ("client_id", models.PositiveBigIntegerField()),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                ("scopes", models.TextField(blank=True, null=True)),
                ("revoked", models.IntegerField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "oauth_access_tokens",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="OauthAuthCodes",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("user_id", models.PositiveBigIntegerField()),
                ("client_id", models.PositiveBigIntegerField()),
                ("scopes", models.TextField(blank=True, null=True)),
                ("revoked", models.IntegerField()),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "oauth_auth_codes",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="OauthClients",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("user_id", models.PositiveBigIntegerField(blank=True, null=True)),
                ("name", models.CharField(max_length=255)),
                ("secret", models.CharField(blank=True, max_length=100, null=True)),
                ("provider", models.CharField(blank=True, max_length=255, null=True)),
                ("redirect", models.TextField()),
                ("personal_access_client", models.IntegerField()),
                ("password_client", models.IntegerField()),
                ("revoked", models.IntegerField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "oauth_clients",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="OauthPersonalAccessClients",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("client_id", models.PositiveBigIntegerField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "oauth_personal_access_clients",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="OauthRefreshTokens",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("access_token_id", models.CharField(max_length=100)),
                ("revoked", models.IntegerField()),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "oauth_refresh_tokens",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Partners",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                ("slug", models.CharField(blank=True, max_length=255, null=True)),
                ("test_credentials", models.JSONField(blank=True, null=True)),
                ("credentials", models.JSONField(blank=True, null=True)),
                ("status", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("charges", models.JSONField(blank=True, null=True)),
            ],
            options={
                "db_table": "partners",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PasswordResets",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.CharField(max_length=255)),
                ("token", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "password_resets",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PaymentCards",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "card_holder_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("masked_pan", models.CharField(blank=True, max_length=255, null=True)),
                ("expiry", models.CharField(blank=True, max_length=255, null=True)),
                ("card_type", models.CharField(blank=True, max_length=255, null=True)),
                ("status", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "issuer_bank_code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "payment_cards",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PowerTokens",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("reference", models.CharField(max_length=255)),
                ("token", models.CharField(max_length=255)),
                ("unit", models.CharField(max_length=255)),
                ("amount", models.CharField(max_length=255)),
                ("transid", models.CharField(db_column="transId", max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "power_tokens",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProductFees",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.PositiveBigIntegerField()),
                ("product_id", models.PositiveBigIntegerField()),
                ("is_active", models.IntegerField()),
                ("services", models.JSONField()),
                ("user_id", models.PositiveBigIntegerField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "product_fees",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Products",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("slug", models.CharField(max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("charge", models.FloatField()),
                ("charge_type", models.CharField(max_length=10)),
                ("vas_commission", models.FloatField()),
                ("merchant_commission", models.FloatField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("charges", models.JSONField(blank=True, null=True)),
            ],
            options={
                "db_table": "products",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Providers",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("slug", models.CharField(max_length=255)),
                ("product", models.CharField(blank=True, max_length=255, null=True)),
                ("status", models.CharField(max_length=8)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "providers",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PulseAggregates",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("bucket", models.PositiveIntegerField()),
                ("period", models.PositiveIntegerField()),
                ("type", models.CharField(max_length=255)),
                ("key", models.TextField()),
                ("key_hash", models.CharField(blank=True, max_length=16, null=True)),
                ("aggregate", models.CharField(max_length=255)),
                ("value", models.DecimalField(decimal_places=2, max_digits=20)),
                ("count", models.PositiveIntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "pulse_aggregates",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PulseEntries",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("timestamp", models.PositiveIntegerField()),
                ("type", models.CharField(max_length=255)),
                ("key", models.TextField()),
                ("key_hash", models.CharField(blank=True, max_length=16, null=True)),
                ("value", models.BigIntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "pulse_entries",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PulseValues",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("timestamp", models.PositiveIntegerField()),
                ("type", models.CharField(max_length=255)),
                ("key", models.TextField()),
                ("key_hash", models.CharField(blank=True, max_length=16, null=True)),
                ("value", models.TextField()),
            ],
            options={
                "db_table": "pulse_values",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ReQueries",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("provider", models.CharField(max_length=255)),
                ("status", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "re_queries",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SagePaySettings",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.BigIntegerField()),
                (
                    "callback_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "sage_pay_settings",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SagePayTransactions",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.PositiveBigIntegerField()),
                ("access_code", models.CharField(max_length=255)),
                ("session_id", models.CharField(max_length=255)),
                (
                    "aes_256_key",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("version", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "authentication_limit",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("ip_address", models.CharField(max_length=255)),
                ("browser", models.CharField(blank=True, max_length=255, null=True)),
                ("browser_details", models.JSONField(blank=True, null=True)),
                (
                    "auth_status",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("auth_html", models.TextField(blank=True, null=True)),
                (
                    "pay_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("display_success", models.IntegerField()),
                (
                    "callback_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("customer_email", models.CharField(max_length=255)),
                (
                    "customer_phone",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("amount", models.FloatField()),
                ("net_amount", models.FloatField()),
                ("charge", models.FloatField()),
                ("info", models.TextField()),
                ("status", models.CharField(max_length=255)),
                ("channel", models.CharField(max_length=255)),
                ("reference", models.CharField(max_length=255)),
                ("external_reference", models.CharField(max_length=255)),
                ("wallet_debited", models.IntegerField(blank=True, null=True)),
                ("wallet_credited", models.IntegerField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "payment_card_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "payment_methods",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("merchant_bears_charge", models.IntegerField()),
                ("metadata", models.JSONField(blank=True, null=True)),
            ],
            options={
                "db_table": "sage_pay_transactions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SagePayWallets",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.BigIntegerField()),
                ("account_number", models.CharField(max_length=10)),
                ("balance", models.DecimalField(decimal_places=2, max_digits=15)),
                ("commission", models.DecimalField(decimal_places=2, max_digits=15)),
                ("status", models.CharField(max_length=9)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "sage_pay_wallets",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SagePayWalletTransactions",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("sage_pay_wallet_id", models.IntegerField()),
                ("business_id", models.IntegerField()),
                ("amount", models.FloatField()),
                ("prev_balance", models.FloatField()),
                ("new_balance", models.FloatField()),
                ("type", models.CharField(max_length=6)),
                ("info", models.TextField()),
                ("reference", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "sage_pay_wallet_transactions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ServiceCodes",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("provider", models.CharField(max_length=255)),
                ("code", models.CharField(max_length=255)),
                ("alias", models.CharField(max_length=255)),
                ("description", models.CharField(max_length=255)),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "service_codes",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ServiceFees",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("type", models.CharField(max_length=255)),
                ("is_active", models.IntegerField()),
                ("charge", models.DecimalField(decimal_places=2, max_digits=8)),
                ("commission", models.DecimalField(decimal_places=2, max_digits=8)),
                ("vas_commission", models.DecimalField(decimal_places=2, max_digits=8)),
                ("is_flat_charge", models.IntegerField()),
                ("flat_charge", models.DecimalField(decimal_places=2, max_digits=8)),
                ("range", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "service_fees",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Services",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("slug", models.CharField(max_length=255)),
                ("providers", models.CharField(max_length=255)),
                ("active_provider", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "services",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ShagoDataPlans",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("data_plan_id", models.PositiveBigIntegerField()),
                ("price", models.CharField(max_length=20)),
                ("code", models.CharField(max_length=20)),
                ("allowance", models.CharField(max_length=255)),
                ("validity", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("network", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "shago_data_plans",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SmeDataPlans",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=50)),
                ("service_code", models.CharField(max_length=20)),
                (
                    "grace_hub_code",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "sme_data_plans",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SMEDataWallets",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("balance", models.DecimalField(decimal_places=3, max_digits=10)),
                (
                    "account_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("commission", models.DecimalField(decimal_places=3, max_digits=10)),
                ("status", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "s_m_e_data_wallets",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SmileBundlePlans",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(max_length=255)),
                ("allowance", models.CharField(blank=True, max_length=255, null=True)),
                ("price", models.CharField(max_length=255)),
                ("validity", models.CharField(blank=True, max_length=255, null=True)),
                ("datacode", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "smile_bundle_plans",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SoniteV2DataPlans",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("data_plan_id", models.PositiveBigIntegerField()),
                ("network", models.CharField(max_length=255)),
                ("code", models.CharField(max_length=255)),
                ("description", models.CharField(max_length=255)),
                ("amount", models.CharField(max_length=255)),
                ("value", models.CharField(max_length=255)),
                ("duration", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "sonite_v2_data_plans",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TelescopeEntries",
            fields=[
                ("sequence", models.BigAutoField(primary_key=True, serialize=False)),
                ("uuid", models.CharField(max_length=36, unique=True)),
                ("batch_id", models.CharField(max_length=36)),
                (
                    "family_hash",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("should_display_on_index", models.IntegerField()),
                ("type", models.CharField(max_length=20)),
                ("content", models.TextField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "telescope_entries",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TelescopeEntriesTags",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("tag", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "telescope_entries_tags",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TelescopeMonitoring",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("tag", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "telescope_monitoring",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Transactions",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.PositiveBigIntegerField()),
                ("amount", models.FloatField()),
                ("net_amount", models.FloatField()),
                ("charge", models.FloatField()),
                ("status", models.CharField(blank=True, max_length=255, null=True)),
                ("channel", models.CharField(max_length=255)),
                ("reference", models.CharField(max_length=255)),
                (
                    "external_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
                ("sub_type", models.CharField(blank=True, max_length=255, null=True)),
                ("wallet_debited", models.IntegerField(blank=True, null=True)),
                ("provider_id", models.PositiveBigIntegerField(blank=True, null=True)),
                ("wallet_credited", models.IntegerField(blank=True, null=True)),
                ("info", models.TextField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("model_id", models.PositiveBigIntegerField(blank=True, null=True)),
                ("margin", models.FloatField()),
                ("model_type", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "db_table": "transactions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TransactionTransfers",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("transaction_id", models.PositiveBigIntegerField()),
                ("sessionid", models.CharField(max_length=255)),
                (
                    "provider_transaction_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "transaction_transfers",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UserAuthLogs",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("email", models.CharField(blank=True, max_length=255, null=True)),
                ("ip_address", models.CharField(max_length=45)),
                ("log_type", models.CharField(max_length=255)),
                ("user_agent", models.CharField(blank=True, max_length=255, null=True)),
                ("info", models.JSONField(blank=True, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "user_auth_logs",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Users",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.BigIntegerField()),
                ("firstname", models.CharField(max_length=255)),
                ("lastname", models.CharField(max_length=255)),
                ("email", models.CharField(max_length=255, unique=True)),
                ("phone", models.CharField(blank=True, max_length=255, null=True)),
                ("dob", models.DateField(blank=True, null=True)),
                ("password", models.CharField(max_length=255)),
                ("two_factor_secret", models.TextField(blank=True, null=True)),
                ("two_factor_recovery_codes", models.TextField(blank=True, null=True)),
                (
                    "two_factor_confirmed_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "remember_token",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "users",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="VirtualAccountInventories",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("account_number", models.CharField(max_length=255, unique=True)),
                ("provider", models.CharField(max_length=255)),
                ("is_used", models.IntegerField()),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("next_available_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "virtual_account_inventories",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="VirtualAccountSettlements",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "account_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "wallet_balance",
                    models.DecimalField(decimal_places=2, max_digits=14),
                ),
                ("bank_code", models.CharField(blank=True, max_length=255, null=True)),
                ("status", models.CharField(blank=True, max_length=255, null=True)),
                ("message", models.CharField(blank=True, max_length=255, null=True)),
                ("reference", models.CharField(blank=True, max_length=255, null=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "virtual_account_settlements",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WaecPins",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("reference", models.CharField(blank=True, max_length=255, null=True)),
                ("amount", models.CharField(blank=True, max_length=255, null=True)),
                ("pin", models.CharField(blank=True, max_length=255, null=True)),
                ("serial", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "instruction",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "waec_pins",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Wallets",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.BigIntegerField()),
                ("is_gl", models.IntegerField()),
                ("can_be_negative", models.IntegerField()),
                ("account_number", models.CharField(max_length=10)),
                ("balance", models.DecimalField(decimal_places=2, max_digits=15)),
                ("commission", models.FloatField()),
                ("status", models.CharField(max_length=9)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("type", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "db_table": "wallets",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WalletTopUpRequests",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.IntegerField()),
                ("amount", models.FloatField()),
                ("name", models.CharField(max_length=255)),
                ("info", models.TextField()),
                ("status", models.CharField(blank=True, max_length=8, null=True)),
                ("image_url", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("wallet_type", models.CharField(max_length=255)),
            ],
            options={
                "db_table": "wallet_top_up_requests",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="WalletTransactions",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("wallet_id", models.IntegerField()),
                ("business_id", models.IntegerField()),
                ("amount", models.FloatField()),
                ("prev_balance", models.FloatField()),
                ("new_balance", models.FloatField()),
                ("type", models.CharField(max_length=6)),
                ("info", models.TextField()),
                ("reference", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("wallet_type", models.CharField(max_length=255)),
                ("transaction_id", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "wallet_transactions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Webhooks",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("business_id", models.PositiveBigIntegerField()),
                ("payload", models.JSONField()),
                ("url", models.CharField(max_length=255)),
                ("tries", models.IntegerField()),
                ("response_code", models.IntegerField()),
                ("type", models.CharField(max_length=255)),
                ("owner", models.CharField(max_length=255)),
                ("created_at", models.DateTimeField(blank=True, null=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "ownable_type",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("ownable_id", models.PositiveBigIntegerField(blank=True, null=True)),
                ("response_message", models.JSONField(blank=True, null=True)),
            ],
            options={
                "db_table": "webhooks",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ModelHasPermissions",
            fields=[
                (
                    "permission",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        primary_key=True,
                        serialize=False,
                        to="sagecloud.permissions",
                    ),
                ),
                ("model_type", models.CharField(max_length=255)),
                ("model_id", models.PositiveBigIntegerField()),
            ],
            options={
                "db_table": "model_has_permissions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="RoleHasPermissions",
            fields=[
                (
                    "permission",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        primary_key=True,
                        serialize=False,
                        to="sagecloud.permissions",
                    ),
                ),
            ],
            options={
                "db_table": "role_has_permissions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ModelHasRoles",
            fields=[
                (
                    "role",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        primary_key=True,
                        serialize=False,
                        to="sagecloud.roles",
                    ),
                ),
                ("model_type", models.CharField(max_length=255)),
                ("model_id", models.PositiveBigIntegerField()),
            ],
            options={
                "db_table": "model_has_roles",
                "managed": False,
            },
        ),
    ]

# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class ActionEvents(models.Model):
    id = models.BigAutoField(primary_key=True)
    batch_id = models.CharField(max_length=36)
    user_id = models.PositiveBigIntegerField()
    name = models.CharField(max_length=255)
    actionable_type = models.CharField(max_length=255)
    actionable_id = models.PositiveBigIntegerField()
    target_type = models.CharField(max_length=255)
    target_id = models.PositiveBigIntegerField()
    model_type = models.CharField(max_length=255)
    model_id = models.PositiveBigIntegerField(blank=True, null=True)
    fields = models.TextField()
    status = models.CharField(max_length=25)
    exception = models.TextField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    original = models.TextField(blank=True, null=True)
    changes = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "action_events"


class Activities(models.Model):
    id = models.BigAutoField(primary_key=True)
    user_id = models.IntegerField()
    info = models.TextField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "activities"


class ApiLogs(models.Model):
    id = models.BigAutoField(primary_key=True)
    user_id = models.CharField(max_length=255, blank=True, null=True)
    url = models.CharField(max_length=255, blank=True, null=True)
    method = models.CharField(max_length=255, blank=True, null=True)
    client_ip = models.CharField(max_length=255, blank=True, null=True)
    duration = models.DecimalField(
        max_digits=8, decimal_places=2, blank=True, null=True
    )
    request_body = models.TextField(blank=True, null=True)
    response_code = models.IntegerField(blank=True, null=True)
    response_body = models.TextField(blank=True, null=True)
    provider = models.CharField(max_length=255, blank=True, null=True)
    provider_request_body = models.TextField(blank=True, null=True)
    provider_response_code = models.IntegerField(blank=True, null=True)
    provider_response_body = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    transaction_id = models.PositiveBigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "api_logs"


class ApiLogsV2(models.Model):
    id = models.BigAutoField(primary_key=True)
    client_ip = models.CharField(max_length=45)
    method = models.CharField(max_length=255)
    url = models.CharField(max_length=255)
    start_time = models.BigIntegerField()
    merchant_request_body = models.JSONField()
    provider = models.ForeignKey("Providers", models.DO_NOTHING, blank=True, null=True)
    transaction = models.ForeignKey(
        "Transactions", models.DO_NOTHING, blank=True, null=True
    )
    provider_request_body = models.JSONField(blank=True, null=True)
    finish_time = models.BigIntegerField(blank=True, null=True)
    merchant_response_body = models.JSONField(blank=True, null=True)
    merchant_response_code = models.IntegerField(blank=True, null=True)
    provider_response_body = models.JSONField(blank=True, null=True)
    provider_response_code = models.IntegerField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "api_logs_v2"


class BankAccountTransaction(models.Model):
    bank_account_id = models.PositiveBigIntegerField()
    transaction_id = models.PositiveBigIntegerField()

    class Meta:
        managed = False
        db_table = "bank_account_transaction"


class BankAccounts(models.Model):
    id = models.BigAutoField(primary_key=True)
    bank_code = models.CharField(max_length=255, blank=True, null=True)
    bank_name = models.CharField(max_length=255, blank=True, null=True)
    currency_code = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)
    provider = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=255, blank=True, null=True)
    account_email = models.CharField(max_length=255, blank=True, null=True)
    account_reference = models.CharField(max_length=255, blank=True, null=True)
    reservation_reference = models.CharField(max_length=255, blank=True, null=True)
    account_number = models.CharField(max_length=255)
    business_id = models.IntegerField(blank=True, null=True)
    third_party = models.IntegerField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    account_name = models.CharField(max_length=255, blank=True, null=True)
    payment_id = models.IntegerField(blank=True, null=True)
    static = models.IntegerField(blank=True, null=True)
    deleted_at = models.DateTimeField(blank=True, null=True)
    is_blocked = models.IntegerField()
    bvn = models.CharField(max_length=255, blank=True, null=True)
    block_reason = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "bank_accounts"


class Banks(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=255, blank=True, null=True)
    institution_code = models.CharField(max_length=255)
    category = models.IntegerField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    etranzact_code = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "banks"


class BeneficialOwners(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.IntegerField()
    name = models.CharField(max_length=255)
    email = models.CharField(max_length=255)
    phone = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "beneficial_owners"


class BillerItems(models.Model):
    id = models.BigAutoField(primary_key=True)
    biller_id = models.PositiveBigIntegerField()
    name = models.CharField(max_length=255, blank=True, null=True)
    amount = models.CharField(max_length=255, blank=True, null=True)
    code = models.CharField(max_length=255, blank=True, null=True)
    allowance = models.CharField(max_length=255, blank=True, null=True)
    duration = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "biller_items"


class Billers(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    biller_type = models.CharField(max_length=11)
    provider = models.CharField(max_length=255)
    biller_name = models.CharField(max_length=255, blank=True, null=True)
    category_name = models.CharField(max_length=255, blank=True, null=True)
    label1 = models.CharField(max_length=255, blank=True, null=True)
    narration = models.CharField(max_length=255, blank=True, null=True)
    short_name = models.CharField(max_length=255, blank=True, null=True)
    charge = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)
    logo_url = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "billers"


class BusinessKeys(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.BigIntegerField()
    public_key = models.CharField(max_length=255, blank=True, null=True)
    secret_key = models.CharField(max_length=255, blank=True, null=True)
    encryption_key = models.CharField(max_length=255, blank=True, null=True)
    iv = models.CharField(max_length=255, blank=True, null=True)
    callback_url = models.CharField(max_length=255, blank=True, null=True)
    webhook_url = models.CharField(max_length=255, blank=True, null=True)
    ip_whitelist = models.CharField(max_length=255, blank=True, null=True)
    ip_blacklist = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    vas_settings = models.JSONField(blank=True, null=True)
    payment_gateway_settings = models.JSONField(blank=True, null=True)
    virtual_account_settings = models.JSONField(blank=True, null=True)
    webhook_signature = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "business_keys"


class Businesses(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    info = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(unique=True, max_length=255, blank=True, null=True)
    email = models.CharField(unique=True, max_length=255)
    address = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=9)
    address_2 = models.CharField(max_length=255, blank=True, null=True)
    logo = models.CharField(max_length=255, blank=True, null=True)
    charge_back_email = models.CharField(
        unique=True, max_length=255, blank=True, null=True
    )
    website = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)
    state = models.CharField(max_length=255, blank=True, null=True)
    postal_code = models.CharField(max_length=255, blank=True, null=True)
    country = models.CharField(max_length=255)
    facebook = models.CharField(max_length=255, blank=True, null=True)
    twitter = models.CharField(max_length=255, blank=True, null=True)
    instagram = models.CharField(max_length=255, blank=True, null=True)
    linkedin = models.CharField(max_length=255, blank=True, null=True)
    youtube = models.CharField(max_length=255, blank=True, null=True)
    category = models.CharField(max_length=255, blank=True, null=True)
    size = models.IntegerField(blank=True, null=True)
    bvn = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=6, blank=True, null=True)
    rc_number = models.CharField(max_length=255, blank=True, null=True)
    rc_registration_document = models.CharField(max_length=255, blank=True, null=True)
    bn_number = models.CharField(max_length=255, blank=True, null=True)
    bn_registration_document = models.CharField(max_length=255, blank=True, null=True)
    cac_it_number = models.CharField(max_length=255, blank=True, null=True)
    cac_it_registration_document = models.CharField(
        max_length=255, blank=True, null=True
    )
    bank_account_name = models.CharField(max_length=255, blank=True, null=True)
    bank_name = models.CharField(max_length=255, blank=True, null=True)
    bank_account_number = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    certificate_of_incorporation = models.CharField(
        max_length=255, blank=True, null=True
    )
    articles_of_association = models.CharField(max_length=255, blank=True, null=True)
    cac_form = models.CharField(max_length=255, blank=True, null=True)
    utility_bill = models.CharField(max_length=255, blank=True, null=True)
    bank_code = models.CharField(max_length=255, blank=True, null=True)
    other_document = models.CharField(max_length=255, blank=True, null=True)
    alert_balance = models.CharField(max_length=255, blank=True, null=True)
    has_reserved_account = models.IntegerField()
    user_id = models.IntegerField(blank=True, null=True)
    lock_update = models.IntegerField()

    class Meta:
        managed = False
        db_table = "businesses"


class CableTvAddOns(models.Model):
    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=255)
    add_on_product_code = models.CharField(max_length=255)
    product_code = models.CharField(max_length=255)
    name = models.CharField(max_length=255, blank=True, null=True)
    price = models.CharField(max_length=255, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    month = models.IntegerField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "cable_tv_add_ons"


class CableTvBouquets(models.Model):
    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=9)
    code = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    description = models.CharField(max_length=255, blank=True, null=True)
    price = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "cable_tv_bouquets"


class CapricornDataPlans(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    service_type = models.CharField(max_length=255)
    allowance = models.CharField(max_length=255, blank=True, null=True)
    price = models.CharField(max_length=255)
    validity = models.CharField(max_length=255, blank=True, null=True)
    datacode = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "capricorn_data_plans"


class ChangeRequests(models.Model):
    id = models.BigAutoField(primary_key=True)
    requester_type = models.CharField(max_length=255)
    requester_id = models.PositiveBigIntegerField()
    entity_type = models.CharField(max_length=255, blank=True, null=True)
    entity_id = models.PositiveBigIntegerField(blank=True, null=True)
    service = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=255)
    description = models.CharField(max_length=255, blank=True, null=True)
    metadata = models.JSONField(blank=True, null=True)
    last_updated_by = models.PositiveBigIntegerField(blank=True, null=True)
    last_updated_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "change_requests"


class CommissionTransactions(models.Model):
    id = models.BigAutoField(primary_key=True)
    wallet_id = models.IntegerField()
    business_id = models.IntegerField()
    amount = models.FloatField()
    prev_balance = models.FloatField()
    new_balance = models.FloatField()
    currency = models.CharField(max_length=255)
    type = models.CharField(max_length=6, blank=True, null=True)
    product = models.CharField(max_length=255, blank=True, null=True)
    info = models.TextField()
    reference = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "commission_transactions"


class Commissions(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.IntegerField()
    product = models.CharField(max_length=255, blank=True, null=True)
    amount = models.FloatField()
    info = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "commissions"


class CorporateDataWallets(models.Model):
    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Businesses, models.DO_NOTHING)
    balance = models.DecimalField(max_digits=10, decimal_places=3)
    account_number = models.CharField(max_length=255, blank=True, null=True)
    commission = models.DecimalField(max_digits=10, decimal_places=3)
    status = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "corporate_data_wallets"


class DataPlans(models.Model):
    id = models.BigAutoField(primary_key=True)
    type = models.CharField(max_length=255)
    code = models.CharField(max_length=255)
    description = models.CharField(max_length=255)
    amount = models.CharField(max_length=255)
    price = models.CharField(max_length=255, blank=True, null=True)
    value = models.CharField(max_length=255)
    duration = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "data_plans"


class Directors(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.IntegerField()
    name = models.CharField(max_length=255)
    email = models.CharField(max_length=255)
    phone = models.CharField(max_length=255)
    bvn = models.CharField(max_length=255, blank=True, null=True)
    nin = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "directors"


class DisputeMessages(models.Model):
    id = models.BigAutoField(primary_key=True)
    dispute_id = models.IntegerField()
    admin_id = models.IntegerField(blank=True, null=True)
    text = models.TextField(blank=True, null=True)
    type = models.IntegerField()
    image_url = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "dispute_messages"


class Disputes(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.IntegerField()
    reference = models.CharField(max_length=255, blank=True, null=True)
    subject = models.CharField(max_length=255)
    status = models.IntegerField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "disputes"


class EducationPlans(models.Model):
    id = models.BigAutoField(primary_key=True)
    service = models.CharField(max_length=255)
    type = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=8, decimal_places=2)
    provider = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "education_plans"


class Epins(models.Model):
    id = models.BigAutoField(primary_key=True)
    transaction = models.ForeignKey("Transactions", models.DO_NOTHING)
    reference = models.CharField(max_length=255)
    amount = models.CharField(max_length=255)
    pin = models.CharField(max_length=255)
    serial = models.CharField(max_length=255)
    instruction = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "epins"


class FailedJobs(models.Model):
    id = models.BigAutoField(primary_key=True)
    uuid = models.CharField(unique=True, max_length=255, blank=True, null=True)
    connection = models.TextField()
    queue = models.TextField()
    payload = models.TextField()
    exception = models.TextField()
    failed_at = models.DateTimeField()

    class Meta:
        managed = False
        db_table = "failed_jobs"


class Fees(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.BigIntegerField()
    products = models.JSONField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "fees"


class GeneralFees(models.Model):
    id = models.BigAutoField(primary_key=True)
    last_updated_by = models.PositiveBigIntegerField()
    old_fee_settings = models.JSONField()
    new_fee_settings = models.JSONField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "general_fees"


class IswBanks(models.Model):
    id = models.BigAutoField(primary_key=True)
    b_id = models.CharField(max_length=255)
    cbn_code = models.CharField(max_length=255)
    bank_name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "isw_banks"


class JambPins(models.Model):
    id = models.BigAutoField(primary_key=True)
    transaction = models.ForeignKey("Transactions", models.DO_NOTHING)
    reference = models.CharField(max_length=255, blank=True, null=True)
    amount = models.CharField(max_length=255, blank=True, null=True)
    pin = models.CharField(max_length=255, blank=True, null=True)
    profile_code = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)
    instruction = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "jamb_pins"


class JobBatches(models.Model):
    id = models.CharField(primary_key=True, max_length=255)
    name = models.CharField(max_length=255)
    total_jobs = models.IntegerField()
    pending_jobs = models.IntegerField()
    failed_jobs = models.IntegerField()
    failed_job_ids = models.TextField()
    options = models.TextField(blank=True, null=True)
    cancelled_at = models.IntegerField(blank=True, null=True)
    created_at = models.IntegerField()
    finished_at = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "job_batches"


class Jobs(models.Model):
    id = models.BigAutoField(primary_key=True)
    queue = models.CharField(max_length=255)
    payload = models.TextField()
    attempts = models.PositiveIntegerField()
    reserved_at = models.PositiveIntegerField(blank=True, null=True)
    available_at = models.PositiveIntegerField()
    created_at = models.PositiveIntegerField()

    class Meta:
        managed = False
        db_table = "jobs"


class Kyc(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    slug = models.CharField(max_length=255)
    description = models.CharField(max_length=255)
    cost = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "kyc"


class Kycs(models.Model):
    id = models.BigAutoField(primary_key=True)
    level = models.CharField(max_length=255)
    daily_limit = models.FloatField()
    max_balance = models.FloatField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "kycs"


class MamaAfricaVendors(models.Model):
    id = models.BigAutoField(primary_key=True)
    value = models.IntegerField()
    product_id = models.IntegerField()
    vendor_id = models.IntegerField()
    denom_code = models.CharField(max_length=10)
    service = models.CharField(max_length=20)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "mama_africa_vendors"


class MandateTransaction(models.Model):
    mandate_id = models.PositiveBigIntegerField()
    transaction_id = models.PositiveBigIntegerField()

    class Meta:
        managed = False
        db_table = "mandate_transaction"


class Mandates(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.PositiveBigIntegerField()
    reference = models.CharField(max_length=255)
    mandate_code = models.CharField(max_length=255, blank=True, null=True)
    subscriber_code = models.CharField(max_length=255)
    account_number = models.CharField(max_length=255)
    account_name = models.CharField(max_length=255)
    bank_code = models.CharField(max_length=255)
    bank_name = models.CharField(max_length=255)
    status = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    customer_phone_number = models.CharField(max_length=255)
    customer_email = models.CharField(max_length=255)
    customer_address = models.CharField(max_length=255)
    bvn = models.CharField(max_length=255, blank=True, null=True)
    biller_id = models.CharField(max_length=255, blank=True, null=True)
    kyc = models.CharField(max_length=255, blank=True, null=True)
    frequency = models.CharField(max_length=255, blank=True, null=True)
    frequency_interval = models.BigIntegerField()
    meta_data = models.JSONField(blank=True, null=True)
    narration = models.CharField(max_length=255)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    debit_day = models.BigIntegerField(blank=True, null=True)
    debit_month = models.BigIntegerField(blank=True, null=True)
    last_processed_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "mandates"


class Migrations(models.Model):
    migration = models.CharField(max_length=255)
    batch = models.IntegerField()

    class Meta:
        managed = False
        db_table = "migrations"


class ModelHasPermissions(models.Model):
    permission = models.OneToOneField(
        "Permissions", models.DO_NOTHING, primary_key=True
    )
    model_type = models.CharField(max_length=255)
    model_id = models.PositiveBigIntegerField()

    class Meta:
        managed = False
        db_table = "model_has_permissions"
        unique_together = (("permission", "model_id", "model_type"),)


class ModelHasRoles(models.Model):
    role = models.OneToOneField("Roles", models.DO_NOTHING, primary_key=True)
    model_type = models.CharField(max_length=255)
    model_id = models.PositiveBigIntegerField()

    class Meta:
        managed = False
        db_table = "model_has_roles"
        unique_together = (("role", "model_id", "model_type"),)


class NovaFieldAttachments(models.Model):
    attachable_type = models.CharField(max_length=255)
    attachable_id = models.PositiveBigIntegerField()
    attachment = models.CharField(max_length=255)
    disk = models.CharField(max_length=255)
    url = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "nova_field_attachments"


class NovaNotifications(models.Model):
    id = models.CharField(primary_key=True, max_length=36)
    type = models.CharField(max_length=255)
    notifiable_type = models.CharField(max_length=255)
    notifiable_id = models.PositiveBigIntegerField()
    data = models.TextField()
    read_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    deleted_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "nova_notifications"


class NovaPendingFieldAttachments(models.Model):
    draft_id = models.CharField(max_length=255)
    attachment = models.CharField(max_length=255)
    disk = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "nova_pending_field_attachments"


class OauthAccessTokens(models.Model):
    id = models.CharField(primary_key=True, max_length=100)
    user_id = models.PositiveBigIntegerField(blank=True, null=True)
    client_id = models.PositiveBigIntegerField()
    name = models.CharField(max_length=255, blank=True, null=True)
    scopes = models.TextField(blank=True, null=True)
    revoked = models.IntegerField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    expires_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "oauth_access_tokens"


class OauthAuthCodes(models.Model):
    id = models.CharField(primary_key=True, max_length=100)
    user_id = models.PositiveBigIntegerField()
    client_id = models.PositiveBigIntegerField()
    scopes = models.TextField(blank=True, null=True)
    revoked = models.IntegerField()
    expires_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "oauth_auth_codes"


class OauthClients(models.Model):
    id = models.BigAutoField(primary_key=True)
    user_id = models.PositiveBigIntegerField(blank=True, null=True)
    name = models.CharField(max_length=255)
    secret = models.CharField(max_length=100, blank=True, null=True)
    provider = models.CharField(max_length=255, blank=True, null=True)
    redirect = models.TextField()
    personal_access_client = models.IntegerField()
    password_client = models.IntegerField()
    revoked = models.IntegerField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "oauth_clients"


class OauthPersonalAccessClients(models.Model):
    id = models.BigAutoField(primary_key=True)
    client_id = models.PositiveBigIntegerField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "oauth_personal_access_clients"


class OauthRefreshTokens(models.Model):
    id = models.CharField(primary_key=True, max_length=100)
    access_token_id = models.CharField(max_length=100)
    revoked = models.IntegerField()
    expires_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "oauth_refresh_tokens"


class Partners(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    slug = models.CharField(max_length=255, blank=True, null=True)
    test_credentials = models.JSONField(blank=True, null=True)
    credentials = models.JSONField(blank=True, null=True)
    status = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    charges = models.JSONField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "partners"


class PasswordResets(models.Model):
    email = models.CharField(max_length=255)
    token = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "password_resets"


class PaymentCards(models.Model):
    id = models.BigAutoField(primary_key=True)
    card_holder_name = models.CharField(max_length=255, blank=True, null=True)
    masked_pan = models.CharField(max_length=255, blank=True, null=True)
    expiry = models.CharField(max_length=255, blank=True, null=True)
    card_type = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=255, blank=True, null=True)
    issuer_bank_code = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "payment_cards"


class Permissions(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    guard_name = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "permissions"


class PowerTokens(models.Model):
    id = models.BigAutoField(primary_key=True)
    transaction = models.ForeignKey("Transactions", models.DO_NOTHING)
    reference = models.CharField(max_length=255)
    token = models.CharField(max_length=255)
    unit = models.CharField(max_length=255)
    amount = models.CharField(max_length=255)
    transid = models.CharField(
        db_column="transId", max_length=255
    )  # Field name made lowercase.
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "power_tokens"


class ProductFees(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.PositiveBigIntegerField()
    product_id = models.PositiveBigIntegerField()
    is_active = models.IntegerField()
    services = models.JSONField()
    user_id = models.PositiveBigIntegerField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "product_fees"


class Products(models.Model):
    id = models.BigAutoField(primary_key=True)
    slug = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    charge = models.FloatField()
    charge_type = models.CharField(max_length=10)
    vas_commission = models.FloatField()
    merchant_commission = models.FloatField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    charges = models.JSONField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "products"


class Providers(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    slug = models.CharField(max_length=255)
    product = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=8)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "providers"


class PulseAggregates(models.Model):
    id = models.BigAutoField(primary_key=True)
    bucket = models.PositiveIntegerField()
    period = models.PositiveIntegerField()
    type = models.CharField(max_length=255)
    key = models.TextField()
    key_hash = models.CharField(max_length=16, blank=True, null=True)
    aggregate = models.CharField(max_length=255)
    value = models.DecimalField(max_digits=20, decimal_places=2)
    count = models.PositiveIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "pulse_aggregates"
        unique_together = (("bucket", "period", "type", "aggregate", "key_hash"),)


class PulseEntries(models.Model):
    id = models.BigAutoField(primary_key=True)
    timestamp = models.PositiveIntegerField()
    type = models.CharField(max_length=255)
    key = models.TextField()
    key_hash = models.CharField(max_length=16, blank=True, null=True)
    value = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "pulse_entries"


class PulseValues(models.Model):
    id = models.BigAutoField(primary_key=True)
    timestamp = models.PositiveIntegerField()
    type = models.CharField(max_length=255)
    key = models.TextField()
    key_hash = models.CharField(max_length=16, blank=True, null=True)
    value = models.TextField()

    class Meta:
        managed = False
        db_table = "pulse_values"
        unique_together = (("type", "key_hash"),)


class ReQueries(models.Model):
    id = models.BigAutoField(primary_key=True)
    transaction = models.ForeignKey("Transactions", models.DO_NOTHING)
    provider = models.CharField(max_length=255)
    status = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "re_queries"


class RoleHasPermissions(models.Model):
    permission = models.OneToOneField(
        Permissions, models.DO_NOTHING, primary_key=True
    )  # The composite primary key (permission_id, role_id) found, that is not supported. The first column is selected.
    role = models.ForeignKey("Roles", models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = "role_has_permissions"
        unique_together = (("permission", "role"),)


class Roles(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    guard_name = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "roles"


class SMEDataWallets(models.Model):
    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Businesses, models.DO_NOTHING)
    balance = models.DecimalField(max_digits=10, decimal_places=3)
    account_number = models.CharField(max_length=255, blank=True, null=True)
    commission = models.DecimalField(max_digits=10, decimal_places=3)
    status = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "s_m_e_data_wallets"


class SagePaySettings(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.BigIntegerField()
    callback_url = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "sage_pay_settings"


class SagePayTransactions(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.PositiveBigIntegerField()
    access_code = models.CharField(max_length=255)
    session_id = models.CharField(max_length=255)
    aes_256_key = models.CharField(max_length=255, blank=True, null=True)
    version = models.CharField(max_length=255, blank=True, null=True)
    authentication_limit = models.CharField(max_length=255, blank=True, null=True)
    ip_address = models.CharField(max_length=255)
    browser = models.CharField(max_length=255, blank=True, null=True)
    browser_details = models.JSONField(blank=True, null=True)
    auth_status = models.CharField(max_length=255, blank=True, null=True)
    auth_html = models.TextField(blank=True, null=True)
    pay_reference = models.CharField(max_length=255, blank=True, null=True)
    display_success = models.IntegerField()
    callback_url = models.CharField(max_length=255, blank=True, null=True)
    customer_email = models.CharField(max_length=255)
    customer_phone = models.CharField(max_length=255, blank=True, null=True)
    amount = models.FloatField()
    net_amount = models.FloatField()
    charge = models.FloatField()
    info = models.TextField()
    status = models.CharField(max_length=255)
    channel = models.CharField(max_length=255)
    reference = models.CharField(max_length=255)
    external_reference = models.CharField(max_length=255)
    wallet_debited = models.IntegerField(blank=True, null=True)
    wallet_credited = models.IntegerField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    payment_card_id = models.CharField(max_length=255, blank=True, null=True)
    payment_methods = models.CharField(max_length=255, blank=True, null=True)
    merchant_bears_charge = models.IntegerField()
    metadata = models.JSONField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "sage_pay_transactions"


class SagePayWalletTransactions(models.Model):
    id = models.BigAutoField(primary_key=True)
    sage_pay_wallet_id = models.IntegerField()
    business_id = models.IntegerField()
    amount = models.FloatField()
    prev_balance = models.FloatField()
    new_balance = models.FloatField()
    type = models.CharField(max_length=6)
    info = models.TextField()
    reference = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "sage_pay_wallet_transactions"


class SagePayWallets(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.BigIntegerField()
    account_number = models.CharField(max_length=10)
    balance = models.DecimalField(max_digits=15, decimal_places=2)
    commission = models.DecimalField(max_digits=15, decimal_places=2)
    status = models.CharField(max_length=9)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "sage_pay_wallets"


class ServiceCodes(models.Model):
    id = models.BigAutoField(primary_key=True)
    provider = models.CharField(max_length=255)
    code = models.CharField(max_length=255)
    alias = models.CharField(max_length=255)
    description = models.CharField(max_length=255)
    type = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "service_codes"


class ServiceFees(models.Model):
    id = models.BigAutoField(primary_key=True)
    business = models.ForeignKey(Businesses, models.DO_NOTHING)
    service = models.ForeignKey("Services", models.DO_NOTHING)
    type = models.CharField(max_length=255)
    is_active = models.IntegerField()
    charge = models.DecimalField(max_digits=8, decimal_places=2)
    commission = models.DecimalField(max_digits=8, decimal_places=2)
    vas_commission = models.DecimalField(max_digits=8, decimal_places=2)
    is_flat_charge = models.IntegerField()
    flat_charge = models.DecimalField(max_digits=8, decimal_places=2)
    range = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "service_fees"


class Services(models.Model):
    id = models.BigAutoField(primary_key=True)
    product = models.ForeignKey(Products, models.DO_NOTHING, blank=True, null=True)
    name = models.CharField(max_length=255)
    slug = models.CharField(max_length=255)
    providers = models.CharField(max_length=255)
    active_provider = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "services"


class ShagoDataPlans(models.Model):
    id = models.BigAutoField(primary_key=True)
    data_plan_id = models.PositiveBigIntegerField()
    price = models.CharField(max_length=20)
    code = models.CharField(max_length=20)
    allowance = models.CharField(max_length=255)
    validity = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    network = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = "shago_data_plans"


class SmeDataPlans(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=50)
    service_code = models.CharField(max_length=20)
    grace_hub_code = models.CharField(max_length=20, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "sme_data_plans"


class SmileBundlePlans(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=255)
    allowance = models.CharField(max_length=255, blank=True, null=True)
    price = models.CharField(max_length=255)
    validity = models.CharField(max_length=255, blank=True, null=True)
    datacode = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "smile_bundle_plans"


class SoniteV2DataPlans(models.Model):
    id = models.BigAutoField(primary_key=True)
    data_plan_id = models.PositiveBigIntegerField()
    network = models.CharField(max_length=255)
    code = models.CharField(max_length=255)
    description = models.CharField(max_length=255)
    amount = models.CharField(max_length=255)
    value = models.CharField(max_length=255)
    duration = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "sonite_v2_data_plans"


class TelescopeEntries(models.Model):
    sequence = models.BigAutoField(primary_key=True)
    uuid = models.CharField(unique=True, max_length=36)
    batch_id = models.CharField(max_length=36)
    family_hash = models.CharField(max_length=255, blank=True, null=True)
    should_display_on_index = models.IntegerField()
    type = models.CharField(max_length=20)
    content = models.TextField()
    created_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "telescope_entries"


class TelescopeEntriesTags(models.Model):
    entry_uuid = models.ForeignKey(
        TelescopeEntries, models.DO_NOTHING, db_column="entry_uuid", to_field="uuid"
    )
    tag = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = "telescope_entries_tags"


class TelescopeMonitoring(models.Model):
    tag = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = "telescope_monitoring"


class TransactionTransfers(models.Model):
    id = models.BigAutoField(primary_key=True)
    transaction_id = models.PositiveBigIntegerField()
    sessionid = models.CharField(max_length=255)
    provider_transaction_id = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "transaction_transfers"


class Transactions(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.PositiveBigIntegerField()
    amount = models.FloatField()
    net_amount = models.FloatField()
    charge = models.FloatField()
    status = models.CharField(max_length=255, blank=True, null=True)
    channel = models.CharField(max_length=255)
    reference = models.CharField(max_length=255)
    external_reference = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)
    sub_type = models.CharField(max_length=255, blank=True, null=True)
    wallet_debited = models.IntegerField(blank=True, null=True)
    provider_id = models.PositiveBigIntegerField(blank=True, null=True)
    wallet_credited = models.IntegerField(blank=True, null=True)
    info = models.TextField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    model_id = models.PositiveBigIntegerField(blank=True, null=True)
    margin = models.FloatField()
    model_type = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "transactions"


class UserAuthLogs(models.Model):
    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey("Users", models.DO_NOTHING, blank=True, null=True)
    email = models.CharField(max_length=255, blank=True, null=True)
    ip_address = models.CharField(max_length=45)
    log_type = models.CharField(max_length=255)
    user_agent = models.CharField(max_length=255, blank=True, null=True)
    info = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "user_auth_logs"


class Users(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.BigIntegerField()
    firstname = models.CharField(max_length=255)
    lastname = models.CharField(max_length=255)
    email = models.CharField(unique=True, max_length=255)
    phone = models.CharField(max_length=255, blank=True, null=True)
    dob = models.DateField(blank=True, null=True)
    password = models.CharField(max_length=255)
    two_factor_secret = models.TextField(blank=True, null=True)
    two_factor_recovery_codes = models.TextField(blank=True, null=True)
    two_factor_confirmed_at = models.DateTimeField(blank=True, null=True)
    remember_token = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "users"


class VirtualAccountInventories(models.Model):
    id = models.BigAutoField(primary_key=True)
    account_number = models.CharField(unique=True, max_length=255)
    provider = models.CharField(max_length=255)
    is_used = models.IntegerField()
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    next_available_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "virtual_account_inventories"


class VirtualAccountSettlements(models.Model):
    id = models.BigAutoField(primary_key=True)
    wallet = models.ForeignKey("Wallets", models.DO_NOTHING)
    business = models.ForeignKey(Businesses, models.DO_NOTHING)
    account_number = models.CharField(max_length=255, blank=True, null=True)
    wallet_balance = models.DecimalField(max_digits=14, decimal_places=2)
    bank_code = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=255, blank=True, null=True)
    message = models.CharField(max_length=255, blank=True, null=True)
    reference = models.CharField(max_length=255, blank=True, null=True)
    settled_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "virtual_account_settlements"


class WaecPins(models.Model):
    id = models.BigAutoField(primary_key=True)
    transaction = models.ForeignKey(Transactions, models.DO_NOTHING)
    reference = models.CharField(max_length=255, blank=True, null=True)
    amount = models.CharField(max_length=255, blank=True, null=True)
    pin = models.CharField(max_length=255, blank=True, null=True)
    serial = models.CharField(max_length=255, blank=True, null=True)
    instruction = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "waec_pins"


class WalletTopUpRequests(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.IntegerField()
    amount = models.FloatField()
    name = models.CharField(max_length=255)
    info = models.TextField()
    status = models.CharField(max_length=8, blank=True, null=True)
    image_url = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    wallet_type = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = "wallet_top_up_requests"


class WalletTransactions(models.Model):
    id = models.BigAutoField(primary_key=True)
    wallet_id = models.IntegerField()
    business_id = models.IntegerField()
    amount = models.FloatField()
    prev_balance = models.FloatField()
    new_balance = models.FloatField()
    type = models.CharField(max_length=6)
    info = models.TextField()
    reference = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    wallet_type = models.CharField(max_length=255)
    transaction_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "wallet_transactions"


class Wallets(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.BigIntegerField()
    is_gl = models.IntegerField()
    can_be_negative = models.IntegerField()
    account_number = models.CharField(max_length=10)
    balance = models.DecimalField(max_digits=15, decimal_places=2)
    commission = models.FloatField()
    status = models.CharField(max_length=9)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = "wallets"


class Webhooks(models.Model):
    id = models.BigAutoField(primary_key=True)
    business_id = models.PositiveBigIntegerField()
    payload = models.JSONField()
    url = models.CharField(max_length=255)
    tries = models.IntegerField()
    response_code = models.IntegerField()
    type = models.CharField(max_length=255)
    owner = models.CharField(max_length=255)
    created_at = models.DateTimeField(blank=True, null=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    ownable_type = models.CharField(max_length=255, blank=True, null=True)
    ownable_id = models.PositiveBigIntegerField(blank=True, null=True)
    response_message = models.JSONField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = "webhooks"

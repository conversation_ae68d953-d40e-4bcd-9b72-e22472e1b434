<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Wallet Adjustment Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .adjustment-details {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            color: {% if adjustment_type == 'CREDIT' %}#28a745{% else %}#dc3545{% endif %};
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Wallet Adjustment Notification</h2>
    </div>

    <p>Hello {{ business_owner_name }},</p>

    <p>We're writing to inform you that a manual adjustment has been made to your {{ business_name }} wallet.</p>

    <div class="adjustment-details">
        <h3>Adjustment Details:</h3>
        <p><strong>Type:</strong> {{ adjustment_type }}</p>
        <p><strong>Amount:</strong> <span class="amount">₦{{ amount|floatformat:2 }}</span></p>
        <p><strong>Wallet:</strong> {{ wallet_type }}</p>
        <p><strong>Date:</strong> {{ adjustment_date }}</p>
        <p><strong>Reference:</strong> {{ transaction_reference }}</p>
    </div>

    <h3>Reason for Adjustment:</h3>
    <p>{{ reason }}</p>

    <h3>Background:</h3>
    <p>This adjustment was made to correct a wallet balance discrepancy related to transaction <strong>{{ original_transaction_reference }}</strong>.
    {% if adjustment_type == 'CREDIT' %}
    Your wallet has been credited with the amount that was not properly processed during the original transaction.
    {% else %}
    Your wallet has been debited to correct an overpayment that occurred during the original transaction.
    {% endif %}
    </p>

    <p>If you have any questions about this adjustment, please don't hesitate to contact our support team.</p>

    <div class="footer">
        <p>Best regards,<br>
        The SageCloud Team</p>

        <p><em>This is an automated notification. Please do not reply to this email.</em></p>
    </div>
</body>
</html>

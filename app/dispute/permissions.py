"""
Dispute permissions using teams app permission system.

This module provides permission classes for dispute-related operations
that integrate with the teams app's Role and Permission models.
"""

from rest_framework import permissions
from teams.permissions import (
    CanCreateDispute,
    CanReadDispute,
    CanUpdateDispute,
    HasAdminPermission,
    HasBusinessPermission,
    check_user_permission,
    get_user_admin_context,
    get_user_business_context,
)

# Re-export teams app permission classes for backward compatibility
__all__ = [
    "CanCreateDispute",
    "CanReadDispute",
    "CanUpdateDispute",
    "HasBusinessPermission",
    "HasAdminPermission",
    "get_user_business_context",
    "get_user_admin_context",
    "check_user_permission",
    "IsBusinessOwner",
    "CanReadAllBusinessDisputes",
    "check_dispute_permission",
]


class IsBusinessOwner(permissions.BasePermission):
    """Allows access only to business owners."""

    message = "Only business owners are authorized to perform this action."

    def has_permission(self, request, view):
        return bool(
            request.user
            and request.user.is_authenticated
            and request.user.role == "Business_Owner"
        )


class CanReadAllBusinessDisputes(HasBusinessPermission):
    """Permission to read all disputes in the business."""

    def __init__(self):
        super().__init__("dispute:READ")


def check_dispute_permission(user, permission_codename, dispute=None):
    """
    Check if user has specific dispute permission.

    Args:
        user: User instance
        permission_codename: Permission codename (e.g., "dispute:CREATE")
        dispute: Dispute instance (optional, for object-level checks)

    Returns:
        bool: True if user has the permission, False otherwise
    """
    # For dispute object-level checks, ensure user belongs to the same business
    business = None
    if dispute:
        business = dispute.business

    return check_user_permission(user, permission_codename, business)

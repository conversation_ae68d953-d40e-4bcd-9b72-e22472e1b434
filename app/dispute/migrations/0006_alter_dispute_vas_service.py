# Generated by Django 5.1.7 on 2025-07-20 13:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dispute", "0005_remove_old_models"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="dispute",
            name="vas_service",
            field=models.CharField(
                blank=True,
                choices=[
                    ("VIRTUAL_ACCOUNT", "VIRTUAL_ACCOUNT"),
                    ("T<PERSON><PERSON>FER", "TRANSFER"),
                    ("AIRTIME", "AIRTIME"),
                    ("DATA", "DATA"),
                    ("BETTING", "BETTING"),
                    ("ELECTRICITY", "ELECTRICITY"),
                    ("CABLE_TV", "CABLE_TV"),
                    ("SME_DATA", "SME_DATA"),
                    ("KYC", "KYC"),
                    ("EDUCATION", "EDUCATION"),
                    ("EPIN", "EPIN"),
                    ("RECURRING_DEBIT", "RECURRING_DEBIT"),
                    ("MANUAL_CREDIT", "MANUAL_CREDIT"),
                    ("MANUAL_DEBIT", "MANUAL_DEBIT"),
                ],
                db_index=True,
                help_text="VAS service type (e.g., Airtime, Electricity)",
                max_length=30,
                null=True,
            ),
        ),
    ]

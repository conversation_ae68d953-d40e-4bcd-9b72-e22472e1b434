# Generated migration for migrating dispute app to use teams app models

from django.db import migrations


def migrate_business_members_to_teams(apps, schema_editor):
    """
    Migrate existing BusinessMember instances to teams app TeamMember model.
    """
    # Get models from the current migration state
    BusinessMember = apps.get_model("dispute", "BusinessMember")
    TeamMember = apps.get_model("teams", "TeamMember")
    Role = apps.get_model("teams", "Role")

    # Role mapping from dispute app roles to teams app roles
    role_mapping = {
        "BUSINESS_OWNER": "Business_Owner",
        "MERCHANT_ADMIN": "Admin",
        "CUSTOMER_SUPPORT": "Customer_Support",
        "OPERATIONS": "Operations",
        "RECONCILIATION": "Reconciliation",
        "DEVELOPER": "Developer",
    }

    migrated_count = 0
    skipped_count = 0

    for business_member in BusinessMember.objects.all():
        try:
            # Map the role
            teams_role_name = role_mapping.get(business_member.role)
            if not teams_role_name:
                print(
                    f"Warning: Unknown role '{business_member.role}' for user {business_member.user.email}"
                )
                skipped_count += 1
                continue

            # Skip business owners as they don't need team membership
            if teams_role_name == "Business_Owner":
                skipped_count += 1
                continue

            # Find the corresponding role in teams app
            try:
                role = Role.objects.get(
                    name=teams_role_name,
                    platform="Business",
                    business=business_member.business,
                )
            except Role.DoesNotExist:
                # Try to find a default role
                try:
                    role = Role.objects.get(
                        name=teams_role_name, platform="Business", business__isnull=True
                    )
                except Role.DoesNotExist:
                    print(
                        f"Warning: Role '{teams_role_name}' not found for business {business_member.business.name}"
                    )
                    skipped_count += 1
                    continue

            # Check if TeamMember already exists
            if TeamMember.objects.filter(user=business_member.user).exists():
                print(
                    f"TeamMember already exists for user {business_member.user.email}"
                )
                skipped_count += 1
                continue

            # Create TeamMember
            TeamMember.objects.create(
                user=business_member.user,
                business=business_member.business,
                role=role,
                status="Active" if business_member.is_active else "Deactivated",
                invited_by=business_member.added_by,
                joined_at=business_member.created_at,
            )

            # Update the user's role to Team_Member
            business_member.user.role = "Team_Member"
            business_member.user.save()

            migrated_count += 1
            print(
                f"Migrated business member: {business_member.user.email} -> {teams_role_name}"
            )

        except Exception as e:
            print(
                f"Error migrating business member {business_member.user.email}: {str(e)}"
            )
            skipped_count += 1

    print(f"Migration completed: {migrated_count} migrated, {skipped_count} skipped")


def migrate_admin_members_to_teams(apps, schema_editor):
    """
    Migrate existing AdminTeamMember instances to teams app AdminProfile model.
    """
    # Get models from the current migration state
    AdminTeamMember = apps.get_model("dispute", "AdminTeamMember")
    AdminProfile = apps.get_model("teams", "AdminProfile")
    Role = apps.get_model("teams", "Role")

    # Role mapping from dispute app roles to teams app roles
    role_mapping = {
        "ADMIN": "Admin",
        "CUSTOMER_SUPPORT": "Customer_Support",
        "OPERATIONS": "Operations",
        "RECONCILIATION": "Reconciliation",
        "DEVELOPER": "Developer",
    }

    migrated_count = 0
    skipped_count = 0

    for admin_member in AdminTeamMember.objects.all():
        try:
            # Map the role
            teams_role_name = role_mapping.get(admin_member.role)
            if not teams_role_name:
                print(
                    f"Warning: Unknown admin role '{admin_member.role}' for user {admin_member.user.email}"
                )
                skipped_count += 1
                continue

            # Find the corresponding role in teams app
            try:
                role = Role.objects.get(
                    name=teams_role_name, platform="Admin", business__isnull=True
                )
            except Role.DoesNotExist:
                print(f"Warning: Admin role '{teams_role_name}' not found")
                skipped_count += 1
                continue

            # Check if AdminProfile already exists
            if AdminProfile.objects.filter(user=admin_member.user).exists():
                print(f"AdminProfile already exists for user {admin_member.user.email}")
                skipped_count += 1
                continue

            # Create AdminProfile
            AdminProfile.objects.create(
                user=admin_member.user,
                role=role,
                status="Active" if admin_member.is_active else "Deactivated",
                invited_by=admin_member.added_by,
                joined_at=admin_member.created_at,
            )

            # Update the user's role to Admin
            admin_member.user.role = "Admin"
            admin_member.user.save()

            migrated_count += 1
            print(
                f"Migrated admin member: {admin_member.user.email} -> {teams_role_name}"
            )

        except Exception as e:
            print(f"Error migrating admin member {admin_member.user.email}: {str(e)}")
            skipped_count += 1

    print(
        f"Admin migration completed: {migrated_count} migrated, {skipped_count} skipped"
    )


def reverse_migration(apps, schema_editor):
    """
    Reverse the migration by recreating BusinessMember and AdminTeamMember records.
    Note: This is a simplified reverse operation and may not perfectly restore all data.
    """
    print(
        "Warning: Reverse migration is not fully implemented. Manual data restoration may be required."
    )


class Migration(migrations.Migration):

    dependencies = [
        ("dispute", "0003_make_dispute_response_message_nullable"),
        (
            "teams",
            "0004_adminprofile_invited_by_adminprofile_joined_at_and_more",
        ),  # Ensure teams app migrations are applied
        ("user", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(
            migrate_business_members_to_teams,
            reverse_migration,
        ),
        migrations.RunPython(
            migrate_admin_members_to_teams,
            reverse_migration,
        ),
    ]

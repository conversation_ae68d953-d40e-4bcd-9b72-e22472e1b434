from decimal import Decimal

import pytest
from business.tests.fixtures import create_business, create_test_user
from django.db import IntegrityError
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from teams.enums import SystemPlatform, SystemRoleType, TeamMemberStatus
from teams.models import Permission, Role, TeamMember
from transaction.models import Transaction
from wallet.enums import WalletEnums
from wallet.models import Wallet

from .enums import DisputeStatus
from .models import Dispute


def create_business_role_with_permissions(role_name, business, permissions=None):
    """Create a business role with specific permissions."""
    if permissions is None:
        permissions = ["dispute:CREATE", "dispute:READ"]

    role = Role.objects.create(
        name=role_name,
        platform=SystemPlatform.Business.value,
        business=business,
        type=SystemRoleType.Custom.value,
        description=f"Test role: {role_name}",
    )

    # Create permissions if they don't exist
    for perm_codename in permissions:
        feature, action = perm_codename.split(":")
        permission, _ = Permission.objects.get_or_create(
            codename=perm_codename,
            defaults={"feature": feature, "action": action.lower()},
        )
        role.permissions.add(permission)

    return role


def create_team_member_with_role(user, business, role_name, permissions=None):
    """Create a team member with a specific role and permissions."""
    role = create_business_role_with_permissions(role_name, business, permissions)

    # Update user role to Team_Member
    user.role = "Team_Member"
    user.save()

    team_member = TeamMember.objects.create(
        user=user,
        business=business,
        role=role,
        status=TeamMemberStatus.Active.value,
        invited_by=business.owner,
    )

    return team_member


@pytest.mark.django_db
class TestDisputeDuplicatePrevention:
    """Test cases for preventing duplicate disputes for the same transaction"""

    @pytest.fixture
    def api_client(self):
        """Return an API client for testing"""
        return APIClient()

    @pytest.fixture
    def authenticated_client(self, api_client):
        """Return an authenticated API client with user and business"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)
        create_team_member_with_role(
            user, business, "Operations", ["dispute:CREATE", "dispute:READ"]
        )

        api_client.force_authenticate(user=user)
        return api_client, user, business

    @pytest.fixture
    def sample_transaction(self, authenticated_client):
        """Create a sample transaction for testing"""
        _, _, business = authenticated_client

        # Create a wallet for the business
        wallet = Wallet.objects.create(
            business=business, type=WalletEnums.GENERAL, balance=Decimal("5000.00")
        )

        # Create a transaction for testing
        transaction = Transaction.objects.create(
            wallet=wallet,
            business=business,
            reference="TXN-TEST-001",
            merchant_reference="MERCH-TEST-001",
            amount=Decimal("1000.00"),
            charge=Decimal("50.00"),
            old_balance=Decimal("5000.00"),
            new_balance=Decimal("3950.00"),
            txn_class="AIRTIME",
            status="SUCCESS",
            mode="API",
            type="DEBIT",
            narration="Test transaction for dispute",
        )
        return transaction

    @pytest.fixture
    def sample_dispute_data(self, sample_transaction):
        """Return sample dispute data"""
        return {
            "transaction_reference": sample_transaction.reference,
            "vas_service": sample_transaction.txn_class,
            "amount": sample_transaction.amount,
            "charge": sample_transaction.charge,
            "stamp_duty": Decimal("0.00"),
            "previous_balance": sample_transaction.old_balance,
            "new_balance": sample_transaction.new_balance,
            "transaction_date": sample_transaction.created_at.isoformat(),
            "merchant_name": sample_transaction.business.name,
            "message": "This is a test dispute message for duplicate prevention testing.",
        }

    def test_create_first_dispute_success(
        self, authenticated_client, sample_dispute_data
    ):
        """Test that the first dispute for a transaction can be created successfully"""
        api_client, user, business = authenticated_client

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, sample_dispute_data, format="json")

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["success"] is True
        assert "Dispute created successfully" in response.data["message"]

        # Verify dispute was created in database
        dispute = Dispute.objects.get(
            business=business,
            transaction_reference=sample_dispute_data["transaction_reference"],
        )
        assert dispute.status == DisputeStatus.PENDING.value
        assert dispute.created_by == user

    def test_create_duplicate_dispute_fails_validation(
        self, authenticated_client, sample_dispute_data
    ):
        """Test that creating a duplicate dispute fails at serializer validation level"""
        api_client, user, business = authenticated_client

        # Create first dispute
        url = reverse("dispute:dispute-list")
        first_response = api_client.post(url, sample_dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        # Attempt to create duplicate dispute
        second_response = api_client.post(url, sample_dispute_data, format="json")

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        assert "transaction_reference" in second_response.data
        assert "A dispute already exists for transaction" in str(
            second_response.data["transaction_reference"]
        )

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business,
            transaction_reference=sample_dispute_data["transaction_reference"],
        ).count()
        assert dispute_count == 1

    def test_create_duplicate_dispute_from_reference_fails(
        self, authenticated_client, sample_transaction
    ):
        """Test that creating duplicate dispute using create-from-reference endpoint fails"""
        api_client, _, business = authenticated_client

        # Create first dispute using create-from-reference endpoint
        url = reverse("dispute:dispute-create-from-reference")
        dispute_data = {
            "transaction_reference": sample_transaction.reference,
            "message": "This is a test dispute message for duplicate prevention testing.",
        }

        first_response = api_client.post(url, dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        # Attempt to create duplicate dispute using same endpoint
        second_response = api_client.post(url, dispute_data, format="json")

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        assert "transaction_reference" in second_response.data
        assert "A dispute already exists for transaction" in str(
            second_response.data["transaction_reference"]
        )

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business, transaction_reference=sample_transaction.reference
        ).count()
        assert dispute_count == 1

    def test_database_constraint_prevents_duplicate(
        self, authenticated_client, sample_transaction
    ):
        """Test that database constraint prevents duplicate disputes"""
        _, user, business = authenticated_client

        # Create first dispute directly in database
        dispute_data = {
            "business": business,
            "created_by": user,
            "transaction_reference": sample_transaction.reference,
            "vas_service": sample_transaction.txn_class,
            "amount": sample_transaction.amount,
            "charge": sample_transaction.charge,
            "stamp_duty": Decimal("0.00"),
            "previous_balance": sample_transaction.old_balance,
            "new_balance": sample_transaction.new_balance,
            "transaction_date": sample_transaction.created_at,
            "merchant_name": sample_transaction.business.name,
            "message": "First dispute",
            "status": DisputeStatus.PENDING.value,
        }

        first_dispute = Dispute.objects.create(**dispute_data)
        assert first_dispute.id is not None

        # Attempt to create duplicate dispute directly in database
        dispute_data["message"] = "Second dispute (should fail)"

        with pytest.raises(IntegrityError):
            Dispute.objects.create(**dispute_data)

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business, transaction_reference=sample_transaction.reference
        ).count()
        assert dispute_count == 1

    def test_different_businesses_can_create_disputes_for_same_reference(
        self, api_client
    ):
        """Test that different businesses can create disputes for the same transaction reference"""
        # Create first business and user
        user1 = create_test_user(email="<EMAIL>")
        business1 = create_business(owner=user1, name="Business 1")
        create_team_member_with_role(
            user1, business1, "Operations", ["dispute:CREATE", "dispute:READ"]
        )

        # Create second business and user
        user2 = create_test_user(email="<EMAIL>")
        business2 = create_business(owner=user2, name="Business 2")
        create_team_member_with_role(
            user2, business2, "Operations", ["dispute:CREATE", "dispute:READ"]
        )

        # Same transaction reference (could happen in real world)
        transaction_reference = "TXN-SHARED-001"

        # Create dispute for first business
        api_client.force_authenticate(user=user1)
        dispute_data1 = {
            "transaction_reference": transaction_reference,
            "vas_service": "AIRTIME",
            "amount": Decimal("1000.00"),
            "charge": Decimal("50.00"),
            "stamp_duty": Decimal("0.00"),
            "previous_balance": Decimal("5000.00"),
            "new_balance": Decimal("3950.00"),
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": business1.name,
            "message": "Dispute from business 1",
        }

        url = reverse("dispute:dispute-list")
        response1 = api_client.post(url, dispute_data1, format="json")
        assert response1.status_code == status.HTTP_201_CREATED

        # Create dispute for second business with same transaction reference
        api_client.force_authenticate(user=user2)
        dispute_data2 = {
            "transaction_reference": transaction_reference,
            "vas_service": "ELECTRICITY",
            "amount": Decimal("2000.00"),
            "charge": Decimal("100.00"),
            "stamp_duty": Decimal("50.00"),
            "previous_balance": Decimal("10000.00"),
            "new_balance": Decimal("7850.00"),
            "transaction_date": "2024-01-01T11:00:00Z",
            "merchant_name": business2.name,
            "message": "Dispute from business 2",
        }

        response2 = api_client.post(url, dispute_data2, format="json")
        assert response2.status_code == status.HTTP_201_CREATED

        # Verify both disputes exist
        dispute1 = Dispute.objects.get(
            business=business1, transaction_reference=transaction_reference
        )
        dispute2 = Dispute.objects.get(
            business=business2, transaction_reference=transaction_reference
        )

        assert dispute1.id != dispute2.id
        assert dispute1.business != dispute2.business
        assert dispute1.message != dispute2.message

    def test_error_message_includes_existing_dispute_details(
        self, authenticated_client, sample_dispute_data
    ):
        """Test that error message includes details about the existing dispute"""
        api_client, _, _ = authenticated_client

        # Create first dispute
        url = reverse("dispute:dispute-list")
        first_response = api_client.post(url, sample_dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        created_dispute = Dispute.objects.get(
            transaction_reference=sample_dispute_data["transaction_reference"]
        )

        # Attempt to create duplicate dispute
        second_response = api_client.post(url, sample_dispute_data, format="json")

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        error_message = str(second_response.data["transaction_reference"])

        # Verify error message contains dispute ID and status
        assert created_dispute.id in error_message
        assert created_dispute.status in error_message
        assert sample_dispute_data["transaction_reference"] in error_message

    def test_mixed_endpoint_duplicate_prevention(
        self, authenticated_client, sample_transaction
    ):
        """Test that duplicate prevention works across different endpoints"""
        api_client, _, business = authenticated_client

        # Create dispute using full endpoint
        full_dispute_data = {
            "transaction_reference": sample_transaction.reference,
            "vas_service": sample_transaction.txn_class,
            "amount": sample_transaction.amount,
            "charge": sample_transaction.charge,
            "stamp_duty": Decimal("0.00"),
            "previous_balance": sample_transaction.old_balance,
            "new_balance": sample_transaction.new_balance,
            "transaction_date": sample_transaction.created_at.isoformat(),
            "merchant_name": sample_transaction.business.name,
            "message": "First dispute via full endpoint",
        }

        url = reverse("dispute:dispute-list")
        first_response = api_client.post(url, full_dispute_data, format="json")
        assert first_response.status_code == status.HTTP_201_CREATED

        # Attempt to create duplicate using create-from-reference endpoint
        reference_dispute_data = {
            "transaction_reference": sample_transaction.reference,
            "message": "Second dispute via reference endpoint (should fail)",
        }

        reference_url = reverse("dispute:dispute-create-from-reference")
        second_response = api_client.post(
            reference_url, reference_dispute_data, format="json"
        )

        assert second_response.status_code == status.HTTP_400_BAD_REQUEST
        assert "A dispute already exists for transaction" in str(
            second_response.data["transaction_reference"]
        )

        # Verify only one dispute exists
        dispute_count = Dispute.objects.filter(
            business=business, transaction_reference=sample_transaction.reference
        ).count()
        assert dispute_count == 1


@pytest.mark.django_db
class TestDisputeBalanceCalculation:
    """Test cases for balance calculation validation in disputes"""

    @pytest.fixture
    def api_client(self):
        """Return an API client for testing"""
        return APIClient()

    @pytest.fixture
    def authenticated_client(self, api_client):
        """Return an authenticated API client with user and business"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        # Create a team member with dispute creation permissions
        create_team_member_with_role(
            user, business, "Operations", ["dispute:CREATE", "dispute:READ"]
        )

        api_client.force_authenticate(user=user)
        return api_client, user, business

    def test_debit_transaction_balance_calculation_valid(self, authenticated_client):
        """Test that DEBIT transaction balance calculation validation works correctly"""
        api_client, _, _ = authenticated_client

        # DEBIT transaction: new_balance = previous_balance - amount - charge - stamp_duty
        dispute_data = {
            "transaction_reference": "DEBIT-TXN-001",
            "vas_service": "AIRTIME",  # DEBIT transaction
            "amount": Decimal("1000.00"),
            "charge": Decimal("50.00"),
            "stamp_duty": Decimal("0.00"),
            "previous_balance": Decimal("5000.00"),
            "new_balance": Decimal("3950.00"),  # 5000 - 1000 - 50 - 0 = 3950
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": "Test Merchant",
            "message": "Test DEBIT transaction dispute",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["success"] is True

    def test_credit_transaction_balance_calculation_valid(self, authenticated_client):
        """Test that CREDIT transaction balance calculation validation works correctly"""
        api_client, _, _ = authenticated_client

        # CREDIT transaction: new_balance = previous_balance + amount + charge + stamp_duty
        dispute_data = {
            "transaction_reference": "CREDIT-TXN-001",
            "vas_service": "VIRTUAL_ACCOUNT",  # CREDIT transaction
            "amount": Decimal("9500.00"),
            "charge": Decimal("0.00"),
            "stamp_duty": Decimal("0.00"),
            "previous_balance": Decimal("9500.00"),
            "new_balance": Decimal("19000.00"),  # 9500 + 9500 + 0 + 0 = 19000
            "transaction_date": "2025-06-26T13:43:54.033508+01:00",
            "merchant_name": "Nill",
            "message": "TESTING DISPUTE - NO WHALA",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data["success"] is True

    def test_debit_transaction_balance_calculation_invalid(self, authenticated_client):
        """Test that invalid DEBIT transaction balance calculation is rejected"""
        api_client, _, _ = authenticated_client

        # DEBIT transaction with incorrect balance calculation
        dispute_data = {
            "transaction_reference": "DEBIT-TXN-INVALID",
            "vas_service": "AIRTIME",  # DEBIT transaction
            "amount": Decimal("1000.00"),
            "charge": Decimal("50.00"),
            "stamp_duty": Decimal("0.00"),
            "previous_balance": Decimal("5000.00"),
            "new_balance": Decimal("4000.00"),  # Wrong! Should be 3950
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": "Test Merchant",
            "message": "Test invalid DEBIT transaction dispute",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "New balance calculation doesn't match" in str(response.data)
        assert "DEBIT transaction" in str(response.data)

    def test_credit_transaction_balance_calculation_invalid(self, authenticated_client):
        """Test that invalid CREDIT transaction balance calculation is rejected"""
        api_client, _, _ = authenticated_client

        # CREDIT transaction with incorrect balance calculation (the original failing case)
        dispute_data = {
            "transaction_reference": "0197AC441B2A7C2B8161696ABB259374",
            "vas_service": "VIRTUAL_ACCOUNT",  # CREDIT transaction
            "amount": Decimal("9500.00"),
            "charge": Decimal("0.00"),
            "stamp_duty": Decimal("0.00"),
            "previous_balance": Decimal("9500.00"),
            "new_balance": Decimal("9500.00"),  # Wrong! Should be 19000 for CREDIT
            "transaction_date": "2025-06-26T13:43:54.033508+01:00",
            "merchant_name": "Nill",
            "message": "TESTING DISPUTE - NO WHALA",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "New balance calculation doesn't match" in str(response.data)
        assert "CREDIT transaction" in str(response.data)


@pytest.mark.django_db
class TestDisputePermissions:
    """Test cases for dispute permissions using teams app system"""

    @pytest.fixture
    def api_client(self):
        """Return an API client for testing"""
        return APIClient()

    def test_business_owner_can_create_dispute(self, api_client):
        """Test that business owners can create disputes"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=user)

        api_client.force_authenticate(user=user)

        dispute_data = {
            "transaction_reference": "TXN-OWNER-001",
            "vas_service": "AIRTIME",
            "amount": "1000.00",
            "charge": "50.00",
            "stamp_duty": "0.00",
            "previous_balance": "5000.00",
            "new_balance": "3950.00",
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": business.name,
            "message": "Test dispute by business owner",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_201_CREATED

    def test_operations_team_member_can_create_dispute(self, api_client):
        """Test that Operations team members can create disputes"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=create_test_user(email="<EMAIL>"))

        create_team_member_with_role(
            user, business, "Operations", ["dispute:CREATE", "dispute:READ"]
        )

        api_client.force_authenticate(user=user)

        dispute_data = {
            "transaction_reference": "TXN-OPS-001",
            "vas_service": "AIRTIME",
            "amount": "1000.00",
            "charge": "50.00",
            "stamp_duty": "0.00",
            "previous_balance": "5000.00",
            "new_balance": "3950.00",
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": business.name,
            "message": "Test dispute by operations team member",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_201_CREATED

    def test_customer_support_cannot_create_dispute(self, api_client):
        """Test that Customer Support team members cannot create disputes"""
        user = create_test_user(email="<EMAIL>")
        business = create_business(owner=create_test_user(email="<EMAIL>"))

        create_team_member_with_role(
            user, business, "Customer_Support", ["dispute:READ"]  # Only read permission
        )

        api_client.force_authenticate(user=user)

        dispute_data = {
            "transaction_reference": "TXN-SUPPORT-001",
            "vas_service": "AIRTIME",
            "amount": "1000.00",
            "charge": "50.00",
            "stamp_duty": "0.00",
            "previous_balance": "5000.00",
            "new_balance": "3950.00",
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": business.name,
            "message": "Test dispute by customer support (should fail)",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_unauthenticated_user_cannot_create_dispute(self, api_client):
        """Test that unauthenticated users cannot create disputes"""
        dispute_data = {
            "transaction_reference": "TXN-UNAUTH-001",
            "vas_service": "AIRTIME",
            "amount": "1000.00",
            "charge": "50.00",
            "stamp_duty": "0.00",
            "previous_balance": "5000.00",
            "new_balance": "3950.00",
            "transaction_date": "2024-01-01T10:00:00Z",
            "merchant_name": "Test Business",
            "message": "Test dispute by unauthenticated user (should fail)",
        }

        url = reverse("dispute:dispute-list")
        response = api_client.post(url, dispute_data, format="json")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

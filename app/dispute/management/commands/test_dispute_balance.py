"""
Django management command to test dispute balance calculations.
Usage: python manage.py test_dispute_balance
"""

from dispute.serializers import DisputeCreateSerializer
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Test dispute balance calculations for CREDIT and DEBIT transactions"

    def handle(self, *args, **options):
        """Run balance calculation tests"""
        self.stdout.write(self.style.SUCCESS("=" * 60))
        self.stdout.write(self.style.SUCCESS("DISPUTE BALANCE CALCULATION TEST"))
        self.stdout.write(self.style.SUCCESS("=" * 60))

        tests = [
            self.test_credit_transaction,
            self.test_debit_transaction,
            self.test_invalid_credit_transaction,
            self.test_invalid_debit_transaction,
        ]

        passed = 0
        total = len(tests)

        for test in tests:
            if test():
                passed += 1

        self.stdout.write(self.style.SUCCESS("\n" + "=" * 60))
        self.stdout.write(
            self.style.SUCCESS(f"TEST RESULTS: {passed}/{total} tests passed")
        )

        if passed == total:
            self.stdout.write(
                self.style.SUCCESS(
                    "🎉 ALL TESTS PASSED! Balance calculation is working correctly."
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR(
                    "❌ Some tests failed. Please check the implementation."
                )
            )

    def test_credit_transaction(self):
        """Test CREDIT transaction balance calculation"""
        self.stdout.write("Testing CREDIT transaction balance calculation...")

        dispute_data = {
            "merchant_name": "Test Merchant",
            "transaction_reference": "CREDIT-TXN-TEST",
            "amount": "9500.00",
            "charge": "0.00",
            "message": "Test CREDIT transaction dispute",
            "new_balance": "19000.00",  # 9500 + 9500 + 0 + 0 = 19000
            "previous_balance": "9500.00",
            "stamp_duty": "0",
            "transaction_date": "2025-06-26T13:43:54.033508+01:00",
            "vas_service": "VIRTUAL_ACCOUNT",
        }

        try:
            serializer = DisputeCreateSerializer(data=dispute_data)
            if serializer.is_valid():
                self.stdout.write(
                    self.style.SUCCESS("✅ CREDIT transaction validation PASSED")
                )
                return True
            else:
                self.stdout.write(
                    self.style.ERROR("❌ CREDIT transaction validation FAILED")
                )
                self.stdout.write(f"Errors: {serializer.errors}")
                return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ CREDIT transaction validation ERROR: {e}")
            )
            return False

    def test_debit_transaction(self):
        """Test DEBIT transaction balance calculation"""
        self.stdout.write("\nTesting DEBIT transaction balance calculation...")

        dispute_data = {
            "merchant_name": "Test Merchant",
            "transaction_reference": "DEBIT-TXN-TEST",
            "amount": "1000.00",
            "charge": "50.00",
            "message": "Test DEBIT transaction dispute",
            "new_balance": "3950.00",  # 5000 - 1000 - 50 - 0 = 3950
            "previous_balance": "5000.00",
            "stamp_duty": "0.00",
            "transaction_date": "2024-01-01T10:00:00Z",
            "vas_service": "AIRTIME",
        }

        try:
            serializer = DisputeCreateSerializer(data=dispute_data)
            if serializer.is_valid():
                self.stdout.write(
                    self.style.SUCCESS("✅ DEBIT transaction validation PASSED")
                )
                return True
            else:
                self.stdout.write(
                    self.style.ERROR("❌ DEBIT transaction validation FAILED")
                )
                self.stdout.write(f"Errors: {serializer.errors}")
                return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ DEBIT transaction validation ERROR: {e}")
            )
            return False

    def test_invalid_credit_transaction(self):
        """Test that invalid CREDIT transaction is rejected"""
        self.stdout.write("\nTesting invalid CREDIT transaction rejection...")

        dispute_data = {
            "merchant_name": "Test Merchant",
            "transaction_reference": "INVALID-CREDIT-TEST",
            "amount": "9500.00",
            "charge": "0.00",
            "message": "Test invalid CREDIT transaction dispute",
            "new_balance": "9500.00",  # Wrong! Should be 19000 for CREDIT
            "previous_balance": "9500.00",
            "stamp_duty": "0.00",
            "transaction_date": "2025-06-26T13:43:54.033508+01:00",
            "vas_service": "VIRTUAL_ACCOUNT",
        }

        try:
            serializer = DisputeCreateSerializer(data=dispute_data)
            if not serializer.is_valid():
                self.stdout.write(
                    self.style.SUCCESS(
                        "✅ Invalid CREDIT transaction correctly REJECTED"
                    )
                )
                return True
            else:
                self.stdout.write(
                    self.style.ERROR(
                        "❌ Invalid CREDIT transaction was incorrectly ACCEPTED"
                    )
                )
                return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Invalid CREDIT transaction test ERROR: {e}")
            )
            return False

    def test_invalid_debit_transaction(self):
        """Test that invalid DEBIT transaction is rejected"""
        self.stdout.write("\nTesting invalid DEBIT transaction rejection...")

        dispute_data = {
            "merchant_name": "Test Merchant",
            "transaction_reference": "INVALID-DEBIT-TEST",
            "amount": "1000.00",
            "charge": "50.00",
            "message": "Test invalid DEBIT transaction dispute",
            "new_balance": "4000.00",  # Wrong! Should be 3950
            "previous_balance": "5000.00",
            "stamp_duty": "0.00",
            "transaction_date": "2024-01-01T10:00:00Z",
            "vas_service": "AIRTIME",
        }

        try:
            serializer = DisputeCreateSerializer(data=dispute_data)
            if not serializer.is_valid():
                self.stdout.write(
                    self.style.SUCCESS(
                        "✅ Invalid DEBIT transaction correctly REJECTED"
                    )
                )
                return True
            else:
                self.stdout.write(
                    self.style.ERROR(
                        "❌ Invalid DEBIT transaction was incorrectly ACCEPTED"
                    )
                )
                return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Invalid DEBIT transaction test ERROR: {e}")
            )
            return False

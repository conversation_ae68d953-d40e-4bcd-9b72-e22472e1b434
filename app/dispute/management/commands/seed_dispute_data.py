from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "DEPRECATED: This command needs to be updated to use teams app models."

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.ERROR(
                "⚠️  DEPRECATED COMMAND ⚠️\n"
                "This command is deprecated and needs to be updated to use the teams app models.\n"
                "The dispute app now uses the teams app for team member management.\n"
                "Please update this command or use alternative seeding methods.\n"
            )
        )

from datetime import datetime, time

import django_filters
from django.utils import timezone

from .enums import DisputeStatus, DisputeType
from .models import Dispute


class DisputeFilter(django_filters.FilterSet):
    """Filter for disputes."""

    status = django_filters.ChoiceFilter(
        choices=DisputeStatus.choices(), help_text="Filter by dispute status"
    )
    dispute_type = django_filters.ChoiceFilter(
        choices=DisputeType.choices(), help_text="Filter by dispute type"
    )

    vas_service = django_filters.CharFilter(
        lookup_expr="iexact", help_text="Filter by VAS service type"
    )

    transaction_reference = django_filters.CharFilter(
        lookup_expr="icontains",
        help_text="Filter by transaction reference (partial match)",
    )

    merchant_name = django_filters.CharFilter(
        lookup_expr="icontains", help_text="Filter by merchant name (partial match)"
    )

    amount_min = django_filters.NumberFilter(
        field_name="amount", lookup_expr="gte", help_text="Filter by minimum amount"
    )

    amount_max = django_filters.NumberFilter(
        field_name="amount", lookup_expr="lte", help_text="Filter by maximum amount"
    )

    created_at_after = django_filters.DateTimeFilter(
        field_name="created_at",
        lookup_expr="gte",
        help_text="Filter disputes created after this date",
    )

    created_at_before = django_filters.DateTimeFilter(
        field_name="created_at",
        lookup_expr="lte",
        help_text="Filter disputes created before this date",
    )

    transaction_date_after = django_filters.DateTimeFilter(
        field_name="transaction_date",
        lookup_expr="gte",
        help_text="Filter by transaction date after",
    )

    transaction_date_before = django_filters.DateTimeFilter(
        field_name="transaction_date",
        lookup_expr="lte",
        help_text="Filter by transaction date before",
    )

    start = django_filters.DateFilter(
        field_name="created_at",
        lookup_expr="gte",
        help_text="Start date for filtering disputes (YYYY-MM-DD) - includes entire day",
    )

    end = django_filters.DateFilter(
        field_name="created_at",
        method="filter_end_date",
        help_text="End date for filtering disputes (YYYY-MM-DD) - includes entire day",
    )

    class Meta:
        model = Dispute
        fields = [
            "status",
            "dispute_type",
            "vas_service",
            "transaction_reference",
            "merchant_name",
            "amount_min",
            "amount_max",
            "created_at_after",
            "created_at_before",
            "transaction_date_after",
            "transaction_date_before",
            "start",
            "end",
        ]

    def filter_end_date(self, queryset, name, value):
        """Filter end date with proper end-of-day handling for inclusive filtering"""
        if value:
            # Convert date to end of day for inclusive filtering
            end_of_day = datetime.combine(value, time.max)
            end_of_day = (
                timezone.make_aware(end_of_day)
                if timezone.is_naive(end_of_day)
                else end_of_day
            )
            return queryset.filter(created_at__lte=end_of_day)
        return queryset

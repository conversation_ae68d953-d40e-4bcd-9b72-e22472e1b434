"""
Admin permissions for disputes using teams app permission system.

This module provides admin-specific permission classes for dispute operations
that integrate with the teams app's Role and Permission models.
"""

from rest_framework import permissions
from teams.permissions import (
    HasAdminPermission,
    check_user_permission,
    get_user_admin_context,
)

# Re-export teams app permission classes for backward compatibility
__all__ = [
    "HasAdminPermission",
    "get_user_admin_context",
    "check_user_permission",
    "IsAdminTeamMember",
    "CanReadAllDisputes",
    "CanUpdateDisputes",
    "CanAssignDisputes",
    "check_admin_dispute_permission",
    "can_user_be_assigned_disputes",
]


class IsAdminTeamMember(permissions.BasePermission):
    """Allows access only to admin team members."""

    message = "Only admin team members are authorized to perform this action."

    def has_permission(self, request, view):
        admin_profile, _ = get_user_admin_context(request.user)
        return admin_profile is not None


class CanReadAllDisputes(HasAdminPermission):
    """Permission to read all disputes across businesses."""

    def __init__(self):
        super().__init__("dispute:READ")


class CanUpdateDisputes(HasAdminPermission):
    """Permission to update disputes."""

    def __init__(self):
        super().__init__("dispute:UPDATE")


class CanAssignDisputes(HasAdminPermission):
    """Permission to assign disputes to team members."""

    def __init__(self):
        super().__init__("dispute:UPDATE")  # Assignment requires update permission


def check_admin_dispute_permission(user, permission_codename):
    """
    Check if admin user has specific dispute permission.

    Args:
        user: User instance
        permission_codename: Permission codename (e.g., "dispute:UPDATE")

    Returns:
        bool: True if user has the permission, False otherwise
    """
    return check_user_permission(user, permission_codename)


def can_user_be_assigned_disputes(user):
    """Check if user can be assigned disputes (has update permission)."""
    return check_admin_dispute_permission(user, "dispute:UPDATE")

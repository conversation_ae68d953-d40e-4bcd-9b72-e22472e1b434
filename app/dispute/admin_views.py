from common.pagination import LargeDatasetKeySetPagination
from common.permissions import IsAdmin
from django.db.models import Count, Q
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from teams.models import AdminProfile

from .admin_permissions import (
    CanAssignDisputes,
    CanReadAllDisputes,
    CanUpdateDisputes,
    IsAdminTeamMember,
)
from .admin_serializers import (
    AdminDisputeDetailSerializer,
    AdminDisputeListSerializer,
    AdminDisputeStatsSerializer,
    DisputeAssignmentSerializer,
    DisputeResponseCreateSerializer,
    DisputeResponseSerializer,
)
from .enums import DisputeStatus
from .filters import DisputeFilter
from .models import Dispute, DisputeAssignment
from .tasks import send_dispute_assignment_email, send_dispute_status_update_email


class AdminDisputeViewSet(viewsets.ModelViewSet):
    """ViewSet for admin dispute management."""

    pagination_class = LargeDatasetKeySetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = DisputeFilter
    search_fields = [
        "id",
        "transaction_reference",
        "merchant_name",
        "message",
        "topic",
        "business__name",
        "assignments__assigned_to__email",
        "assignments__assigned_to__firstname",
        "assignments__assigned_to__lastname",
    ]
    ordering_fields = [
        "created_at",
        "amount",
        "transaction_date",
        "status",
        "resolved_at",
        "updated_at",
    ]
    ordering = ["-created_at"]

    def get_queryset(self):
        """Get all disputes for admin management."""
        queryset = (
            Dispute.objects.all()
            .select_related("business", "created_by", "resolved_by")
            .prefetch_related("assignments", "responses")
        )

        status_filter = self.request.query_params.get("status")
        ordering_param = self.request.query_params.get("ordering")

        if not ordering_param:
            if status_filter == DisputeStatus.RESOLVED.value:

                queryset = queryset.order_by("-resolved_at")
            else:

                queryset = queryset.order_by("-created_at")

        return queryset

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action in ["list"]:
            return AdminDisputeListSerializer
        else:
            return AdminDisputeDetailSerializer

    def get_permissions(self):
        """Return appropriate permissions based on action."""
        if self.action in ["list", "retrieve"]:
            permission_classes = [IsAuthenticated, CanReadAllDisputes]
            permission_classes = [IsAdmin]
        elif self.action in ["assign_dispute", "unassign_dispute"]:
            permission_classes = [IsAuthenticated, CanAssignDisputes]
            permission_classes = [IsAdmin]
        elif self.action in ["add_response", "update_status"]:
            permission_classes = [IsAuthenticated, CanUpdateDisputes]
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAuthenticated, IsAdminTeamMember]
            permission_classes = [IsAdmin]

        return [permission() for permission in permission_classes]

    def list(self, request, *args, **kwargs):
        """List all disputes with simple pagination format."""
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific dispute with full details."""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(
            {
                "success": True,
                "message": "Dispute details retrieved successfully",
                "data": serializer.data,
            }
        )

    @action(detail=True, methods=["post"], url_path="assign")
    def assign_dispute(self, request, pk=None):
        """Assign dispute to a team member."""
        dispute = self.get_object()

        serializer = DisputeAssignmentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        assignment = serializer.save(dispute=dispute, assigned_by=request.user)

        # Send email notification
        send_dispute_assignment_email.delay(assignment.id)

        return Response(
            {
                "success": True,
                "message": "Dispute assigned successfully",
                "data": serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )

    @action(detail=True, methods=["post"], url_path="unassign")
    def unassign_dispute(self, request, pk=None):
        """Unassign dispute from current assignee."""
        dispute = self.get_object()

        # Deactivate current assignments
        assignments_updated = DisputeAssignment.objects.filter(
            dispute=dispute, is_active=True
        ).update(is_active=False)

        if assignments_updated > 0:
            return Response(
                {
                    "success": True,
                    "message": "Dispute unassigned successfully",
                    "data": None,
                }
            )
        else:
            return Response(
                {
                    "success": False,
                    "message": "No active assignment found for this dispute",
                    "data": None,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"], url_path="add-response")
    def add_response(self, request, pk=None):
        """Add a response to a dispute."""
        dispute = self.get_object()

        serializer = DisputeResponseCreateSerializer(
            data=request.data, context={"dispute": dispute, "request": request}
        )
        serializer.is_valid(raise_exception=True)
        response_obj = serializer.save()

        # Send email notification if not internal note
        if not response_obj.is_internal_note:
            send_dispute_status_update_email.delay(str(dispute.id), response_obj.id)

        response_serializer = DisputeResponseSerializer(response_obj)
        return Response(
            {
                "success": True,
                "message": "Response added successfully",
                "data": response_serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )

    @action(detail=True, methods=["get"], url_path="responses")
    def get_responses(self, request, pk=None):
        """Get all responses for a dispute."""
        dispute = self.get_object()
        responses = dispute.responses.all().order_by("created_at")

        serializer = DisputeResponseSerializer(responses, many=True)
        return Response(
            {
                "success": True,
                "message": "Dispute responses retrieved successfully",
                "data": serializer.data,
            }
        )

    @action(detail=False, methods=["get"], url_path="statistics")
    def statistics(self, request):
        """Get admin dispute statistics."""
        # Get dispute counts by status
        stats = Dispute.objects.aggregate(
            total_disputes=Count("id"),
            pending_disputes=Count("id", filter=Q(status=DisputeStatus.PENDING.value)),
            in_review_disputes=Count(
                "id", filter=Q(status=DisputeStatus.IN_REVIEW.value)
            ),
            resolved_disputes=Count(
                "id", filter=Q(status=DisputeStatus.RESOLVED.value)
            ),
            assigned_disputes=Count("id", filter=Q(assignments__is_active=True)),
            unassigned_disputes=Count("id", filter=~Q(assignments__is_active=True)),
        )

        serializer = AdminDisputeStatsSerializer(stats)
        return Response(
            {
                "success": True,
                "message": "Admin dispute statistics retrieved successfully",
                "data": serializer.data,
            }
        )

    @action(detail=False, methods=["get"], url_path="assignable-members")
    def assignable_members(self, request):
        """Get list of team members who can be assigned disputes."""
        from .admin_permissions import can_user_be_assigned_disputes

        # Get all active admin team members
        team_members = AdminProfile.objects.filter(status="Active").select_related(
            "user", "role"
        )

        # Filter those who can be assigned disputes
        assignable_members = []
        for member in team_members:
            if can_user_be_assigned_disputes(member.user):
                assignable_members.append(
                    {
                        "id": member.id,
                        "email": member.user.email,
                        "name": member.user.fullname,
                        "role": member.role.name,
                    }
                )

        return Response(
            {
                "success": True,
                "message": "Assignable team members retrieved successfully",
                "data": assignable_members,
            }
        )

    @action(detail=False, methods=["get"], url_path="my-assignments")
    def my_assignments(self, request):
        """Get disputes assigned to the current user."""
        # Get disputes assigned to current user
        assigned_disputes = (
            Dispute.objects.filter(
                assignments__assigned_to=request.user, assignments__is_active=True
            )
            .select_related("business", "created_by")
            .prefetch_related("responses")
        )

        serializer = AdminDisputeListSerializer(assigned_disputes, many=True)
        return Response(
            {
                "success": True,
                "message": "Your assigned disputes retrieved successfully",
                "data": serializer.data,
            }
        )

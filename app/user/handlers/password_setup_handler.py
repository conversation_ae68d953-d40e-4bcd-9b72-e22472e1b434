from datetime import datetime

from django.utils.timezone import make_aware
from teams.enums import SystemBaseUserRole, TeamMemberStatus
from teams.models import AdminProfile, TeamMember
from user.models import User


class UserOnboardingHandler:
    def __init__(self, user: User):
        self.user = user

    def complete_password_setup(self, raw_password: str):
        """
        Sets the user’s password, marks them verified if needed,
        and flips the relevant TeamMember/AdminProfile to Active.
        """
        current_timestamp = make_aware(datetime.now())
        self.user.set_password(raw_password)
        if not self.user.verified:
            self.user.verified = True
            self.user.email_verified_at = current_timestamp
        self.user.save()
        self.user.save_last_login()

        role = self.user.role
        if role == SystemBaseUserRole.TeamMember.value:
            profile_qs = TeamMember.objects.filter(user=self.user)
        elif role == SystemBaseUserRole.Admin.value:
            profile_qs = AdminProfile.objects.filter(user=self.user)
        else:
            return  # BusinessOwner: nothing more to do

        profile = profile_qs.first()
        if profile:
            profile.status = TeamMemberStatus.Active.value
            profile.joined_at = current_timestamp
            profile.save()

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

User = get_user_model()


class Command(BaseCommand):
    help = "Make a user an admin by email address"

    def add_arguments(self, parser):
        parser.add_argument(
            "email", type=str, help="Email address of the user to make admin"
        )
        parser.add_argument(
            "--superuser",
            action="store_true",
            help="Also make the user a superuser (Django admin access)",
        )

    def handle(self, *args, **options):
        email = options["email"]
        make_superuser = options["superuser"]

        try:
            user = User.objects.get(email=email)

            # Update user role to Admin
            old_role = user.role
            user.role = "Admin"

            # Make staff if making superuser
            if make_superuser:
                user.is_staff = True
                user.is_superuser = True

            user.save()

            self.stdout.write(self.style.SUCCESS(f"Successfully updated user {email}:"))
            self.stdout.write(f'  - Role changed from "{old_role}" to "Admin"')

            if make_superuser:
                self.stdout.write("  - Made superuser (Django admin access)")
                self.stdout.write("  - Made staff user")

            self.stdout.write("\nUser can now access admin Wema endpoints:")
            self.stdout.write("  - POST /api/v1/admin/wema/name-enquiry/")
            self.stdout.write("  - POST /api/v1/admin/wema/funds-transfer/")
            self.stdout.write("  - POST /api/v1/admin/wema/account-statements/")
            self.stdout.write("  - GET /api/v1/admin/wema/payout-history/")

        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"User with email {email} does not exist")
            )

            # Offer to create the user
            create_user = input("Would you like to create this user? (y/n): ")
            if create_user.lower() == "y":
                firstname = input("Enter first name: ")
                lastname = input("Enter last name: ")

                user = User.objects.create_user(
                    email=email,
                    firstname=firstname,
                    lastname=lastname,
                    role="Admin",
                    verified=True,
                    is_staff=make_superuser,
                    is_superuser=make_superuser,
                )

                self.stdout.write(
                    self.style.SUCCESS(f"Successfully created admin user {email}")
                )

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error updating user: {str(e)}"))

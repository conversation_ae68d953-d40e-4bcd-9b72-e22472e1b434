import os

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.environ.get("POSTGRES_DB"),
        "USER": os.environ.get("POSTGRES_USER"),
        "PASSWORD": os.environ.get("POSTGRES_PASSWORD"),
        "HOST": os.environ.get("POSTGRES_HOST"),
        "PORT": os.environ.get("POSTGRES_PORT"),
    },
    "audit_db": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.environ.get("AUDIT_POSTGRES_DB"),
        "USER": os.environ.get("POSTGRES_USER"),
        "PASSWORD": os.environ.get("POSTGRES_PASSWORD"),
        "HOST": os.environ.get("AUDIT_POSTGRES_HOST"),
        "PORT": os.environ.get("POSTGRES_PORT"),
        "CONN_MAX_AGE": 3600,
    },
    "sagecloud": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.environ.get("SAGECLOUD_POSTGRES_DB"),
        "USER": os.environ.get("SAGECLOUD_POSTGRES_USER"),
        "PASSWORD": os.environ.get("SAGECLOUD_POSTGRES_PASSWORD"),
        "HOST": os.environ.get("SAGECLOUD_POSTGRES_HOST"),
        "PORT": os.environ.get("SAGECLOUD_POSTGRES_PORT"),
        "CONN_MAX_AGE": 3600,
    },
}

DATABASE_ROUTERS = [
    "core.settings.routers.AuditLogRouter",
    "core.settings.routers.DefaultRouter",
    "core.settings.routers.SagecloudRouter",
]

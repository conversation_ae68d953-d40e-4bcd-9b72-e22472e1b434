"""core URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from common.views import health_check, readiness_check
from django.contrib import admin
from django.urls import include, path
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/schema/", SpectacularAPIView.as_view(), name="schema"),
    path("api/v1/doc/", SpectacularSwaggerView.as_view(url_name="schema"), name="doc"),
    path(
        "api/v1/redoc/", SpectacularRedocView.as_view(url_name="schema"), name="redoc"
    ),
    path("api/v1/api-auth/", include("rest_framework.urls")),
    path("__debug__/", include("debug_toolbar.urls")),
    path("api/readiness/", readiness_check, name="readiness_check"),
    path("api/healthz/", health_check, name="health_check"),
    path("api/v1/auth/", include("user.v1.urls.auth")),
    path("api/v1/users/", include("user.v1.urls.users")),
    path("api/v1/transaction/", include("transaction.v1.urls")),
    path("api/v1/business/", include("business.v1.urls")),
    path("api/v1/console/", include("console.v1.urls")),
    path("api/v1/team/", include("teams.v1.urls")),
    path("api/v1/wallet/", include("wallet.v1.urls")),
    path("api/v1/virtual-account/", include("virtual_account.v1.urls")),
    path("api/v1/audit/", include("audit.urls")),
    path("api/v1/fee-settings/", include("fees.v1.urls")),
    path("api/v1/commissions/", include("commission.v1.urls")),
    path("api/v1/", include("exports.urls")),
    path("api/v1/disputes/", include("dispute.urls")),
    path("api/v1/admin/disputes/", include("dispute.admin_urls")),
    path("api/v1/admin/transactions/", include("transaction.admin_urls.lien_urls")),
    path("api/v1/admin/wema/", include("vas.admin.wema.urls")),
    path("api/v1/config/", include("config.v1.urls")),
    path("api/v2/", include("vas.v2.urls")),
    path("api/v3/", include("vas.v3.urls")),
]
